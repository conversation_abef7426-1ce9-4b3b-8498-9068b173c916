#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多线程环境下的WebSocket方法
"""

import sys
import os
import threading
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def test_single_asin(analyzer, asin, thread_id):
    """测试单个ASIN"""
    print(f"🧵 线程{thread_id}: 开始处理 {asin}")
    
    try:
        result = analyzer.reverse_engineer_keepa(asin)
        
        if result:
            print(f"✅ 线程{thread_id}: {asin} 成功 - 最高${result.get('historical_highest_price', 0):.2f} 最低${result.get('historical_lowest_price', 0):.2f} 来源:{result.get('source', 'unknown')}")
            return True
        else:
            print(f"❌ 线程{thread_id}: {asin} 失败")
            return False
            
    except Exception as e:
        print(f"❌ 线程{thread_id}: {asin} 异常 - {str(e)}")
        return False

def test_multithread():
    """测试多线程处理"""
    print("🔍 测试多线程WebSocket处理...")
    
    # 创建分析器实例
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    
    # 测试ASIN列表
    test_asins = [
        "B07QNYTRG2",  # 已知有效的ASIN
        "B08N5WRWNW",  # 另一个测试ASIN
        "B0BSHF7LLL",  # 第三个测试ASIN
    ]
    
    print(f"📊 使用3个线程处理 {len(test_asins)} 个ASIN")
    
    # 创建线程
    threads = []
    results = {}
    
    def worker(asin, thread_id):
        results[asin] = test_single_asin(analyzer, asin, thread_id)
    
    start_time = time.time()
    
    # 启动线程
    for i, asin in enumerate(test_asins):
        thread = threading.Thread(target=worker, args=(asin, i+1))
        threads.append(thread)
        thread.start()
        
        # 添加小延迟避免同时启动
        time.sleep(1)
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    
    # 统计结果
    success_count = sum(1 for success in results.values() if success)
    
    print(f"\n📊 多线程测试结果:")
    print(f"   总耗时: {end_time - start_time:.1f} 秒")
    print(f"   成功数量: {success_count}/{len(test_asins)}")
    print(f"   成功率: {success_count/len(test_asins)*100:.1f}%")
    
    for asin, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {asin}: {status}")
    
    return success_count > 0

def main():
    """主函数"""
    print("🚀 开始多线程WebSocket测试...")
    print("=" * 60)
    
    success = test_multithread()
    
    print("\n" + "=" * 60)
    print(f"📋 测试结果: {'✅ 通过' if success else '❌ 失败'}")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
