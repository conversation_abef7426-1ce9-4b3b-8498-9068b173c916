#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试Keepa API端点的脚本
"""

import sys
import os
from 历史价格8 import KeepaHistoricalPriceChecker

def test_keepa_api_endpoints():
    """测试Keepa API端点"""
    print("🔍 Keepa API端点测试工具")
    print("=" * 50)
    
    try:
        # 创建检查器
        checker = KeepaHistoricalPriceChecker(marketplace="US", verbose=True)
        checker.load_proxies()
        
        asin = "B07QNYTRG2"
        print(f"🎯 测试ASIN: {asin}")
        
        # 获取API端点列表
        print(f"\n📡 获取API端点列表...")
        api_endpoints = checker.analyze_keepa_js()
        
        print(f"\n🌐 测试前10个最有希望的API端点:")
        print("-" * 50)
        
        success_count = 0
        
        for i, endpoint_template in enumerate(api_endpoints[:10], 1):
            try:
                endpoint = endpoint_template.format(asin=asin)
                print(f"\n{i}. 测试: {endpoint}")
                
                # 尝试API调用
                result = checker.try_keepa_api(endpoint_template, asin)
                
                if result:
                    print(f"   ✅ 成功! 获取到价格数据:")
                    print(f"      最高价: {result.get('historical_highest_price')}")
                    print(f"      最低价: {result.get('historical_lowest_price')}")
                    success_count += 1
                    
                    # 如果找到有效数据，可以停止测试
                    if result.get('historical_highest_price') or result.get('historical_lowest_price'):
                        print(f"   🎉 找到有效的API端点!")
                        break
                else:
                    print(f"   ❌ 失败")
                    
            except Exception as e:
                print(f"   ❌ 异常: {str(e)}")
        
        print(f"\n📊 测试结果:")
        print(f"   测试端点数: {min(10, len(api_endpoints))}")
        print(f"   成功端点数: {success_count}")
        print(f"   成功率: {(success_count / min(10, len(api_endpoints))) * 100:.1f}%")
        
        # 如果没有成功的API，尝试WebSocket
        if success_count == 0:
            print(f"\n🔌 API端点都失败了，尝试WebSocket方法...")
            main_page_data = checker.get_keepa_main_page(asin)
            if main_page_data:
                ws_result = checker.simulate_keepa_websocket(asin, main_page_data)
                if ws_result:
                    print(f"✅ WebSocket成功!")
                    print(f"   最高价: {ws_result.get('historical_highest_price')}")
                    print(f"   最低价: {ws_result.get('historical_lowest_price')}")
                else:
                    print(f"❌ WebSocket也失败了")
            else:
                print(f"❌ 无法获取主页面数据")
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

def analyze_keepa_structure():
    """分析Keepa网站结构"""
    print(f"\n🔬 分析Keepa网站结构")
    print("=" * 40)
    
    try:
        checker = KeepaHistoricalPriceChecker(marketplace="US", verbose=True)
        checker.load_proxies()
        
        asin = "B07QNYTRG2"
        
        # 获取主页面
        print(f"📄 获取主页面...")
        main_page_data = checker.get_keepa_main_page(asin)
        
        if main_page_data:
            html = main_page_data['html']
            
            # 分析JavaScript代码
            print(f"\n🔍 分析JavaScript代码...")
            
            # 查找关键变量和函数
            import re
            
            patterns = {
                'WebSocket连接': r'new WebSocket\(["\']([^"\']+)["\']',
                '服务器配置': r'window\.server\s*=\s*\[([^\]]+)\]',
                '版本信息': r'version=([0-9.]+)',
                '应用配置': r'app=([^&"\']+)',
                '数据处理函数': r'function\s+(\w*[Pp]roduct\w*)\s*\(',
                'API调用': r'fetch\s*\(["\']([^"\']*api[^"\']*)["\']',
                '数据解析': r'JSON\.parse\s*\(',
                '价格相关': r'["\']([^"\']*price[^"\']*)["\']',
            }
            
            for pattern_name, pattern in patterns.items():
                matches = re.findall(pattern, html, re.IGNORECASE)
                if matches:
                    print(f"   {pattern_name}: {matches[:3]}")  # 只显示前3个匹配
                else:
                    print(f"   {pattern_name}: 未找到")
            
            # 查找可能的数据端点
            print(f"\n🌐 查找数据端点...")
            endpoint_patterns = [
                r'["\']https://[^"\']*keepa[^"\']*api[^"\']*["\']',
                r'["\']https://[^"\']*api[^"\']*keepa[^"\']*["\']',
                r'["\']https://[^"\']*push\.keepa[^"\']*["\']',
                r'["\']https://[^"\']*graph\.keepa[^"\']*["\']',
            ]
            
            all_endpoints = set()
            for pattern in endpoint_patterns:
                matches = re.findall(pattern, html, re.IGNORECASE)
                for match in matches:
                    clean_match = match.strip('"\'')
                    if clean_match:
                        all_endpoints.add(clean_match)
            
            if all_endpoints:
                print(f"   发现 {len(all_endpoints)} 个端点:")
                for endpoint in list(all_endpoints)[:5]:
                    print(f"      {endpoint}")
            else:
                print(f"   未发现明显的API端点")
        
        else:
            print(f"❌ 无法获取主页面")
            
    except Exception as e:
        print(f"❌ 结构分析异常: {str(e)}")

def test_direct_websocket():
    """直接测试WebSocket连接"""
    print(f"\n🔌 直接测试WebSocket连接")
    print("=" * 40)
    
    try:
        import websocket
        import json
        import threading
        import time
        
        # WebSocket服务器信息
        ws_server = "push.keepa.com"
        ws_url = f"wss://{ws_server}/apps/cloud/?app=keepaWebsite&version=3.0"
        
        print(f"🔗 连接到: {ws_url}")
        
        messages_received = []
        
        def on_message(ws, message):
            print(f"📨 收到消息: {str(message)[:100]}...")
            messages_received.append(message)
        
        def on_error(ws, error):
            print(f"❌ WebSocket错误: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print(f"🔌 连接关闭: {close_status_code}")
        
        def on_open(ws):
            print(f"✅ WebSocket连接已建立")
            
            # 发送测试消息
            test_messages = [
                {"type": "ping"},
                {"cmd": "product", "domain": 1, "asin": "B07QNYTRG2"},
                {"action": "getProduct", "marketplace": "1", "asin": "B07QNYTRG2"}
            ]
            
            for msg in test_messages:
                try:
                    ws.send(json.dumps(msg))
                    print(f"📤 发送: {msg}")
                    time.sleep(1)
                except Exception as e:
                    print(f"❌ 发送失败: {str(e)}")
        
        # 创建WebSocket连接
        ws = websocket.WebSocketApp(ws_url,
                                  on_open=on_open,
                                  on_message=on_message,
                                  on_error=on_error,
                                  on_close=on_close)
        
        # 在单独线程中运行
        ws_thread = threading.Thread(target=ws.run_forever)
        ws_thread.daemon = True
        ws_thread.start()
        
        # 等待10秒
        time.sleep(10)
        ws.close()
        
        print(f"📊 收到 {len(messages_received)} 条消息")
        
    except ImportError:
        print(f"❌ 需要安装websocket-client: pip install websocket-client")
    except Exception as e:
        print(f"❌ WebSocket测试异常: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始Keepa API深度测试")
    
    # 测试API端点
    test_keepa_api_endpoints()
    
    # 分析网站结构
    analyze_keepa_structure()
    
    # 测试WebSocket
    test_direct_websocket()
    
    print(f"\n🌍 深度测试完成!")
