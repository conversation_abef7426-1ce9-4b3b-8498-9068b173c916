#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试之前失败的ASIN
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def test_failed_asins():
    """测试之前失败的ASIN"""
    print("🔍 测试之前失败的ASIN...")
    
    # 创建分析器实例，不启用详细输出以减少噪音
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    
    # 使用用户输出中失败的ASIN
    failed_asins = [
        "B002YESWOQ",
        "B000NFC17M", 
        "B07B2S3SPY",
        "B07ZNW4TLG",
        "B003UWYEZ2"
    ]
    
    success_count = 0
    total_count = len(failed_asins)
    
    for asin in failed_asins:
        print(f"\n📱 测试ASIN: {asin}")
        print("-" * 40)
        
        try:
            # 使用主文件中的方法
            result = analyzer.get_keepa_info_with_browser(asin, max_retries=1)
            
            if result and (result.get('historical_highest_price') is not None or 
                          result.get('historical_lowest_price') is not None):
                print(f"✅ 成功提取价格信息:")
                print(f"   最高价: ${result.get('historical_highest_price', 0):.2f}")
                print(f"   最低价: ${result.get('historical_lowest_price', 0):.2f}")
                print(f"   断货状态: {result.get('out_of_stock_within_month', '未知')}")
                success_count += 1
            else:
                print(f"❌ 未能提取到有效价格信息")
                if result:
                    print(f"   返回结果: {result}")
                
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📋 测试结果总结: {success_count}/{total_count} 成功")
    
    if success_count > 0:
        print(f"🎉 修复有效！{success_count} 个ASIN现在能正常工作")
        return True
    else:
        print("⚠️  所有ASIN仍然失败，需要进一步调试")
        return False

def test_single_asin_detailed():
    """详细测试单个ASIN"""
    print("\n🔍 详细测试单个ASIN...")
    
    # 创建分析器实例，启用详细输出
    analyzer = KeepaHistoricalPriceChecker(verbose=True)
    
    test_asin = "B002YESWOQ"  # 选择一个失败的ASIN进行详细测试
    
    print(f"📱 详细测试ASIN: {test_asin}")
    print("=" * 50)
    
    try:
        result = analyzer.get_keepa_info_with_browser(test_asin, max_retries=1)
        
        if result:
            print("✅ 详细测试结果:")
            for key, value in result.items():
                print(f"   {key}: {value}")
            return True
        else:
            print("❌ 详细测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 详细测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始测试修复后的失败ASIN...")
    print("=" * 60)
    
    # 测试多个失败的ASIN
    batch_result = test_failed_asins()
    
    # 详细测试单个ASIN
    detailed_result = test_single_asin_detailed()
    
    print("\n" + "=" * 60)
    print("📋 最终测试结果:")
    print(f"   批量测试: {'✅ 有改善' if batch_result else '❌ 仍然失败'}")
    print(f"   详细测试: {'✅ 成功' if detailed_result else '❌ 失败'}")
    
    return batch_result or detailed_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
