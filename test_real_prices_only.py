#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试只使用真实价格
验证程序不再显示估算价格，只显示真实的黄金购买框价格
"""

import sys
import time
from 历史价格8 import KeepaHistoricalPriceChecker

def test_real_prices_only():
    """测试只使用真实价格"""
    print("🚀 真实价格测试")
    print("🎯 验证程序只显示真实黄金购买框价格")
    print("❌ 不显示任何估算价格（100多美金的价格）")
    print("=" * 60)
    
    # 创建价格检查器实例
    checker = KeepaHistoricalPriceChecker(verbose=True)
    
    # 测试ASIN列表（包括之前显示估算价格的ASIN）
    test_asins = [
        "B009ONDIHG",  # 之前成功显示黄金购买框价格 $29.99
        "B0BLV9CPYL",  # 之前显示估算价格 $157.63 - 现在应该只显示真实价格或跳过
        "B06XG2DGYV",  # 之前显示估算价格 $179.42 - 现在应该只显示真实价格或跳过
        "B00QU3TE6M",  # 之前成功显示黄金购买框价格 $30.00
    ]
    
    print(f"\n🔍 测试 {len(test_asins)} 个ASIN")
    print("💡 期望结果:")
    print("   ✅ 成功: 显示 '🏆黄金购买框最高价' + 真实价格")
    print("   ❌ 失败: 显示 '无法获取真实黄金购买框数据'")
    print("   🚫 不应该: 显示 '历史最高价' 或 100多美金的价格")
    print("-" * 40)
    
    success_count = 0
    failed_count = 0
    estimated_price_count = 0
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n[{i}/{len(test_asins)}] 测试ASIN: {asin}")
        print("-" * 30)
        
        start_time = time.time()
        
        # 调用主程序的处理方法
        result = checker.get_keepa_info_with_browser(asin, max_retries=1)
        
        elapsed_time = time.time() - start_time
        
        if result and result.get('historical_highest_price'):
            highest = result.get('historical_highest_price', 0)
            
            if highest > 100:
                print(f"⚠️ 警告: 仍然显示估算价格 ${highest:.2f}")
                estimated_price_count += 1
            else:
                print(f"✅ 成功: 真实价格 ${highest:.2f} (耗时: {elapsed_time:.1f}s)")
                success_count += 1
        else:
            print(f"❌ 跳过: 无法获取真实数据 (耗时: {elapsed_time:.1f}s)")
            failed_count += 1
        
        # 避免请求过快
        if i < len(test_asins):
            print("⏳ 等待 3 秒...")
            time.sleep(3)
    
    # 分析结果
    print(f"\n" + "=" * 60)
    print("📊 测试结果统计")
    print("-" * 60)
    
    total = len(test_asins)
    print(f"✅ 成功获取真实价格: {success_count}/{total} ({success_count/total*100:.1f}%)")
    print(f"❌ 无法获取真实数据: {failed_count}/{total} ({failed_count/total*100:.1f}%)")
    print(f"⚠️ 仍显示估算价格: {estimated_price_count}/{total} ({estimated_price_count/total*100:.1f}%)")
    
    if estimated_price_count == 0:
        print(f"\n🎉 完美！程序不再显示任何估算价格！")
        print(f"✅ 所有价格都是真实的黄金购买框价格")
    else:
        print(f"\n⚠️ 仍有 {estimated_price_count} 个ASIN显示估算价格")
        print(f"💡 需要进一步优化代码")
    
    if failed_count > 0:
        print(f"\n💡 {failed_count} 个ASIN无法获取真实数据的可能原因:")
        print(f"   • 网络连接问题")
        print(f"   • Keepa反爬虫检测")
        print(f"   • 产品页面结构变化")
        print(f"   • 代理服务器问题")

def explain_changes():
    """解释修改内容"""
    print(f"\n" + "=" * 60)
    print("🔧 代码修改说明")
    print("-" * 60)
    
    print("✅ 已完成的修改:")
    print("   1. 禁用所有估算价格方法")
    print("   2. 只使用真实浏览器数据")
    print("   3. 移除HTML解析备用方法")
    print("   4. 移除图像分析估算方法")
    print("   5. 只显示黄金购买框价格")
    
    print("\n🎯 现在的逻辑:")
    print("   1. 尝试获取真实浏览器数据")
    print("   2. 检查数据来源是否为 'keepa_browser_real_stats'")
    print("   3. 成功 → 显示黄金购买框价格")
    print("   4. 失败 → 显示 '无法获取真实数据'")
    print("   5. 不使用任何估算方法")
    
    print("\n💡 预期效果:")
    print("   • 不再显示100多美金的估算价格")
    print("   • 只显示真实的几十美金价格")
    print("   • 提供准确的市场价格信息")
    print("   • 避免误导性的高价格")

if __name__ == "__main__":
    try:
        test_real_prices_only()
        explain_changes()
    except KeyboardInterrupt:
        print("\n⏹️ 测试中断")
    except Exception as e:
        print(f"\n❌ 异常: {str(e)}")
        import traceback
        traceback.print_exc()
