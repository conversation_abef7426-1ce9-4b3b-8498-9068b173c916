#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试WebSocket集成的历史价格脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入修改后的历史价格脚本
try:
    from 历史价格8 import KeepaHistoricalPriceChecker
    print("✅ 成功导入历史价格分析器")
except ImportError as e:
    print(f"❌ 导入失败: {str(e)}")
    sys.exit(1)

def test_websocket_method():
    """测试WebSocket方法"""
    print("🔍 测试WebSocket价格获取方法...")
    
    # 创建分析器实例
    analyzer = KeepaHistoricalPriceChecker(verbose=True)
    
    # 测试ASIN
    test_asin = "B07QNYTRG2"
    
    print(f"📱 测试ASIN: {test_asin}")
    
    try:
        # 调用WebSocket方法
        result = analyzer.reverse_engineer_keepa(test_asin)
        
        if result:
            print(f"✅ 成功获取价格数据:")
            print(f"   ASIN: {result.get('asin', 'Unknown')}")
            print(f"   标题: {result.get('title', 'Unknown')[:50]}...")
            print(f"   历史最高价: ${result.get('historical_highest_price', 0):.2f}")
            print(f"   历史最低价: ${result.get('historical_lowest_price', 0):.2f}")
            if result.get('current_price'):
                print(f"   当前价格: ${result.get('current_price'):.2f}")
            print(f"   数据源: {result.get('source', 'unknown')}")
            
            return True
        else:
            print("❌ 未获取到价格数据")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_asins():
    """测试多个ASIN"""
    print("\n🔍 测试多个ASIN...")
    
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    
    test_asins = [
        "B07QNYTRG2",  # 测试用的ASIN
        "B08N5WRWNW",  # 另一个测试ASIN
    ]
    
    success_count = 0
    
    for asin in test_asins:
        print(f"\n📱 测试ASIN: {asin}")
        
        try:
            result = analyzer.reverse_engineer_keepa(asin)
            
            if result and result.get('historical_highest_price'):
                print(f"✅ 成功: 最高${result.get('historical_highest_price'):.2f} 最低${result.get('historical_lowest_price'):.2f}")
                success_count += 1
            else:
                print(f"❌ 失败: 无价格数据")
                
        except Exception as e:
            print(f"❌ 异常: {str(e)}")
    
    print(f"\n📊 测试结果: {success_count}/{len(test_asins)} 成功")
    return success_count > 0

def main():
    """主测试函数"""
    print("🚀 开始测试WebSocket集成...")
    print("=" * 60)
    
    # 测试单个ASIN
    single_test_success = test_websocket_method()
    
    # 测试多个ASIN
    multiple_test_success = test_multiple_asins()
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print(f"   单个ASIN测试: {'✅ 通过' if single_test_success else '❌ 失败'}")
    print(f"   多个ASIN测试: {'✅ 通过' if multiple_test_success else '❌ 失败'}")
    
    if single_test_success and multiple_test_success:
        print("\n🎉 所有测试通过！WebSocket集成成功！")
        return True
    else:
        print("\n⚠️  部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
