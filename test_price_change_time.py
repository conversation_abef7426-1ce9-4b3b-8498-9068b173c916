#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试价格变动时间解析
验证从HTML span元素中正确提取价格变动时间
"""

import sys
import time
from 历史价格8 import KeepaHistoricalPriceChecker

def test_price_change_time_parsing():
    """测试价格变动时间解析功能"""
    print("🚀 价格变动时间解析测试")
    print("🎯 验证从HTML span元素中提取价格变动时间")
    print("📅 目标格式: (上次更新： 4 分钟 前, 上次价格变动: 2 个月前)")
    print("=" * 60)
    
    # 创建价格检查器实例
    checker = KeepaHistoricalPriceChecker(verbose=True)
    
    # 测试HTML内容样例
    test_html_samples = [
        # 样例1: 用户提供的格式
        '<span style="font-size: 12px; color: #999;">  (上次更新： 4&nbsp;分钟&nbsp;前, 上次价格变动: 2 个月前)</span>',
        
        # 样例2: 其他可能的格式
        '<span>(上次更新： 1 小时前, 上次价格变动: 15 天前)</span>',
        
        # 样例3: 分钟格式
        '<span>上次价格变动: 30 分钟前</span>',
        
        # 样例4: 刚刚格式
        '<span>上次价格变动: 刚刚</span>',
        
        # 样例5: 带&nbsp;的格式
        '<span>上次价格变动: 6&nbsp;个月&nbsp;前</span>',
    ]
    
    print(f"\n🔍 测试 {len(test_html_samples)} 个HTML样例")
    print("-" * 40)
    
    for i, html_sample in enumerate(test_html_samples, 1):
        print(f"\n[{i}/{len(test_html_samples)}] 测试样例:")
        print(f"HTML: {html_sample}")
        print("-" * 30)
        
        # 解析价格变动时间
        result = checker.extract_last_price_change_time(html_sample)
        
        if result:
            print(f"✅ 解析成功!")
            print(f"📅 类型: {result.get('type', 'unknown')}")
            print(f"📅 数值: {result.get('value', 0)}")
            print(f"📅 天数: {result.get('days_ago', 0):.1f} 天前")
            print(f"📅 文本: {result.get('text', 'unknown')}")
            
            # 判断分类
            days_ago = result.get('days_ago', 0)
            if days_ago <= 15:
                category = "15天内"
            else:
                category = "15天外"
            print(f"🏷️ 分类: {category}")
        else:
            print(f"❌ 解析失败")

def test_real_keepa_page():
    """测试真实Keepa页面的价格变动时间解析"""
    print(f"\n" + "=" * 60)
    print("🌐 真实Keepa页面测试")
    print("🎯 从真实页面中提取价格变动时间")
    print("-" * 60)
    
    checker = KeepaHistoricalPriceChecker(verbose=True)
    
    # 测试ASIN
    test_asin = "B07TZDH6G4"
    
    print(f"\n🔍 测试ASIN: {test_asin}")
    print("-" * 30)
    
    start_time = time.time()
    
    # 获取完整数据（包含价格变动时间）
    result = checker.get_keepa_image_api_data_optimized(test_asin)
    
    elapsed_time = time.time() - start_time
    
    if result and result.get('success'):
        print(f"\n✅ 成功获取数据! (耗时: {elapsed_time:.1f}s)")
        
        # 显示价格变动时间信息
        if 'last_price_change' in result and result['last_price_change']:
            change_info = result['last_price_change']
            print(f"\n📅 价格变动时间信息:")
            print(f"   类型: {change_info.get('type', 'unknown')}")
            print(f"   数值: {change_info.get('value', 0)}")
            print(f"   天数: {change_info.get('days_ago', 0):.1f} 天前")
            print(f"   文本: {change_info.get('text', 'unknown')}")
            
            # 显示分类结果
            days_ago = change_info.get('days_ago', 0)
            if days_ago <= 15:
                time_category = "15天内"
            else:
                time_category = "15天外"
            
            print(f"\n🏷️ 时间分类: {time_category}")
            
            # 显示完整分类
            if 'category' in result:
                print(f"🏷️ 完整分类: {result['category']}")
        else:
            print(f"\n⚠️ 未找到价格变动时间信息")
            print(f"   可能原因:")
            print(f"   • 页面格式发生变化")
            print(f"   • 正则表达式需要调整")
            print(f"   • 网络或加载问题")
        
        # 显示其他信息
        print(f"\n📊 其他信息:")
        print(f"   最高价: ${result.get('historical_highest_price', 0):.2f}")
        print(f"   当前价: ${result.get('current_price', 0):.2f}")
        print(f"   来源: {result.get('source', 'unknown')}")
        
    else:
        print(f"\n❌ 获取数据失败 (耗时: {elapsed_time:.1f}s)")

def show_expected_formats():
    """显示预期的HTML格式"""
    print(f"\n" + "=" * 60)
    print("📋 预期HTML格式")
    print("-" * 60)
    
    expected_formats = [
        "上次价格变动: 2 个月前",
        "上次价格变动: 15 天前", 
        "上次价格变动: 4 小时前",
        "上次价格变动: 30 分钟前",
        "上次价格变动: 刚刚",
        "上次价格变动: 2&nbsp;个月&nbsp;前",
        "上次价格变动: 15&nbsp;天&nbsp;前",
    ]
    
    print("🎯 支持的格式:")
    for fmt in expected_formats:
        print(f"   • {fmt}")
    
    print(f"\n💡 分类规则:")
    print(f"   • ≤ 15天: 15天内")
    print(f"   • > 15天: 15天外")
    print(f"   • 未找到: 未知时间")

if __name__ == "__main__":
    try:
        test_price_change_time_parsing()
        test_real_keepa_page()
        show_expected_formats()
    except KeyboardInterrupt:
        print("\n⏹️ 测试中断")
    except Exception as e:
        print(f"\n❌ 异常: {str(e)}")
        import traceback
        traceback.print_exc()
