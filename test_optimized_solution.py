#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的Keepa解决方案
"""

import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from 历史价格8 import KeepaHistorical<PERSON><PERSON><PERSON><PERSON><PERSON>

def test_single_asin(asin, checker):
    """测试单个ASIN"""
    start_time = time.time()
    
    try:
        result = checker.reverse_engineer_keepa(asin)
        end_time = time.time()
        
        if result and (result.get('historical_highest_price') or result.get('historical_lowest_price')):
            return {
                'asin': asin,
                'success': True,
                'highest_price': result.get('historical_highest_price'),
                'lowest_price': result.get('historical_lowest_price'),
                'out_of_stock': result.get('out_of_stock_within_month'),
                'time_taken': end_time - start_time,
                'error': None
            }
        else:
            return {
                'asin': asin,
                'success': False,
                'highest_price': None,
                'lowest_price': None,
                'out_of_stock': None,
                'time_taken': end_time - start_time,
                'error': 'No price data extracted'
            }
            
    except Exception as e:
        end_time = time.time()
        return {
            'asin': asin,
            'success': False,
            'highest_price': None,
            'lowest_price': None,
            'out_of_stock': None,
            'time_taken': end_time - start_time,
            'error': str(e)
        }

def test_optimized_solution():
    """测试优化后的解决方案"""
    print("🚀 测试优化后的Keepa解决方案")
    print("=" * 60)
    
    # 测试ASIN列表
    test_asins = [
        "B07QNYTRG2",
        "B07SPTL99F", 
        "B07TXJKT46",
        "B08N5WRWNW",
        "B07YD2MXKR"
    ]
    
    print(f"📋 测试配置:")
    print(f"   ASIN数量: {len(test_asins)}")
    print(f"   线程数: 10")
    print(f"   市场: US")
    
    # 创建检查器（使用10个线程）
    checker = KeepaHistoricalPriceChecker(threads=10, marketplace="US", verbose=False)
    checker.load_proxies()
    
    print(f"\n🔄 开始并发测试...")
    start_time = time.time()
    
    results = []
    
    # 使用线程池并发处理
    with ThreadPoolExecutor(max_workers=10) as executor:
        # 提交所有任务
        future_to_asin = {
            executor.submit(test_single_asin, asin, checker): asin 
            for asin in test_asins
        }
        
        # 收集结果
        for future in as_completed(future_to_asin):
            asin = future_to_asin[future]
            try:
                result = future.result()
                results.append(result)
                
                # 实时显示结果
                if result['success']:
                    print(f"✅ {result['asin']}: ${result['highest_price']} / ${result['lowest_price']} ({result['time_taken']:.1f}s)")
                else:
                    print(f"❌ {result['asin']}: {result['error']} ({result['time_taken']:.1f}s)")
                    
            except Exception as e:
                print(f"❌ {asin}: 处理异常 - {str(e)}")
                results.append({
                    'asin': asin,
                    'success': False,
                    'error': str(e),
                    'time_taken': 0
                })
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 统计结果
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"\n📊 测试结果统计:")
    print(f"   总耗时: {total_time:.1f}秒")
    print(f"   平均每个ASIN: {total_time/len(test_asins):.1f}秒")
    print(f"   成功数量: {len(successful)}/{len(test_asins)}")
    print(f"   成功率: {(len(successful)/len(test_asins))*100:.1f}%")
    
    if successful:
        avg_time = sum(r['time_taken'] for r in successful) / len(successful)
        print(f"   成功案例平均耗时: {avg_time:.1f}秒")
        
        print(f"\n✅ 成功案例详情:")
        for result in successful:
            print(f"   {result['asin']}: 最高${result['highest_price']} 最低${result['lowest_price']}")
    
    if failed:
        print(f"\n❌ 失败案例:")
        for result in failed:
            print(f"   {result['asin']}: {result['error']}")
    
    # 性能评估
    print(f"\n⚡ 性能评估:")
    if len(successful) > 0:
        print(f"   🎯 优化成功！直接使用已知端点大幅提升速度")
        print(f"   🚀 并发处理效率: {len(test_asins)/total_time:.1f} ASIN/秒")
    else:
        print(f"   ⚠️  需要进一步优化端点或认证机制")
    
    return {
        'total_time': total_time,
        'success_rate': (len(successful)/len(test_asins))*100,
        'successful_results': successful,
        'failed_results': failed
    }

def test_endpoint_performance():
    """测试端点性能"""
    print(f"\n🔬 端点性能测试")
    print("=" * 40)
    
    checker = KeepaHistoricalPriceChecker(marketplace="US", verbose=False)
    checker.load_proxies()
    
    endpoints = [
        "https://discuss.keepa.com/t/data-features-access-free-vs-monthly-subscription/5845",
        "https://packagist.org/packages/keepa/php_api",
        "https://keepa.com/#!discuss/t/how-our-api-plans-work/"
    ]
    
    test_asin = "B07QNYTRG2"
    
    for i, endpoint in enumerate(endpoints, 1):
        print(f"\n{i}. 测试端点: {endpoint}")
        start_time = time.time()
        
        try:
            result = checker.try_keepa_api(endpoint, test_asin)
            end_time = time.time()
            
            if result:
                print(f"   ✅ 成功 ({end_time - start_time:.1f}s)")
                print(f"      最高价: ${result.get('historical_highest_price', 'N/A')}")
                print(f"      最低价: ${result.get('historical_lowest_price', 'N/A')}")
            else:
                print(f"   ❌ 失败 ({end_time - start_time:.1f}s)")
                
        except Exception as e:
            end_time = time.time()
            print(f"   ❌ 异常 ({end_time - start_time:.1f}s): {str(e)}")

if __name__ == "__main__":
    print("🎯 开始优化解决方案测试")
    
    # 测试优化后的解决方案
    results = test_optimized_solution()
    
    # 测试端点性能
    test_endpoint_performance()
    
    print(f"\n🌟 测试完成!")
    
    if results['success_rate'] > 80:
        print("🎉 优化非常成功！解决方案已经可以投入使用！")
    elif results['success_rate'] > 50:
        print("✅ 优化基本成功，还有进一步改进空间")
    else:
        print("⚠️  需要进一步优化解决方案")
