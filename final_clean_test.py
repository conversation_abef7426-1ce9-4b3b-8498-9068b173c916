#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终清理版本测试
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistorical<PERSON><PERSON><PERSON><PERSON><PERSON>

def test_clean_version():
    """测试清理后的版本"""
    print("🔍 测试清理后的版本...")
    
    # 创建分析器实例
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    
    # 测试ASIN列表
    test_asins = [
        "B0BQZ9K2N2",  # 已知有效的ASIN
        "B07ZNW4TLG",  # 用户输出中失败的ASIN
    ]
    
    results = []
    
    for asin in test_asins:
        print(f"📱 处理 {asin}...")
        
        try:
            result = analyzer.reverse_engineer_keepa(asin)
            
            if result:
                source = result.get('source', 'unknown')
                highest = result.get('historical_highest_price', 0)
                lowest = result.get('historical_lowest_price', 0)
                
                print(f"✅ {asin}: 最高${highest:.2f} 最低${lowest:.2f} 来源:{source}")
                results.append(True)
            else:
                print(f"❌ {asin}: 未获取到数据")
                results.append(False)
                
        except Exception as e:
            print(f"❌ {asin}: 异常 - {str(e)}")
            results.append(False)
    
    # 统计结果
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 测试结果:")
    print(f"   成功: {success_count}/{total_count}")
    print(f"   成功率: {success_count/total_count*100:.1f}%")
    
    return success_count > 0

def main():
    """主函数"""
    print("🚀 最终清理版本测试")
    print("=" * 50)
    
    success = test_clean_version()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试通过！WebSocket方法正常工作！")
        print("💡 现在脚本应该能够正常处理ASIN了")
        print("📝 输出更加清洁，没有多余的调试信息")
    else:
        print("❌ 测试失败，需要进一步调试")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
