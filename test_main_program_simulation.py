#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟主程序的实际运行环境
"""

import sys
import os
import json
from concurrent.futures import ThreadPoolExecutor

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def get_remaining_asins():
    """获取检查点之后剩余的ASIN"""
    # 从文件加载所有ASIN
    asins = []
    with open('ZYasin.txt', 'r', encoding='utf-8') as f:
        for line in f:
            asin = line.strip()
            if asin and len(asin) == 10:
                asins.append(asin)
    
    # 加载检查点
    with open('ZYasin_美国_checkpoint.json', 'r', encoding='utf-8') as f:
        checkpoint = json.load(f)
    
    last_processed = checkpoint['last_processed_asin']
    last_index = asins.index(last_processed)
    remaining_asins = asins[last_index + 1:]
    
    return remaining_asins

def process_single_asin(asin):
    """处理单个ASIN（模拟主程序的处理方式）"""
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    
    try:
        # 使用主程序的方法
        result = analyzer.get_keepa_info_with_browser(asin, max_retries=3)
        
        if result and (result.get('historical_highest_price') is not None or 
                      result.get('historical_lowest_price') is not None):
            return {
                'asin': asin,
                'success': True,
                'highest': result.get('historical_highest_price'),
                'lowest': result.get('historical_lowest_price'),
                'stock': result.get('out_of_stock_within_month')
            }
        else:
            return {
                'asin': asin,
                'success': False,
                'reason': 'no_price_data'
            }
            
    except Exception as e:
        return {
            'asin': asin,
            'success': False,
            'reason': f'exception: {str(e)}'
        }

def test_single_thread():
    """测试单线程处理剩余ASIN"""
    print("🔍 单线程处理剩余ASIN...")
    
    remaining_asins = get_remaining_asins()
    print(f"剩余ASIN数量: {len(remaining_asins)}")
    
    # 只测试前5个
    test_asins = remaining_asins[:5]
    
    results = []
    for i, asin in enumerate(test_asins, 1):
        print(f"\n📱 处理ASIN {i}/{len(test_asins)}: {asin}")
        result = process_single_asin(asin)
        results.append(result)
        
        if result['success']:
            print(f"✅ 成功: 最高价 ${result['highest']:.2f}, 最低价 ${result['lowest']:.2f}")
        else:
            print(f"❌ 失败: {result['reason']}")
    
    success_count = sum(1 for r in results if r['success'])
    print(f"\n📋 单线程结果: {success_count}/{len(test_asins)} 成功")
    return results

def test_multi_thread():
    """测试多线程处理剩余ASIN（模拟主程序）"""
    print("\n🔍 多线程处理剩余ASIN（模拟主程序）...")
    
    remaining_asins = get_remaining_asins()
    
    # 只测试前5个，使用5个线程（模拟主程序的高并发）
    test_asins = remaining_asins[:5]
    
    print(f"使用 {len(test_asins)} 个线程同时处理...")
    
    results = []
    with ThreadPoolExecutor(max_workers=len(test_asins)) as executor:
        futures = [executor.submit(process_single_asin, asin) for asin in test_asins]
        
        for i, future in enumerate(futures, 1):
            try:
                result = future.result(timeout=60)
                results.append(result)
                
                if result['success']:
                    print(f"✅ ASIN {i}: {result['asin']} 成功")
                else:
                    print(f"❌ ASIN {i}: {result['asin']} 失败 - {result['reason']}")
                    
            except Exception as e:
                results.append({
                    'asin': test_asins[i-1],
                    'success': False,
                    'reason': f'timeout_or_error: {str(e)}'
                })
                print(f"❌ ASIN {i}: {test_asins[i-1]} 超时或错误")
    
    success_count = sum(1 for r in results if r['success'])
    print(f"\n📋 多线程结果: {success_count}/{len(test_asins)} 成功")
    return results

def test_all_remaining():
    """测试所有剩余ASIN（模拟完整的主程序运行）"""
    print("\n🔍 测试所有剩余ASIN（完整模拟）...")
    
    remaining_asins = get_remaining_asins()
    print(f"总共剩余ASIN: {len(remaining_asins)}")
    
    # 使用10个线程（与主程序相同）
    batch_size = 10
    
    all_results = []
    
    for i in range(0, len(remaining_asins), batch_size):
        batch = remaining_asins[i:i+batch_size]
        print(f"\n处理批次 {i//batch_size + 1}: {len(batch)} 个ASIN")
        
        with ThreadPoolExecutor(max_workers=min(10, len(batch))) as executor:
            futures = [executor.submit(process_single_asin, asin) for asin in batch]
            
            batch_results = []
            for future in futures:
                try:
                    result = future.result(timeout=60)
                    batch_results.append(result)
                except Exception as e:
                    batch_results.append({
                        'asin': 'unknown',
                        'success': False,
                        'reason': f'batch_error: {str(e)}'
                    })
            
            all_results.extend(batch_results)
            
            # 显示批次结果
            batch_success = sum(1 for r in batch_results if r['success'])
            print(f"批次结果: {batch_success}/{len(batch)} 成功")
    
    total_success = sum(1 for r in all_results if r['success'])
    print(f"\n📋 完整测试结果: {total_success}/{len(remaining_asins)} 成功")
    print(f"总成功率: {total_success/len(remaining_asins)*100:.1f}%")
    
    return all_results

def main():
    """主函数"""
    print("🚀 模拟主程序实际运行环境...")
    print("=" * 60)
    
    # 测试单线程
    single_results = test_single_thread()
    
    # 测试多线程
    multi_results = test_multi_thread()
    
    # 比较结果
    single_success = sum(1 for r in single_results if r['success'])
    multi_success = sum(1 for r in multi_results if r['success'])
    
    print("\n" + "=" * 60)
    print("📋 对比分析:")
    print(f"   单线程成功: {single_success}/{len(single_results)}")
    print(f"   多线程成功: {multi_success}/{len(multi_results)}")
    
    if single_success > multi_success:
        print("\n🎯 确认问题: 多线程环境影响成功率")
        print("💡 建议解决方案:")
        print("   1. 减少并发线程数")
        print("   2. 增加请求间隔")
        print("   3. 改进错误重试机制")
    elif single_success == multi_success and single_success > 0:
        print("\n✅ 多线程工作正常")
        print("💡 主程序问题可能在其他地方")
    else:
        print("\n⚠️  所有方法都失败，需要进一步调查")

if __name__ == "__main__":
    main()
