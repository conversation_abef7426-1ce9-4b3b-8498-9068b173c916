[{"url": "https://challenges.cloudflare.com/turnstile/v0/b/8359bcf47b68/api.js", "status": 200, "headers": {"access-control-allow-origin": "*", "alt-svc": "h3=\":443\"; ma=86400", "cache-control": "max-age=31536000, stale-if-error=10800, stale-while-revalidate=31536000, public", "cf-ray": "966edb6b0d766848-NRT", "content-encoding": "br", "content-type": "application/javascript; charset=UTF-8", "cross-origin-resource-policy": "cross-origin", "date": "<PERSON><PERSON>, 29 Jul 2025 19:00:04 GMT", "last-modified": "Mon, 21 Jul 2025 14:54:13 GMT", "priority": "u=1,i=?0", "server": "cloudflare", "server-timing": "cfExtPri", "vary": "Accept-Encoding"}, "content": "\"use strict\";(function(){function Ht(e,t,a,o,c,l,m){try{var h=e[l](m),s=h.value}catch(p){a(p);return}h.done?t(s):Promise.resolve(s).then(o,c)}function Bt(e){return function(){var t=this,a=arguments;return new Promise(function(o,c){var l=e.apply(t,a);function m(s){Ht(l,o,c,m,h,\"next\",s)}function h(s){Ht(l,o,c,m,h,\"throw\",s)}m(void 0)})}}function P(e,t){return t!=null&&typeof Symbol!=\"undefined\"&&t[Symbol.hasInstance]?!!t[Symbol.hasInstance](e):P(e,t)}function Pe(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function Ve(e){for(var t=1;t<arguments.length;t++){var a=arguments[t]!=null?arguments[t]:{},o=Object.keys(a);typeof Object.getOwnPropertySymbols==\"function\"&&(o=o.concat(Object.getOwnPropertySymbols(a).filter(function(c){return Object.getOwnPropertyDescriptor(a,c).enumerable}))),o.forEach(function(c){Pe(e,c,a[c])})}return e}function kr(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(c){return Object.getOwnPropertyDescriptor(e,c).enumerable})),a.push.apply(a,o)}return a}function ot(e,t){return t=t!=null?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):kr(Object(t)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))}),e}function Gt(e){if(Array.isArray(e))return e}function Xt(e,t){var a=e==null?null:typeof Symbol!=\"undefined\"&&e[Symbol.iterator]||e[\"@@iterator\"];if(a!=null){var o=[],c=!0,l=!1,m,h;try{for(a=a.call(e);!(c=(m=a.next()).done)&&(o.push(m.value),!(t&&o.length===t));c=!0);}catch(s){l=!0,h=s}finally{try{!c&&a.return!=null&&a.return()}finally{if(l)throw h}}return o}}function Yt(){throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}function ct(e,t){(t==null||t>e.length)&&(t=e.length);for(var a=0,o=new Array(t);a<t;a++)o[a]=e[a];return o}function Qt(e,t){if(e){if(typeof e==\"string\")return ct(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);if(a===\"Object\"&&e.constructor&&(a=e.constructor.name),a===\"Map\"||a===\"Set\")return Array.from(a);if(a===\"Arguments\"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return ct(e,t)}}function Oe(e,t){return Gt(e)||Xt(e,t)||Qt(e,t)||Yt()}function F(e){\"@swc/helpers - typeof\";return e&&typeof Symbol!=\"undefined\"&&e.constructor===Symbol?\"symbol\":typeof e}function We(e,t){var a={label:0,sent:function(){if(l[0]&1)throw l[1];return l[1]},trys:[],ops:[]},o,c,l,m;return m={next:h(0),throw:h(1),return:h(2)},typeof Symbol==\"function\"&&(m[Symbol.iterator]=function(){return this}),m;function h(p){return function(_){return s([p,_])}}function s(p){if(o)throw new TypeError(\"Generator is already executing.\");for(;m&&(m=0,p[0]&&(a=0)),a;)try{if(o=1,c&&(l=p[0]&2?c.return:p[0]?c.throw||((l=c.return)&&l.call(c),0):c.next)&&!(l=l.call(c,p[1])).done)return l;switch(c=0,l&&(p=[p[0]&2,l.value]),p[0]){case 0:case 1:l=p;break;case 4:return a.label++,{value:p[1],done:!1};case 5:a.label++,c=p[1],p=[0];continue;case 7:p=a.ops.pop(),a.trys.pop();continue;default:if(l=a.trys,!(l=l.length>0&&l[l.length-1])&&(p[0]===6||p[0]===2)){a=0;continue}if(p[0]===3&&(!l||p[1]>l[0]&&p[1]<l[3])){a.label=p[1];break}if(p[0]===6&&a.label<l[1]){a.label=l[1],l=p;break}if(l&&a.label<l[2]){a.label=l[2],a.ops.push(p);break}l[2]&&a.ops.pop(),a.trys.pop();continue}p=t.call(e,a)}catch(_){p=[6,_],c=0}finally{o=l=0}if(p[0]&5)throw p[1];return{value:p[0]?p[1]:void 0,done:!0}}}var Kt=300,$t=10;var Jt={code:200500,description:\"Turnstile's api.js was loaded, but the iframe under challenges.cloudflare.com could not be loaded. Has the visitor blocked some parts of challenges.cloudflare.com or are they self-hosting api.js?\",internalRepr:\"iframe_load_err\",public:!0,retryable:!1};var Zt=300020;var Ue=300030;var je=300031;function ut(e){var t=new URLSearchParams;if(e.params._debugSitekeyOverrides&&(e.params._debugSitekeyOverrides.offlabel!==\"default\"&&t.set(\"offlabel\",e.params._debugSitekeyOverrides.offlabel),e.params._debugSitekeyOverrides.clearanceLevel!==\"default\"&&t.set(\"clearance_level\",e.params._debugSitekeyOverrides.clearanceLevel)),window.__cfDebugTurnstileOutcome&&t.set(\"__cfDebugTurnstileOutcome\",String(window.__cfDebugTurnstileOutcome)),t.size!==0)return t.toString()}var qe=\"cf-chl-widget-\",W=\"cloudflare-challenge\",er=\".cf-turnstile\",tr=\".cf-challenge\",rr=\".g-recaptcha\",lt=\"cf_challenge_response\",st=\"cf-turnstile-response\",dt=\"g-recaptcha-response\",ar=8e3,ft=\"private-token\",nr=3,ir=500,or=500,Y=\"\";var z;(function(e){e.Managed=\"managed\",e.NonInteractive=\"non-interactive\",e.Invisible=\"invisible\"})(z||(z={}));var M;(function(e){e.Normal=\"normal\",e.Compact=\"compact\",e.Invisible=\"invisible\",e.Flexible=\"flexible\"})(M||(M={}));var ze;(function(e){e.Auto=\"auto\",e.Light=\"light\",e.Dark=\"dark\"})(ze||(ze={}));var ge;(function(e){e.Verifying=\"verifying\",e.VerifyingHavingTroubles=\"verifying-having-troubles\",e.VerifyingOverrun=\"verifying-overrun\",e.FailureWoHavingTroubles=\"failure-wo-having-troubles\",e.FailureHavingTroubles=\"failure-having-troubles\",e.FailureFeedback=\"failure-feedback\",e.FailureFeedbackCode=\"failure-feedback-code\",e.ExpiredNeverRefresh=\"expired-never-refresh\",e.ExpiredManualRefresh=\"expired-manual-refresh\",e.TimeoutNeverRefresh=\"timeout-never-refresh\",e.TimeoutManualRefresh=\"timeout-manual-refresh\",e.InteractivityRequired=\"interactivity-required\",e.UnsupportedBrowser=\"unsupported-browser\",e.TimeCheckCachedWarning=\"time-check-cached-warning\",e.InvalidDomain=\"invalid-domain\"})(ge||(ge={}));var ye;(function(e){e.Never=\"never\",e.Auto=\"auto\"})(ye||(ye={}));var J;(function(e){e.Never=\"never\",e.Manual=\"manual\",e.Auto=\"auto\"})(J||(J={}));var oe;(function(e){e.Never=\"never\",e.Manual=\"manual\",e.Auto=\"auto\"})(oe||(oe={}));var Q;(function(e){e.Always=\"always\",e.Execute=\"execute\",e.InteractionOnly=\"interaction-only\"})(Q||(Q={}));var he;(function(e){e.Render=\"render\",e.Execute=\"execute\"})(he||(he={}));var ce;(function(e){e.Execute=\"execute\"})(ce||(ce={}));var H;(function(e){e.New=\"new\",e.CrashedRetry=\"crashed_retry\",e.FailureRetry=\"failure_retry\",e.StaleExecute=\"stale_execute\",e.AutoExpire=\"auto_expire\",e.AutoTimeout=\"auto_timeout\",e.ManualRefresh=\"manual_refresh\",e.Api=\"api\",e.CheckDelays=\"check_delays\",e.TimeCheckCachedWarningAux=\"time_check_cached_warning_aux\",e.JsCookiesMissingAux=\"js_cookies_missing_aux\",e.RedirectingTextOverrun=\"redirecting_text_overrun\"})(H||(H={}));function L(e,t){return e.indexOf(t)!==-1}var Nr=[\"bg-bg\",\"da-dk\",\"de-de\",\"el-gr\",\"ja-jp\",\"ms-my\",\"ru-ru\",\"sk-sk\",\"sl-si\",\"sr-ba\",\"tl-ph\",\"uk-ua\"],Mr=[\"ar-eg\",\"es-es\",\"cs-cz\",\"fa-ir\",\"fr-fr\",\"hr-hr\",\"hu-hu\",\"id-id\",\"it-it\",\"lt-lt\",\"nb-no\",\"nl-nl\",\"pl-pl\",\"pt-br\",\"th-th\",\"tr-tr\",\"ro-ro\"];function pt(e,t){var a=\"https://challenges.cloudflare.com\";if(t){var o;a=(o=e[\"base-url\"])!==null&&o!==void 0?o:a}return a}function vt(e,t,a,o,c,l,m,h){var s=pt(a,c),p=l?\"h/\".concat(l,\"/\"):\"\",_=h?\"?\".concat(h):\"\",A=a[\"feedback-enabled\"]===!1?\"fbD\":\"fbE\";return\"\".concat(s,\"/cdn-cgi/challenge-platform/\").concat(p,\"turnstile/if/ov2/av0/rcv\").concat(o,\"/\").concat(e,\"/\").concat(t,\"/\").concat(a.theme,\"/\").concat(A,\"/\").concat(m,\"/\").concat(a.size,\"/\").concat(a.language,\"/\").concat(_)}var mt=function(e){var t,a,o=window.innerWidth<400,c=e.state!==ge.FailureFeedbackCode&&(e.state===ge.FailureFeedback||e.state===ge.FailureHavingTroubles||!e.errorCode),l,m=L(Nr,(l=(t=e.displayLanguage)===null||t===void 0?void 0:t.toLowerCase())!==null&&l!==void 0?l:\"nonexistent\"),h,s=L(Mr,(h=(a=e.displayLanguage)===null||a===void 0?void 0:a.toLowerCase())!==null&&h!==void 0?h:\"nonexistent\");return o?Lr({isModeratelyVerbose:s,isSmallerFeedback:c,isVerboseLanguage:m}):c&&m?\"630px\":c&&s?\"620px\":c?\"600px\":m?\"690px\":\"680px\"},Lr=function(e){var t=e.isVerboseLanguage,a=e.isSmallerFeedback,o=e.isModeratelyVerbose;return a&&t?\"660px\":a&&o?\"620px\":a?\"600px\":t?\"770px\":o?\"740px\":\"730px\"};function He(e){if(e===void 0)throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");return e}function cr(e,t){if(!P(e,t))throw new TypeError(\"Cannot call a class as a function\")}function Z(e,t){return Z=Object.setPrototypeOf||function(o,c){return o.__proto__=c,o},Z(e,t)}function ur(e,t){if(typeof t!=\"function\"&&t!==null)throw new TypeError(\"Super expression must either be null or a function\");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Z(e,t)}function Be(){if(typeof Reflect==\"undefined\"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy==\"function\")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function ke(e,t,a){return Be()?ke=Reflect.construct:ke=function(c,l,m){var h=[null];h.push.apply(h,l);var s=Function.bind.apply(c,h),p=new s;return m&&Z(p,m.prototype),p},ke.apply(null,arguments)}function ue(e){return ue=Object.setPrototypeOf?Object.getPrototypeOf:function(a){return a.__proto__||Object.getPrototypeOf(a)},ue(e)}function lr(e){return Function.toString.call(e).indexOf(\"[native code]\")!==-1}function Ge(e){var t=typeof Map==\"function\"?new Map:void 0;return Ge=function(o){if(o===null||!lr(o))return o;if(typeof o!=\"function\")throw new TypeError(\"Super expression must either be null or a function\");if(typeof t!=\"undefined\"){if(t.has(o))return t.get(o);t.set(o,c)}function c(){return ke(o,arguments,ue(this).constructor)}return c.prototype=Object.create(o.prototype,{constructor:{value:c,enumerable:!1,writable:!0,configurable:!0}}),Z(c,o)},Ge(e)}function sr(e,t){return t&&(F(t)===\"object\"||typeof t==\"function\")?t:He(e)}function dr(e){var t=Be();return function(){var o=ue(e),c;if(t){var l=ue(this).constructor;c=Reflect.construct(o,arguments,l)}else c=o.apply(this,arguments);return sr(this,c)}}var fr=function(e){\"use strict\";ur(a,e);var t=dr(a);function a(o,c){cr(this,a);var l;return l=t.call(this,o),Pe(He(l),\"code\",void 0),l.name=\"TurnstileError\",l.code=c,l}return a}(Ge(Error));function v(e,t){var a=\"[Cloudflare Turnstile] \".concat(e,\".\");throw new fr(a,t)}function x(e){console.warn(\"[Cloudflare Turnstile] \".concat(e))}function Xe(e){return e.startsWith(qe)?e.substring(qe.length):null}function K(e){return\"\".concat(qe).concat(e)}function gt(){var e=/\\/turnstile\\/v0(\\/.*)?\\/api\\.js/,t=document.currentScript;if(P(t,HTMLScriptElement)&&e.test(t.src))return t;for(var a=document.querySelectorAll(\"script\"),o=0,c;c=a[o];o++)if(P(c,HTMLScriptElement)&&e.test(c.src))return c}function pr(){var e=gt();e||v(\"Could not find Turnstile script tag, some features may not be available\",43777);var t=e.src,a={loadedAsync:!1,params:new URLSearchParams,src:t};(e.async||e.defer)&&(a.loadedAsync=!0);var o=t.split(\"?\");return o.length>1&&(a.params=new URLSearchParams(o[1])),a}function U(){return typeof performance!=\"undefined\"&&performance.now?performance.now():Date.now()}var yt=function(e,t,a){var o=pt(t.params,!1),c=\"h/\".concat(\"b\",\"/\"),l,m,h=\"\".concat(o,\"/cdn-cgi/challenge-platform/\").concat(c,\"feedback-reports/\").concat(Xe(e),\"/\").concat(t.displayLanguage,\"/\").concat((m=t.params.theme)!==null&&m!==void 0?m:t.theme,\"/\").concat(a);if(window.top!==window.self){window.open(h,\"_blank\",\"noopener,noreferrer\");return}t.wrapper.parentNode||v(\"Cannot initialize Widget, Element not found (#\".concat(e,\").\"),3074);var s=document.createElement(\"div\");s.style.position=\"fixed\",s.style.zIndex=\"2147483646\",s.style.width=\"100vw\",s.style.height=\"100vh\",s.style.top=\"0\",s.style.left=\"0\",s.style.transformOrigin=\"center center\",s.style.overflowX=\"hidden\",s.style.overflowY=\"auto\",s.style.background=\"rgba(0,0,0,0.4)\";var p=document.createElement(\"div\");p.style.display=\"table-cell\",p.style.verticalAlign=\"middle\",p.style.width=\"100vw\",p.style.height=\"100vh\";var _=document.createElement(\"div\");_.className=\"cf-turnstile-feedback\",_.id=\"cf-fr-id\",_.style.width=\"100vw\",_.style.maxWidth=\"450px\",_.style.height=mt(t),_.style.position=\"relative\",_.style.zIndex=\"2147483647\",_.style.backgroundColor=\"#ffffff\",_.style.borderRadius=\"5px\",_.style.left=\"0px\",_.style.top=\"0px\",_.style.overflow=\"hidden\",_.style.margin=\"0px auto\";var A=document.createElement(\"iframe\");A.id=\"\".concat(e,\"-fr\"),A.setAttribute(\"src\",h),A.setAttribute(\"allow\",\"cross-origin-isolated; fullscreen\"),A.setAttribute(\"sandbox\",\"allow-same-origin allow-scripts allow-popups allow-forms\"),A.setAttribute(\"scrolling\",\"no\"),A.style.borderWidth=\"0px\",A.style.width=\"100%\",A.style.height=\"100%\",A.style.overflow=\"hidden\";var T=document.createElementNS(\"http://www.w3.org/2000/svg\",\"svg\");T.setAttribute(\"tabindex\",\"0\"),T.setAttribute(\"role\",\"img\"),T.setAttribute(\"aria-label\",\"Close button icon\"),T.style.position=\"absolute\",T.style.width=\"26px\",T.style.height=\"26px\",T.style.zIndex=\"2147483647\",T.style.cursor=\"pointer\",t.displayRtl?T.style.left=\"2px\":T.style.right=\"6px\",T.style.top=\"5px\",T.setAttribute(\"width\",\"20\"),T.setAttribute(\"height\",\"20\"),T.addEventListener(\"click\",function(){var R;(R=s.parentNode)===null||R===void 0||R.removeChild(s)}),T.addEventListener(\"keydown\",function(R){if(R.key===\"Enter\"||R.key===\" \"){var X;(X=s.parentNode)===null||X===void 0||X.removeChild(s)}});var k=document.createElementNS(\"http://www.w3.org/2000/svg\",\"ellipse\");k.setAttribute(\"ry\",\"12\"),k.setAttribute(\"rx\",\"12\"),k.setAttribute(\"cy\",\"12\"),k.setAttribute(\"cx\",\"12\"),k.setAttribute(\"fill\",\"none\"),k.setAttribute(\"stroke-width\",\"0\"),T.appendChild(k);var O=document.createElementNS(\"http://www.w3.org/2000/svg\",\"line\");O.setAttribute(\"stroke-width\",\"1\"),O.setAttribute(\"stroke\",\"#999\"),O.setAttribute(\"fill\",\"none\"),O.setAttribute(\"x1\",\"6\"),O.setAttribute(\"x2\",\"18\"),O.setAttribute(\"y1\",\"18\"),O.setAttribute(\"y2\",\"5\"),T.appendChild(O);var N=document.createElementNS(\"http://www.w3.org/2000/svg\",\"line\");N.setAttribute(\"stroke-width\",\"1\"),N.setAttribute(\"stroke\",\"#999\"),N.setAttribute(\"fill\",\"none\"),N.setAttribute(\"x1\",\"6\"),N.setAttribute(\"x2\",\"18\"),N.setAttribute(\"y1\",\"5\"),N.setAttribute(\"y2\",\"18\"),T.appendChild(N),_.appendChild(A),_.appendChild(T),p.appendChild(_),s.appendChild(p),s.addEventListener(\"click\",function(){var R;(R=s.parentNode)===null||R===void 0||R.removeChild(s)}),t.wrapper.parentNode.appendChild(s),window.addEventListener(\"resize\",function(){_.style.height=mt(t)})},vr=function(e){var t,a,o;(o=document.getElementById(e))===null||o===void 0||(a=o.parentElement)===null||a===void 0||(t=a.parentElement)===null||t===void 0||t.remove()};var Ye;(function(e){e.Failure=\"failure\",e.Verifying=\"verifying\",e.Overruning=\"overrunning\",e.Custom=\"custom\"})(Ye||(Ye={}));var ht=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:3;return e.length>t?e.substring(0,t):e};function mr(e){if(!e)return\"-\";var t=function(a,o){if(!a||a.tagName===\"BODY\")return o;for(var c=1,l=a.previousElementSibling;l;)l.tagName===a.tagName&&c++,l=l.previousElementSibling;var m=ht(a.tagName.toLowerCase()),h=\"\".concat(m,\"[\").concat(c,\"]\");return t(a.parentNode,\"/\".concat(h).concat(o))};return t(e,\"\")}function gr(e){if(!e)return\"\";var t=e.getBoundingClientRect();return\"\".concat(t.top,\"|\").concat(t.right)}var Fr={button:\"b\",checkbox:\"c\",email:\"e\",hidden:\"h\",number:\"n\",password:\"p\",radio:\"r\",select:\"sl\",submit:\"s\",text:\"t\",textarea:\"ta\"};function yr(e){if(!e)return\"\";var t=e.closest(\"form\");if(!t)return\"nf\";var a=Array.from(t.querySelectorAll(\"input, select, textarea, button\")),o=a.slice(0,20).map(function(l){return Fr[l.type]||\"-\"}).join(\"\"),c=[\"m:\".concat(t.method||\"\"),\"f:\".concat(a.length),o].join(\"|\");return c}function hr(e,t,a){for(var o=\"\",c=0,l=document.createNodeIterator(e,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT,{acceptNode:function(A){return c>t||o.length>a?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT}}),m;(m=l.nextNode())!==null&&o.length<a;){if(m.nodeType===Node.ELEMENT_NODE){var h=m;o+=\"\".concat(ht(h.tagName.toLowerCase()));for(var s=0;s<h.attributes.length;s++){var p=h.attributes[s];o+=\"_\".concat(ht(p.name,2))}o+=\">\"}else m.nodeType===Node.TEXT_NODE&&(o+=\"-t\");var _=m.parentNode;for(c=0;_!==e&&_!==null;)c++,_=_.parentNode}return o.substring(0,a)}function _r(e){if(typeof e!=\"string\")throw new Error(\"djb2: expected string, got \".concat(typeof e==\"undefined\"?\"undefined\":F(e)));for(var t=5381,a=0;a<e.length;a++){var o=e.charCodeAt(a);t=t*33^o}return t>>>0}function br(e,t){var a;t.upgradeAttempts++;var o=gt();if(!(!o||!o.parentNode)){var c=o==null?void 0:o.nonce;e._pState=t;var l=new URL(o.src),m=document.createElement(\"script\");l.searchParams.set(\"_upgrade\",\"true\"),l.searchParams.set(\"_cb\",String(Date.now())),m.async=!0,c&&(m.nonce=c),m.setAttribute(\"crossorigin\",\"anonymous\"),m.src=l.toString(),o==null||(a=o.parentNode)===null||a===void 0||a.replaceChild(m,o)}}function xr(e,t){var a=e._pState;return a?(t.isReady=a.isReady,t.isRecaptchaCompatibilityMode=a.isRecaptchaCompatibilityMode,t.lastWidgetIdx=a.lastWidgetIdx,t.scriptWasLoadedAsync=a.scriptWasLoadedAsync,t.upgradeAttempts=a.upgradeAttempts,t.upgradeCompletedCount=a.upgradeCompletedCount+1,t.turnstileLoadInitTimeMs=U(),t.watchCatInterval=null,t.watchCatSeq=a.watchCatSeq,t.widgetMap=a.widgetMap,!0):!1}function _t(e){return L([\"auto\",\"dark\",\"light\"],e)}function bt(e){return L([\"auto\",\"never\"],e)}function xt(e){return e>0&&e<9e5}function wt(e){return e>0&&e<36e4}var Dr=/^[0-9A-Za-z_-]{3,100}$/;function wr(e){return Dr.test(e)}var Pr=/^[a-z0-9_-]{0,32}$/i;function Et(e){return e===void 0?!0:typeof e==\"string\"&&Pr.test(e)}var Vr=/^[a-z0-9_\\-=]{0,255}$/i;function Tt(e){return e===void 0?!0:typeof e==\"string\"&&Vr.test(e)}function Rt(e){return L([M.Normal,M.Compact,M.Invisible,M.Flexible],e)}function St(e){return L([\"auto\",\"manual\",\"never\"],e)}function At(e){return L([\"auto\",\"manual\",\"never\"],e)}var Wr=/^[a-z]{2,3}([-_][a-z]{2})?$/i;function It(e){return e===\"auto\"||Wr.test(e)}function Ct(e){return L([\"always\",\"execute\",\"interaction-only\"],e)}function Er(e){return L([\"true\",\"false\"],e)}function Ot(e){return L([\"render\",\"execute\"],e)}var Ur=900,jr=10,qr=50;function zr(e){e.watchCatSeq++;var t=!0,a=!1,o=void 0;try{for(var c=e.widgetMap[Symbol.iterator](),l;!(t=(l=c.next()).done);t=!0){var m=Oe(l.value,2),h=m[0],s=m[1],p;s.watchcat.seq=e.watchCatSeq,s.watchcat.lastAckedSeq===0&&(s.watchcat.lastAckedSeq=e.watchCatSeq);var _=K(h);if(!_||!s.shadow){s.watchcat.missingWidgetWarning||(x(\"Cannot find Widget \".concat(_,\", consider using turnstile.remove() to clean up a widget.\")),s.watchcat.missingWidgetWarning=!0);continue}var A=s.shadow.querySelector(\"#\".concat(_));if(!A){s.watchcat.missingWidgetWarning||(x(\"Cannot find Widget \".concat(_,\", consider using turnstile.remove() to clean up a widget.\")),s.watchcat.missingWidgetWarning=!0);continue}if(!(s.isComplete||s.isFailed||s.isResetting)){var T=s.watchcat.seq-1-jr,k=s.watchcat.lastAckedSeq<T,O=s.watchcat.seq-1-qr,N=s.isOverrunning&&s.watchcat.overrunBeginSeq<O;if((s.isExecuting||!s.isInitialized||s.isInitialized&&!s.isStale&&!s.isExecuted)&&s.watchcat.lastAckedSeq!==0&&k||N){var R;s.watchcat.lastAckedSeq=0,s.watchcat.seq=0,s.isExecuting=!1;var X=function(r,u){console.log(\"Turnstile Widget seem to have \".concat(r,\": \"),u)};X(k?\"hung\":\"crashed\",h);var b=k?Ue:je,n;if((R=e.msgHandler)===null||R===void 0||R.call(e,{data:{code:b,event:\"fail\",rcV:(n=s.nextRcV)!==null&&n!==void 0?n:Y,source:W,widgetId:h}}),0)var i;continue}(p=A.contentWindow)===null||p===void 0||p.postMessage({event:\"meow\",seq:e.watchCatSeq,source:W,widgetId:h},\"*\")}}}catch(r){a=!0,o=r}finally{try{!t&&c.return!=null&&c.return()}finally{if(a)throw o}}}function kt(e){e.watchCatInterval===null&&(e.watchCatInterval=setInterval(function(){zr(e)},Ur))}function Nt(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;e.watchCatInterval!==null&&(e.widgetMap.size===0||t)&&clearInterval(e.watchCatInterval)}var y={apiVersion:1,isReady:!1,isRecaptchaCompatibilityMode:!1,lastWidgetIdx:0,scriptUrl:\"undefined\",scriptWasLoadedAsync:!1,turnstileLoadInitTimeMs:U(),upgradeAttempts:0,upgradeCompletedCount:0,watchCatInterval:null,watchCatSeq:0,widgetMap:new Map};function Mt(e){Ar(e,\"\")}function Pt(){var e=[er,tr];y.isRecaptchaCompatibilityMode&&e.push(rr),document.querySelectorAll(e.join(\", \")).forEach(function(t){return Vt.render(t)})}var Wt=[];function Tr(){y.isReady=!0;var e=!0,t=!1,a=void 0;try{for(var o=Wt[Symbol.iterator](),c;!(e=(c=o.next()).done);e=!0){var l=c.value;l()}}catch(m){t=!0,a=m}finally{try{!e&&o.return!=null&&o.return()}finally{if(t)throw a}}}function Hr(e){var t=y.widgetMap.get(e);if(!(t===void 0||t.hasResponseParamEl)){t.hasResponseParamEl=!0;var a,o=(a=t.params[\"response-field\"])!==null&&a!==void 0?a:!0;if(o){var c=document.createElement(\"input\");c.type=\"hidden\";var l;c.name=(l=t.params[\"response-field-name\"])!==null&&l!==void 0?l:st,c.id=\"\".concat(e,\"_response\"),t.wrapper.appendChild(c);var m;if(typeof t.params[\"response-field-name\"]!=\"string\"&&Ir((m=t.params.sitekey)!==null&&m!==void 0?m:\"\")){var h=document.createElement(\"input\");h.type=\"hidden\",h.name=lt,h.id=\"\".concat(e,\"_legacy_response\"),t.wrapper.appendChild(h)}}if(y.isRecaptchaCompatibilityMode){var s=document.createElement(\"input\");s.type=\"hidden\",s.name=dt,s.id=\"\".concat(e,\"_g_response\"),t.wrapper.appendChild(s)}}}function Ar(e,t){Hr(e);var a=document.getElementById(\"\".concat(e,\"_response\"));a!==null&&P(a,HTMLInputElement)&&(a.value=t);var o=document.getElementById(\"\".concat(e,\"_legacy_response\"));if(o!==null&&P(o,HTMLInputElement)&&(o.value=t),y.isRecaptchaCompatibilityMode){var c=document.getElementById(\"\".concat(e,\"_g_response\"));c!==null&&P(c,HTMLInputElement)&&(c.value=t)}}function Lt(e,t){var a=t.params,o=a.size,c=o===void 0?\"normal\":o,l=t.mode;switch(l){case z.NonInteractive:case z.Managed:switch(c){case M.Compact:e.style.width=\"150px\",e.style.height=\"140px\";break;case M.Invisible:v('Invalid value for parameter \"size\", expected \"'.concat(M.Compact,'\", \"').concat(M.Flexible,'\", or \"').concat(M.Normal,'\", got \"').concat(c,'\"'),2817);case M.Normal:e.style.width=\"300px\",e.style.height=\"65px\";break;case M.Flexible:e.style.width=\"100%\",e.style.maxWidth=\"100vw\",e.style.minWidth=\"300px\",e.style.height=\"65px\";break;default:break}break;case z.Invisible:e.style.width=\"0\",e.style.height=\"0\",e.style.position=\"absolute\",e.style.visibility=\"hidden\",e.setAttribute(\"tabindex\",\"-1\"),e.setAttribute(\"aria-hidden\",\"true\");break;default:v('Invalid value for parameter \"mode\", expected \"'.concat(z.NonInteractive,'\", \"').concat(z.Managed,'\" or \"').concat(z.Invisible,'\", got \"').concat(l,'\"'),2818)}}function Rr(e){e.style.width=\"0\",e.style.height=\"0\"}function Br(e,t){var a=t.get(\"turnstile_iframe_alt\");a&&(e.title=a)}function Gr(e){return e.origin?e.origin===\"https://challenges.cloudflare.com\"||e.origin===\"https://challenges-staging.cloudflare.com\":!0}function Ir(e){return e.startsWith(\"0x4AAAAAAAAAA\")||e.startsWith(\"0x4AAAAAAAAj\")}function Ft(){for(var e=window;e&&e.top!==e&&!e.location.href.startsWith(\"http\");)e=e.top;return e==null?void 0:e.location.href}var Vt=function(){var e=function(n,i,r,u){return O.apply(this,arguments)},t=function(n,i,r){if(n.params.retry===ye.Auto||r){n.isExecuted&&(n.msgQueue.push(ce.Execute),n.isExecuted=!0,n.isExecuting=!0);var u,d=r?0:1e3*2+((u=n.params[\"retry-interval\"])!==null&&u!==void 0?u:0);n.retryTimeout=window.setTimeout(function(){var g=r?H.CrashedRetry:H.FailureRetry;_(g,i)},d)}},a=function(n,i,r){var u;n.response===void 0&&v(\"[Internal Error] Widget was completed but no response was given\",1362),n.isExecuting=!1,n.isComplete=!0,Ar(i,n.response),(u=n.cbSuccess)===null||u===void 0||u.call(n,n.response,r)},o=function(n){if(!n)return[];for(var i=n.attributes,r=i.length,u=new Array(r),d=0;d<r;d++)u[d]=i[d].name;return u},c=function(n,i,r){if(n.rcV=i,0)var u},l=function(){var n=\"abcdefghijklmnopqrstuvwxyz0123456789\",i=n.length,r=\"\";do{r=\"\";for(var u=0;u<5;u++)r+=n.charAt(Math.floor(Math.random()*i))}while(y.widgetMap.has(r));return r},m=function(n){var i=!0,r=!1,u=void 0;try{for(var d=y.widgetMap[Symbol.iterator](),g;!(i=(g=d.next()).done);i=!0){var w=Oe(g.value,2),E=w[0],S=w[1];if(S.wrapper.parentElement===n)return E}}catch(I){r=!0,u=I}finally{try{!i&&d.return!=null&&d.return()}finally{if(r)throw u}}return null},h=function(n,i,r){for(;n.msgQueue.length>0;){var u,d=n.msgQueue.pop();(u=r.contentWindow)===null||u===void 0||u.postMessage({event:d,source:W,widgetId:i},\"*\")}},s=function(n,i){if(i){var r=[\"retry-interval\",\"retry\",\"size\",\"theme\",\"tabindex\",\"execution\",\"refresh-expired\",\"refresh-timeout\",\"response-field-name\",\"response-field\",\"language\",\"base-url\",\"appearance\",\"sitekey\",\"feedback-enabled\"],u=[],d=!0,g=!1,w=void 0;try{for(var E=r[Symbol.iterator](),S;!(d=(S=E.next()).done);d=!0){var I=S.value;i[I]&&i[I]!==n.params[I]&&u.push(I)}}catch(D){g=!0,w=D}finally{try{!d&&E.return!=null&&E.return()}finally{if(g)throw w}}u.length>0&&v(\"The parameters \".concat(r.join(\",\"),\" is/are not allowed be changed between the calls of render() and execute() of a widget.\\n    Consider rendering a new widget if you want to change the following parameters \").concat(u.join(\",\")),3618),i.action&&(Et(i.action)||v('Invalid input for optional parameter \"action\", got \"'.concat(i.action,'\"'),3604),n.action=i.action),i.cData&&(Tt(i.cData)||v('Invalid input for optional parameter \"cData\", got \"'.concat(i.cData,'\"'),3605),n.cData=i.cData),i[\"after-interactive-callback\"]&&(n.cbAfterInteractive=i[\"after-interactive-callback\"]),i[\"before-interactive-callback\"]&&(n.cbBeforeInteractive=i[\"before-interactive-callback\"]),i.callback&&(n.cbSuccess=i.callback),i[\"expired-callback\"]&&(n.cbExpired=i[\"expired-callback\"]),i[\"timeout-callback\"]&&(n.cbTimeout=i[\"timeout-callback\"]),i[\"error-callback\"]&&(n.cbError=i[\"error-callback\"]),i[\"unsupported-callback\"]&&(n.cbUnsupported=i[\"unsupported-callback\"]),i.chlPageData&&(n.chlPageData=i.chlPageData)}},p=function(n){_(H.Api,n)},_=function(n,i){var r=R(i);r||v(\"Nothing to reset found for provided container\",3329);var u=y.widgetMap.get(r);if(u){var d;u.isResetting=!0,u.response=void 0,u.mode=void 0,u.msgQueue=[],u.isComplete=!1,u.isExecuting=!1,u.isExpired=!1,u.isFailed=!1,u.isInitialized=!1,u.isStale=!1,u.watchcat.lastAckedSeq=0,u.watchcat.seq=0,u.params.execution===he.Render&&(u.msgQueue.push(ce.Execute),u.isExecuted=!0,u.isExecuting=!0);var g=K(r),w=u.shadow.querySelector(\"#\".concat(g));(!g||!w)&&v(\"Widget \".concat(r,\" to reset was not found.\"),3330),u.params.appearance===Q.InteractionOnly&&Rr(w),u.params.sitekey===null&&v(\"Unexpected Error: Sitekey is null\",3347);var E=w.cloneNode(),S;E.src=vt(r,u.params.sitekey,u.params,(S=u.rcV)!==null&&S!==void 0?S:Y,!1,\"b\",n,ut(u)),(d=w.parentNode)===null||d===void 0||d.replaceChild(E,w),Mt(g),u.retryTimeout&&window.clearTimeout(u.retryTimeout)}else v(\"Widget \".concat(r,\" to reset was not found.\"),3331)},A=function(n){var i,r=R(n);if(!r||!y.widgetMap.has(r)){x(\"Nothing to remove found for the provided container.\");return}var u=K(r),d=[\"input#\".concat(u,\"_response\"),\"input#\".concat(u,\"_legacy_response\"),\"input#\".concat(u,\"_g_response\")];document.querySelectorAll(d.join(\", \")).forEach(function(E){return E.remove()});var g=y.widgetMap.get(r);g==null||g.shadow.querySelectorAll(d.join(\", \")).forEach(function(E){return E.remove()}),g==null||g.wrapper.remove();var w=(i=y.widgetMap.get(r))===null||i===void 0?void 0:i.retryTimeout;w&&window.clearTimeout(w),y.widgetMap.delete(r),Nt(y)},T=function(n,i){var r,u,d=U(),g;if(typeof n==\"string\")try{var w=document.querySelector(n);w||v('Unable to find a container for \"'.concat(n,'\"'),3585),g=w}catch(zt){v('Invalid type for \"container\", expected \"selector\" or an implementation of \"HTMLElement\", got \"'.concat(n,'\"'),3586)}else P(n,HTMLElement)?g=n:v('Invalid type for parameter \"container\", expected \"string\" or an implementation of \"HTMLElement\"',3587);var E=!0,S=!1,I=void 0;try{for(var D=y.widgetMap.values()[Symbol.iterator](),B;!(E=(B=D.next()).done);E=!0){var ee=B.value;if(g.contains(ee.wrapper)){x(\"Turnstile has already been rendered in this container. Did you mean to render it multiple times?\");break}}}catch(zt){S=!0,I=zt}finally{try{!E&&D.return!=null&&D.return()}finally{if(S)throw I}}var V=Ft();if(!V)return v(\"Turnstile cannot determine its page location\",3607);if(V.startsWith(\"file:\"))return v(\"Turnstile cannot run in a file:// url\",3608);var le=Xr(g);if(le){var f=Object.assign(le,i),te=f.action,se=f.cData,xe=f.chlPageData,j=f.sitekey,de;f.theme=(de=f.theme)!==null&&de!==void 0?de:ze.Auto;var re;f.retry=(re=f.retry)!==null&&re!==void 0?re:ye.Auto;var we;f.execution=(we=f.execution)!==null&&we!==void 0?we:he.Render;var ae;f.appearance=(ae=f.appearance)!==null&&ae!==void 0?ae:Q.Always;var Ee;f[\"retry-interval\"]=Number((Ee=f[\"retry-interval\"])!==null&&Ee!==void 0?Ee:ar);var fe;f[\"expiry-interval\"]=Number((fe=f[\"expiry-interval\"])!==null&&fe!==void 0?fe:(Kt-$t)*1e3);var ne;f.size=(ne=f.size)!==null&&ne!==void 0?ne:M.Normal;var Te=f.callback,Ne=f[\"expired-callback\"],Re=f[\"timeout-callback\"],Se=f[\"after-interactive-callback\"],Ae=f[\"before-interactive-callback\"],ie=f[\"error-callback\"],Ze=f[\"unsupported-callback\"];typeof j!=\"string\"&&v('Invalid or missing type for parameter \"sitekey\", expected \"string\", got \"'.concat(typeof j==\"undefined\"?\"undefined\":F(j),'\"'),3588),wr(j)||v('Invalid input for parameter \"sitekey\", got \"'.concat(j,'\"'),3589),Rt(f.size)||v('Invalid type for parameter \"size\", expected normal|compact, got \"'.concat(f.size,'\" ').concat(F(f.size)),3590),_t(f.theme)||v('Invalid type for parameter \"theme\", expected dark|light|auto, got \"'.concat(f.theme,'\" ').concat(F(f.theme)),3591),bt(f.retry)||v('Invalid type for parameter \"retry\", expected never|auto, got \"'.concat(f.retry,'\" ').concat(F(f.retry)),3592),f.language||(f.language=\"auto\"),It(f.language)||(x('Invalid language value: \"'.concat(f.language,\", expected either: auto, or an ISO 639-1 two-letter language code (e.g. en) or language and country code (e.g. en-US).\")),f.language=\"auto\"),Ct(f.appearance)||v('Unknown appearance value: \"'.concat(f.appearance,\", expected either: 'always', 'execute', or 'interaction-only'.\"),3600),Ot(f.execution)||v('Unknown execution value: \"'.concat(f.execution,\", expected either: 'render' or 'execute'.\"),3601),xt(f[\"retry-interval\"])||v('Invalid retry-interval value: \"'.concat(f[\"retry-interval\"],', expected an integer value > 0 and < 900000\"'),3602),wt(f[\"expiry-interval\"])||v('Invalid expiry-interval value: \"'.concat(f[\"expiry-interval\"],', expected an integer value > 0 and < 360000\"'),3602);var Ie,$=(Ie=f[\"refresh-expired\"])!==null&&Ie!==void 0?Ie:J.Auto;St($)?f[\"refresh-expired\"]=$:v('Invalid type for parameter \"refresh-expired\", expected never|manual|auto, got \"'.concat($,'\" ').concat(typeof $==\"undefined\"?\"undefined\":F($)),3603);var pe,Me=(pe=f[\"refresh-timeout\"])!==null&&pe!==void 0?pe:oe.Auto;At($)?f[\"refresh-timeout\"]=Me:v('Invalid type for parameter \"refresh-timeout\", expected never|manual|auto, got \"'.concat(Me,'\" ').concat(typeof Me==\"undefined\"?\"undefined\":F(Me)),3603);var q=document.createElement(\"iframe\"),ve=document.createElement(\"div\"),Ut=ve.attachShadow({mode:\"closed\"});Et(te)||v('Invalid input for optional parameter \"action\", got \"'.concat(te,'\"'),3604),Tt(se)||v('Invalid input for optional parameter \"cData\", got \"'.concat(se,'\"'),3605);var Ce=l(),me=K(Ce);if(!(!Ce||!me)){var jt=[],et=f.execution===he.Render;et&&jt.push(ce.Execute),y.lastWidgetIdx++;var Cr={};y.widgetMap.set(Ce,ot(Ve({action:te,cbAfterInteractive:Se,cbBeforeInteractive:Ae,cbError:ie,cbExpired:Ne,cbSuccess:Te,cbTimeout:Re,cbUnsupported:Ze,cData:se,chlPageData:xe,hasResponseParamEl:!1,idx:y.lastWidgetIdx,isComplete:!1,isExecuted:et,isExecuting:et,isExpired:!1,isFailed:!1,isInitialized:!1,isResetting:!1,isStale:!1,msgQueue:jt,params:f,rcV:Y,watchcat:{lastAckedSeq:0,missingWidgetWarning:!1,overrunBeginSeq:0,seq:0}},Cr),{isOverrunning:!1,shadow:Ut,widgetInitStartTimeMs:0,widgetParamsStartTimeMs:0,widgetRenderEndTimeMs:0,widgetRenderStartTimeMs:d,wrapper:ve})),kt(y);var tt=y.widgetMap.get(Ce);tt||v(\"Turnstile Initialization Error \",3606),q.style.display=\"none\",q.style.border=\"none\",q.style.overflow=\"hidden\",q.setAttribute(\"src\",vt(Ce,j,f,Y,!1,\"b\",H.New,ut(tt))),q.onerror=function(){if(ie){ie==null||ie(String(Jt.code));return}v(\"Could not load challenge from challenges.cloudflare.com.\",161)};var qt=[\"cross-origin-isolated\",\"fullscreen\",\"autoplay\"],rt;L((rt=(u=document.featurePolicy)===null||u===void 0||(r=u.features)===null||r===void 0?void 0:r.call(u))!==null&&rt!==void 0?rt:[],ft)&&qt.push(ft),q.setAttribute(\"allow\",qt.join(\"; \")),q.setAttribute(\"sandbox\",\"allow-same-origin allow-scripts allow-popups\"),q.id=me;var at;q.tabIndex=(at=f.tabindex)!==null&&at!==void 0?at:0,q.title=\"Widget containing a Cloudflare security challenge\",Ut.appendChild(q);var nt,Or=(nt=f[\"response-field\"])!==null&&nt!==void 0?nt:!0;if(Or){var Le=document.createElement(\"input\");Le.type=\"hidden\";var it;if(Le.name=(it=f[\"response-field-name\"])!==null&&it!==void 0?it:st,Le.id=\"\".concat(me,\"_response\"),ve.appendChild(Le),typeof f[\"response-field-name\"]!=\"string\"&&Ir(j)){var Fe=document.createElement(\"input\");Fe.type=\"hidden\",Fe.name=lt,Fe.id=\"\".concat(me,\"_legacy_response\"),ve.appendChild(Fe)}}if(y.isRecaptchaCompatibilityMode){var De=document.createElement(\"input\");De.type=\"hidden\",De.name=dt,De.id=\"\".concat(me,\"_g_response\"),ve.appendChild(De)}return g.appendChild(ve),tt.widgetRenderEndTimeMs=U(),me}}},k=function(){var n,i=-1,r=!0,u=!1,d=void 0;try{for(var g=y.widgetMap[Symbol.iterator](),w;!(r=(w=g.next()).done);r=!0){var E=Oe(w.value,2),S=E[0],I=E[1];i<I.idx&&(n=S,i=I.idx)}}catch(D){u=!0,d=D}finally{try{!r&&g.return!=null&&g.return()}finally{if(u)throw d}}return i===-1&&v(\"Could not find widget\",43778),n};function O(){return O=Bt(function(b,n,i,r){var u,d,g,w,E,S,I,D,B,ee;return We(this,function(V){switch(V.label){case 0:if(u=b.params.sitekey,d=Ft(),!d)return x(\"Cannot determine Turnstile's embedded location, aborting clearance redemption.\"),a(b,n,!1),[2];g=\"h/\".concat(\"b\",\"/\"),w=new URL(d),E=\"https\",S=\"\",D=\"\".concat(E,\"://\").concat(w.host,\"/cdn-cgi/challenge-platform/\").concat(g,\"rc/\").concat(r).concat(S),V.label=1;case 1:return V.trys.push([1,3,,4]),[4,fetch(D,{body:JSON.stringify({secondaryToken:i,sitekey:u}),headers:{\"Content-Type\":\"application/json\"},method:\"POST\",redirect:\"manual\"})];case 2:return B=V.sent(),B.status!==200?(x(\"Cannot determine Turnstile's embedded location, aborting clearance redemption, are you running Turnstile on a Cloudflare Zone?\"),a(b,n,!1)):a(b,n,!0),[3,4];case 3:return ee=V.sent(),x(\"Error contacting Turnstile, aborting clearance remdeption.\"),a(b,n,!1),[3,4];case 4:return[2]}})}),O.apply(this,arguments)}var N=function(b){var n=b.data;if(n.source===W){if(!Gr(b)){x(\"Ignored message from wrong origin: \".concat(b.origin,\".\"));return}if(!(!n.widgetId||!y.widgetMap.has(n.widgetId))){var i=K(n.widgetId),r=y.widgetMap.get(n.widgetId);if(!(!i||!r))switch(n.event){case\"init\":{var u;r.widgetInitStartTimeMs=U();var d=r.shadow.getElementById(i);d||v(\"Cannot initialize Widget, Element not found (#\".concat(i,\").\"),3074),r.mode=n.mode,r.nextRcV=n.nextRcV,r.mode===z.Invisible&&r.params[\"refresh-expired\"]===J.Manual&&x(\"refresh-expired=manual is impossible in invisible mode, consider using '\".concat(J.Auto,\"' or '\").concat(J.Never,\".'\")),r.mode!==z.Managed&&r.params[\"refresh-timeout\"]!==oe.Auto&&x(\"setting refresh-timeout has no effect on an invisible/non-interactive widget and will be ignored.\"),r.params.appearance===Q.Always||r.isExecuting&&r.params.appearance===Q.Execute?Lt(d,r):Rr(d),d.style.display=\"\";var g=r.shadow.querySelector(\"#\".concat(i));g||v(\"Received state for an unknown widget: \".concat(n.widgetId),3078),(u=g.contentWindow)===null||u===void 0||u.postMessage({event:\"init\",source:W,widgetId:n.widgetId},\"*\");break}case\"translationInit\":{var w=r.shadow.getElementById(i);w||v(\"Cannot initialize Widget, Element not found (#\".concat(i,\").\"),3074);var E=new Map;r.displayLanguage=n.displayLanguage,Object.keys(n.translationData).forEach(function(pe){E.set(pe,n.translationData[pe])}),Br(w,E);break}case\"languageUnsupported\":{x(\"Language \".concat(r.params.language,\" is not supported, falling back to: \").concat(n.fallback,\".\")),r.displayLanguage=n.fallback;break}case\"reject\":{var S=r.shadow.getElementById(i);if(r.isExecuting=!1,S||v(\"Cannot initialize Widget, Element not found (#\".concat(i,\").\"),3075),n.reason===\"unsupported_browser\"){var I;(I=r.cbUnsupported)===null||I===void 0||I.call(r)}break}case\"food\":{r.watchcat&&n.seq>r.watchcat.lastAckedSeq&&(r.watchcat.lastAckedSeq=n.seq);break}case\"overrunBegin\":{r.isOverrunning=!0,r.watchcat&&(r.watchcat.overrunBeginSeq=r.watchcat.lastAckedSeq);break}case\"overrunEnd\":{r.isOverrunning=!1;break}case\"complete\":{c(r,Y,n.widgetId),r.response=n.token,n.sToken?e(r,i,n.sToken,n.chlId):a(r,i,!1);break}case\"fail\":{n.rcV&&c(r,n.rcV,n.widgetId),n.cfChlOut&&(r.cfChlOut=n.cfChlOut),n.cfChlOutS&&(r.cfChlOutS=n.cfChlOutS),n.code&&(r.errorCode=n.code),r.isExecuting=!1,r.isFailed=!0,r.isInitialized=!0,Mt(i);var D=r.cbError,B=n.code===Ue||n.code===je;if(B){var ee,V=r.shadow.querySelector(\"#\".concat(i));V==null||(ee=V.contentWindow)===null||ee===void 0||ee.postMessage({event:\"forceFail\",source:W,widgetId:n.widgetId},\"*\")}if(D){var le;D(String((le=n.code)!==null&&le!==void 0?le:Zt))?r.params.retry===ye.Auto&&!r.isResetting&&t(r,i,B):(n.code&&x(\"Error: \".concat(n.code,\".\")),t(r,i,B))}else n.code?(t(r,i,B),v(\"Error: \".concat(n.code),3076)):t(r,i,!1);break}case\"feedbackInit\":{var f=r.wrapper.querySelector(\"#\".concat(i,\"-fr\"));if(f){x(\"A feedback report form is already opened for this widget.\");return}yt(i,r,n.feedbackOrigin);break}case\"requestFeedbackData\":{var te,se=r.shadow.querySelector(\"#\".concat(i));se||v(\"Received state for an unknown widget: #\".concat(i,\" / \").concat(n.widgetId),3078),(te=se.contentWindow)===null||te===void 0||te.postMessage({event:\"requestTurnstileResults\",source:W,widgetId:n.widgetId},\"*\");break}case\"turnstileResults\":{var xe,j,de=(xe=r.wrapper.parentNode)===null||xe===void 0?void 0:xe.querySelector(\"#\".concat(i,\"-fr\"));de||v(\"Received state for an unknown widget: \".concat(n.widgetId),3078),(j=de.contentWindow)===null||j===void 0||j.postMessage({cfChlOut:r.cfChlOut,cfChlOutS:r.cfChlOutS,errorCode:r.errorCode,event:\"feedbackData\",md:n.md,mode:n.mode,rayId:n.rayId,rcV:n.rcV,sitekey:n.sitekey,source:W,widgetId:n.widgetId},\"*\");break}case\"closeFeedbackReportIframe\":{var re,we=(re=r.wrapper.parentNode)===null||re===void 0?void 0:re.querySelector(\"#\".concat(i,\"-fr\"));we||v(\"Received state for an unknown widget: \".concat(n.widgetId),3078),vr(\"\".concat(i,\"-fr\"));break}case\"tokenExpired\":{var ae,Ee=n.token;r.isExpired=!0,(ae=r.cbExpired)===null||ae===void 0||ae.call(r,Ee),r.params[\"refresh-expired\"]===J.Auto&&!r.isResetting&&_(H.AutoExpire,i);break}case\"interactiveTimeout\":{c(r,Y,n.widgetId),Mt(i);var fe=r.cbTimeout;if(fe?fe():r.params[\"refresh-timeout\"]===oe.Never&&!r.isResetting&&x(\"The widget encountered an interactive timeout and is set to never refresh. Consider defining a timeout handler and resetting the widget upon timeout as solving a widget in a timed-out state is going to fail.\"),r.params[\"refresh-timeout\"]===oe.Auto&&!r.isResetting){var ne=r.cbAfterInteractive;ne==null||ne(),_(H.AutoTimeout,i)}break}case\"refreshRequest\":{c(r,Y,n.widgetId),_(H.ManualRefresh,i);break}case\"reloadRequest\":{c(r,n.nextRcV,n.widgetId),_(n.trigger,i);break}case\"interactiveBegin\":{var Te,Ne=r.shadow.getElementById(i);Ne||v(\"Cannot layout widget, Element not found (#\".concat(i,\").\"),3076),(Te=r.cbBeforeInteractive)===null||Te===void 0||Te.call(r),r.params.appearance===Q.InteractionOnly&&Lt(Ne,r);break}case\"interactiveEnd\":{var Re;(Re=r.cbAfterInteractive)===null||Re===void 0||Re.call(r);break}case\"widgetStale\":{r.isStale=!0;break}case\"requestExtraParams\":{var Se;r.widgetParamsStartTimeMs=U();var Ae=r.shadow.querySelector(\"#\".concat(i));Ae||v(\"Received state for an unknown widget: \".concat(n.widgetId),3078),r.isResetting=!1;var ie={},Ze=U(),Ie={\"ht.atrs\":o(document.body.parentNode),pi:{ffp:yr(r.wrapper),ii:window.self!==window.top,lH:window.location.href,mL:document.getElementsByTagName(\"meta\").length,pfp:hr(document,nr,ir),sL:document.scripts.length,sR:r.wrapper.shadowRoot===null,ssL:document.styleSheets.length,t:_r(document.title),tL:document.getElementsByTagName(\"*\").length,wp:gr(r.wrapper),xp:mr(r.wrapper).substring(0,or)},\"w.iW\":window.innerWidth},$=U()-Ze;(Se=Ae.contentWindow)===null||Se===void 0||Se.postMessage(Ve({action:r.action,appearance:r.params.appearance,au:y.scriptUrl,cData:r.cData,ch:\"8359bcf47b68\",chlPageData:r.chlPageData,event:\"extraParams\",execution:r.params.execution,\"expiry-interval\":r.params[\"expiry-interval\"],language:r.params.language,rcV:r.rcV,\"refresh-expired\":r.params[\"refresh-expired\"],\"refresh-timeout\":r.params[\"refresh-timeout\"],retry:r.params.retry,\"retry-interval\":r.params[\"retry-interval\"],source:W,tiefTimeMs:$,timeRenderMs:r.widgetRenderEndTimeMs-r.widgetRenderStartTimeMs,timeToInitMs:r.widgetInitStartTimeMs-r.widgetRenderEndTimeMs,timeToParamsMs:r.widgetParamsStartTimeMs-r.widgetInitStartTimeMs,turnstileAgeMs:U()-y.turnstileLoadInitTimeMs,upgradeAttempts:y.upgradeAttempts,upgradeCompletedCount:y.upgradeCompletedCount,url:Ft(),widgetAgeMs:U()-r.widgetRenderStartTimeMs,widgetId:n.widgetId,wPr:Ie},ie),\"*\"),h(r,n.widgetId,Ae),r.isInitialized=!0;break}default:break}}}};y.msgHandler=N,window.addEventListener(\"message\",N);function R(b){if(typeof b==\"string\"){var n=Xe(b);if(n&&y.widgetMap.has(n))return n;if(y.widgetMap.has(b))return b;try{var i=document.querySelector(b);return i?R(i):null}catch(r){return null}}return P(b,Element)?m(b):b||y.widgetMap.size===0?null:y.widgetMap.keys().next().value}var X={};return ot(Ve({},X),{_private:{showFeedback:function(n){var i=R(n);if(i){var r=K(i);if(r){var u=y.widgetMap.get(i);u&&yt(r,u,Ye.Custom)}}}},execute:function(n,i){var r=R(n);if(!r){i===void 0&&v(\"Please provide 2 parameters to execute: container and parameters\",43521);var u=T(n,i);u||v(\"Failed to render widget\",43522),r=u}var d=y.widgetMap.get(r);if(d){s(d,i);var g=K(r);if(d.isExecuting){x(\"Call to execute() on a widget that is already executing (\".concat(g,\"), consider using reset() before execute().\"));return}if(d.isExecuting=!0,d.response){var w;d.isExecuting=!1,x(\"Call to execute() on a widget that was already executed (\".concat(g,\"), execute() will return the previous token obtained. Consider using reset() before execute() to obtain a fresh token.\")),(w=d.cbSuccess)===null||w===void 0||w.call(d,d.response,!1);return}d.isExpired&&x(\"Call to execute on a expired-widget (\".concat(g,\"), consider using reset() before.\")),d.isStale&&(_(H.StaleExecute,g),d.isExecuting=!0),d.msgQueue.push(ce.Execute),d.isExecuted=!0;var E=d.shadow.querySelector(\"#\".concat(g));if(E||(d.isExecuting=!1,v(\"Widget \".concat(g,\" to execute was not found\"),43522)),d.isResetting)return;if(d.isInitialized&&h(d,r,E),d.isInitialized&&d.params.appearance===Q.Execute&&Lt(E,d),d.isExecuting){var S,I=d.shadow.querySelector(\"#\".concat(g));I||v(\"Received state for an unknown widget: \".concat(r),3078),(S=I.contentWindow)===null||S===void 0||S.postMessage({event:\"execute\",source:W,widgetId:r},\"*\")}}},getResponse:function(n){var i;if(typeof n==\"undefined\"){var r=k();if(r){var u,d=y.widgetMap.get(r);return d!=null&&d.isExpired&&x(\"Call to getResponse on a widget that expired, consider refreshing the widget.\"),(u=y.widgetMap.get(r))===null||u===void 0?void 0:u.response}v(\"Could not find a widget\",43794)}var g=R(n);return g||v(\"Could not find widget for provided container\",43778),(i=y.widgetMap.get(g))===null||i===void 0?void 0:i.response},implicitRender:Pt,isExpired:function(n){var i;if(typeof n==\"undefined\"){var r=k();if(r){var u,d;return(d=(u=y.widgetMap.get(r))===null||u===void 0?void 0:u.isExpired)!==null&&d!==void 0?d:!1}v(\"Could not find a widget\",43794)}var g=R(n);g||v(\"Could not find widget for provided container\",43778);var w;return(w=(i=y.widgetMap.get(g))===null||i===void 0?void 0:i.isExpired)!==null&&w!==void 0?w:!1},ready:function(n){if(y.scriptWasLoadedAsync&&(x(\"turnstile.ready() would break if called *before* the Turnstile api.js script is loaded by visitors.\"),v(\"Remove async/defer from the Turnstile api.js script tag before using turnstile.ready().\",3857)),typeof n!=\"function\"&&v('turnstile.ready() expected a \"function\" argument, got \"'.concat(typeof n==\"undefined\"?\"undefined\":F(n),'\"'),3841),y.isReady){n();return}Wt.push(n)},remove:A,render:T,reset:p})}();function Xr(e){var t=e.getAttribute(\"data-sitekey\"),a={sitekey:t},o=e.getAttribute(\"data-tabindex\");o&&(a.tabindex=Number.parseInt(o,10));var c=e.getAttribute(\"data-theme\");c&&(_t(c)?a.theme=c:x('Unknown data-theme value: \"'.concat(c,'\".')));var l=e.getAttribute(\"data-size\");if(l&&(Rt(l)?a.size=l:x('Unknown data-size value: \"'.concat(l,'\".'))),0)var m;var h=e.getAttribute(\"data-action\");typeof h==\"string\"&&(a.action=h);var s=e.getAttribute(\"data-cdata\");typeof s==\"string\"&&(a.cData=s);var p=e.getAttribute(\"data-retry\");p&&(bt(p)?a.retry=p:x('Invalid data-retry value: \"'.concat(p,\", expected either 'never' or 'auto'\\\".\")));var _=e.getAttribute(\"data-retry-interval\");if(_){var A=Number.parseInt(_,10);xt(A)?a[\"retry-interval\"]=A:x('Invalid data-retry-interval value: \"'.concat(_,', expected an integer value > 0 and < 900000\".'))}var T=e.getAttribute(\"data-expiry-interval\");if(T){var k=Number.parseInt(T,10);wt(k)?a[\"expiry-interval\"]=k:x('Invalid data-expiry-interval value: \"'.concat(k,', expected an integer value > 0 and < 360000\".'))}var O=e.getAttribute(\"data-refresh-expired\");O&&(St(O)?a[\"refresh-expired\"]=O:x('Unknown data-refresh-expired value: \"'.concat(O,\", expected either: 'never', 'auto' or 'manual'.\")));var N=e.getAttribute(\"data-refresh-timeout\");N&&(At(N)?a[\"refresh-timeout\"]=N:x('Unknown data-refresh-timeout value: \"'.concat(N,\", expected either: 'never', 'auto' or 'manual'.\")));var R=e.getAttribute(\"data-language\");R&&(It(R)?a.language=R:x('Invalid data-language value: \"'.concat(R,\", expected either: auto, or an ISO 639-1 two-letter language code (e.g. en) or language and country code (e.g. en-US).\")));function X(w){var E=e.getAttribute(w);return E&&window[E]?window[E]:void 0}var b=[\"error-callback\",\"unsupported-callback\",\"callback\",\"expired-callback\",\"timeout-callback\",\"after-interactive-callback\",\"before-interactive-callback\"];b.forEach(function(w){a[w]=X(\"data-\".concat(w))});var n=e.getAttribute(\"data-feedback-enabled\");n?(Er(n)||x('Invalid data-feedback-enabled value: \"'.concat(n,\", expected either: 'true' or 'false'. Value is ignored.\")),a[\"feedback-enabled\"]=n===\"true\"):a[\"feedback-enabled\"]=!0;var i,r=(i=e.getAttribute(\"data-response-field\"))!==null&&i!==void 0?i:\"true\";a[\"response-field\"]=r===\"true\";var u=e.getAttribute(\"data-response-field-name\");u&&(a[\"response-field-name\"]=u);var d=e.getAttribute(\"data-execution\");d&&(Ot(d)?a.execution=d:x('Unknown data-execution value: \"'.concat(d,\", expected either: 'render' or 'execute'.\")));var g=e.getAttribute(\"data-appearance\");return g&&(Ct(g)?a.appearance=g:x('Unknown data-appearance value: \"'.concat(g,\", expected either: 'always', 'execute', or 'interaction-only'.\"))),a}function Yr(){var e=!0;Nt(y,e),y.msgHandler&&window.removeEventListener(\"message\",y.msgHandler),br(window.turnstile,y)}_e=!1,C=pr(),y.scriptWasLoadedAsync=(Qe=C==null?void 0:C.loadedAsync)!==null&&Qe!==void 0?Qe:!1,y.scriptUrl=(Ke=C==null?void 0:C.src)!==null&&Ke!==void 0?Ke:\"undefined\",C!=null&&C.params&&(be=C.params.get(\"compat\"),(be==null?void 0:be.toLowerCase())===\"recaptcha\"?window.grecaptcha?x(\"grecaptcha is already defined. The compatibility layer will not be enabled.\"):(x(\"Compatibility layer enabled.\"),y.isRecaptchaCompatibilityMode=!0,window.grecaptcha=Vt):be!==null&&x('Unknown value for api.js?compat: \"'.concat(be,'\", ignoring.')),C.params.forEach(function(e,t){L([\"onload\",\"compat\",\"_cb\",\"_upgrade\",\"_reload\",\"render\"],t)||x('Unknown parameter passed to api.js: \"?'.concat(t,'=...\", ignoring.'))}),_e=C.params.get(\"_upgrade\")===\"true\",G=C.params.get(\"onload\"),G&&!_e&&setTimeout(function(){typeof window[G]==\"function\"?window[G]():(x(\"Unable to find onload callback '\".concat(G,\"' immediately after loading, expected 'function', got '\").concat(F(window[G]),\"'.\")),setTimeout(function(){typeof window[G]==\"function\"?window[G]():x(\"Unable to find onload callback '\".concat(G,\"' after 1 second, expected 'function', got '\").concat(F(window[G]),\"'.\"))},1e3))},0)),Dt=\"turnstile\"in window,Dt&&!_e?x(\"Turnstile already has been loaded. Was Turnstile imported multiple times?\"):(Dt&&_e&&(xr(window.turnstile,y),kt(y),(C==null||($e=C.params)===null||$e===void 0?void 0:$e.get(\"render\"))!==\"explicit\"&&setTimeout(Pt,0)),window.turnstile=Vt,_e||((C==null||(Je=C.params)===null||Je===void 0?void 0:Je.get(\"render\"))!==\"explicit\"&&Wt.push(Pt),document.readyState===\"complete\"||document.readyState===\"interactive\"?setTimeout(Tr,0):window.addEventListener(\"DOMContentLoaded\",Tr))),Sr=24*60*60*1e3,window.setTimeout(Yr,Sr);var _e,C,Qe,Ke,be,G,Dt,$e,Je,Sr;})();\n", "timestamp": 1753815605.8280344, "type": "response"}, {"url": "https://cdn.keepa.com/languages/cn.json?20250729", "status": 200, "headers": {"access-control-allow-origin": "*", "access-control-expose-headers": "Content-Type, Content-Length, Content-Length-Uncompressed", "age": "319", "cache-control": "public,max-age=31536000,immutable", "cf-cache-status": "HIT", "cf-ray": "966edb6f6cc6d743-NRT", "content-encoding": "br", "content-type": "application/json", "date": "<PERSON><PERSON>, 29 Jul 2025 19:00:05 GMT", "etag": "W/\"2148732602\"", "last-modified": "Mon, 21 Jul 2025 10:41:37 GMT", "server": "cloudflare", "strict-transport-security": "max-age=15552000; includeSubDomains; preload", "vary": "Origin, accept-encoding", "x-content-type-options": "nosniff"}, "content": "{\n  \"_error\": \"发生错误 - 请在几分钟后重试，或者尝试重新载入Keepa。 如果错误仍然存在，请联系**************。 谢谢。\",\n  \"_1\": \"设置\",\n  \"_2\": \"登录\",\n  \"_3\": \"错误\",\n  \"_4\": \"用户名\",\n  \"_5\": \"电子邮件\",\n  \"_6\": \"密码\",\n  \"_7\": \"Web推送\",\n  \"_8\": \"设置\",\n  \"_9\": \"登录／注册\",\n  \"_10\": \"搜索结果\",\n  \"_11\": \"最近访问\",\n  \"_12\": \"促销\",\n  \"_13\": \"追踪\",\n  \"_14\": \"应用\",\n  \"_15\": \"信息\",\n  \"_16\": \"已达到您选择的最后交易。\",\n  \"_17\": \"已达到允许的最大页面数。请使用筛选器。\",\n  \"_18\": \"行\",\n  \"_19\": \"搜索\",\n  \"_20\": \"追踪概览\",\n  \"_21\": \"管理您的账户设置\",\n  \"_22\": \"心愿单\",\n  \"_23\": \"网站特色\",\n  \"_24\": \"评论\",\n  \"_25\": \"新闻\",\n  \"_26\": \"免责声明\",\n  \"_27\": \"您好\",\n  \"_28\": \"Keepa是一个现代化的亚马逊价格跟踪器; 优雅，高效且易用。<br /><br />停留一会儿，开始省钱吧！␣\",\n  \"_29\": \"我们提供\",\n  \"_30\": \"全面的价格历史图表\",\n  \"_31\": \"降价及可购买提醒\",\n  \"_32\": \"浏览器扩展程序\",\n  \"_33\": \"促销，最近的降价的概览\",\n  \"_34\": \"一个庞大且不断更新的商品数据库\",\n  \"_35\": \"移动应用程序\",\n  \"_36\": \"现在可以在Google Play Store和Apple App Store购买。\",\n  \"_37\": \"浏览器扩展程序－一旦安装，在亚马逊的每件商品页面都将会呈现Keepa的价格历史图表\",\n  \"_38\": \"名称\",\n  \"_39\": \"嗨␣\",\n  \"_40\": \"类别\",\n  \"_41\": \"再见，␣\",\n  \"_42\": \"注册\",\n  \"_43\": \"匿名\",\n  \"_44\": \"完成！\",\n  \"_45\": \"正在处理…\",\n  \"_46\": \"错误的用户名或者密码\",\n  \"_47\": \"截至\",\n  \"_48\": \"最少6位字符\",\n  \"_49\": \"电子邮件地址无效\",\n  \"_50\": \"用户名已经存在\",\n  \"_51\": \"全选\",\n  \"_52\": \"您的账户\",\n  \"_53\": \"您还未登录。您可以使用您的用户名和密码进行登录，或者使用您第一次设置追踪请求时我们提供给您的链接。\",\n  \"_54\": \"清除全部\",\n  \"_55\": \"Amazon\",\n  \"_56\": \"语言\",\n  \"_57\": \"当前密码：\",\n  \"_58\": \"新密码：\",\n  \"_59\": \"确认新密码：\",\n  \"_60\": \"确认您的密码（如果您使用了社交账号登录请留空）\",\n  \"_61\": \"删除帐户\",\n  \"_62\": \"通知设置\",\n  \"_63\": \"更改您的密码\",\n  \"_64\": \"保存设置\",\n  \"_65\": \"您所有的商品追踪和账户信息都将不可逆地被删除。\",\n  \"_66\": \"要删除您的帐户吗？\",\n  \"_67\": \"帐户将在(7)内删除。点击取消。\",\n  \"_68\": \"是的，删除我的账户。\",\n  \"_69\": \"您的电子邮件地址\",\n  \"_70\": \"一个新的电子邮件窗口已经打开，使得撰写邮件变得轻而易举。您可以随意使用您喜欢的电子邮件软件，轻松地向我们的支持团队起草邮件。<br><br>如果电子邮件窗口没有自动打开，您仍然可以直接通过 <EMAIL> 与我们联系。\",\n  \"_71\": \"#count# 条评论\",\n  \"_72\": \"评分正在改善\",\n  \"_73\": \"评分下降了\",\n  \"_74\": \"新密码无效（最少6位字符）\",\n  \"_75\": \"旧密码错误\",\n  \"_76\": \"错误的密码\",\n  \"_77\": [\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"],\n  \"_78\": [\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"],\n  \"_79\": [\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\", \"七月\", \"八月\", \"九月\", \"十月\", \"十一月\", \"十二月\"],\n  \"_80\": [\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\", \"七月\", \"八月\", \"九月\", \"十月\", \"十一月\", \"十二月\"],\n  \"_81\": \"当前：␣\",\n  \"_82\": \"显示全部\",\n  \"_83\": \"没有追踪\",\n  \"_84\": \"取消删除 (3)\",\n  \"_85\": \"您没有跟踪任何项目。\",\n  \"_86\": \"Keepa可以为您监控任何亚马逊商品，一旦降价可以发送提醒。 我们将通过电子邮件，Web Push, Telegram 或RSS通知您。无需注册，并且它是免费的！ 转到<a href='#!deals' class='href'>促销</a>部分，或者使用搜索找到您想要追踪的商品！\",\n  \"_87\": \"条件满足\",\n  \"_88\": \"是的!\",\n  \"_89\": \"还没\",\n  \"_90\": \"还未设定电子邮件！\",\n  \"_91\": \"删除跟踪\",\n  \"_92\": \"导出数据\",\n  \"_93\": \"全新品\",\n  \"_94\": \"非全新品\",\n  \"_95\": \"␣或更低\",\n  \"_96\": \"追踪监控\",\n  \"_97\": \"价格历史\",\n  \"_98\": \"更新期望价格\",\n  \"_99\": \"显示\",\n  \"_100\": \"删除追踪\",\n  \"_101\": \"更新您的期望价格：\",\n  \"_102\": \"确认更新\",\n  \"_103\": \"发送通知通过：\",\n  \"_104\": \"（编辑通知ID）\",\n  \"_105\": \"无效价格\",\n  \"_106\": \"该商品目前在亚马逊上不可购买。 您想在其再次可购买时收到通知吗？␣\",\n  \"_107\": \"选择您的期望价格：\",\n  \"_108\": \"您可以分别追踪亚马逊和第三方卖家的全新品和非全新品\",\n  \"_109\": \"您的通知方式（可选的）：\",\n  \"_110\": \"开始追踪\",\n  \"_111\": \"已更新\",\n  \"_112\": \"刚刚\",\n  \"_113\": \"1分钟前\",\n  \"_114\": \"&nbsp;分钟&nbsp;前\",\n  \"_115\": \"1小时前\",\n  \"_116\": \" 小时前\",\n  \"_117\": \"1天前\",\n  \"_118\": \" 天前 \",\n  \"_119\": \"1个月前\",\n  \"_120\": \" 个月前\",\n  \"_121\": \"商品未找到\",\n  \"_122\": \"通过 \",\n  \"_123\": \"上次更新：\",\n  \"_124\": \"亚马逊价格历史\",\n  \"_125\": \"追踪商品\",\n  \"_126\": \"更多信息\",\n  \"_127\": \"品牌\",\n  \"_128\": \"制造商\",\n  \"_129\": \"标签： \",\n  \"_130\": \"艺术家： \",\n  \"_131\": \"出版商： \",\n  \"_132\": \"按类别查找相似的项目\",\n  \"_133\": \"配件\",\n  \"_134\": \"无货\",\n  \"_135\": \"范围\",\n  \"_136\": \"天\",\n  \"_137\": \"周\",\n  \"_138\": \"2周\",\n  \"_139\": \"月\",\n  \"_140\": \"3月\",\n  \"_141\": \"年\",\n  \"_142\": \"全部\",\n  \"_143\": \"天\",\n  \"_144\": \"特写视图\",\n  \"_145\": \"关闭\",\n  \"_146\": \"打开\",\n  \"_147\": \"Keepa 可以跟踪任何亚马逊产品，并在价格发生变化时向您发送提醒。通过电子邮件、网络推送、Telegram 或 RSS 接收通知 - 无需注册！\",\n  \"_148\": \"全新品\",\n  \"_149\": \"非全新品\",\n  \"_150\": \"Amazon和Amazon徽标是Amazon.com, Inc.或其附属公司的商标。<br><br>显示的产品价格在所示日期/时间内是准确的，价格和供应情况可能会发生变化。购买时在Amazon上显示的任何价格和可用性信息将适用于购买此产品。<br><br>本网站包含我们可能会获得补偿的联盟链接。\",\n  \"_151\": \"此网站上显示的某些内容来自亚马逊服务有限责任公司。 此内容按“原样”提供，并可能随时会更改或删除\",\n  \"_152\": \"特征\",\n  \"_153\": \"管理您关注的商品\",\n  \"_154\": \"最高#days#天\",\n  \"_155\": \"今天\",\n  \"_156\": \"自从我们开始追踪以来，该产品从未有过库存。\",\n  \"_157\": \"选择您的亚马逊地区\",\n  \"_158\": \"选择语言\",\n  \"_159\": \"搜索商品\",\n  \"_160\": \"或者用以下方式注册\",\n  \"_161\": \"或者用以下方式登录\",\n  \"_162\": \"抱歉，没有找到此价格的历史数据。\",\n  \"_163\": \"Specify your desired price change in percent.\",\n  \"_164\": \"过滤极端价格\",\n  \"_165\": \"服务条款\",\n  \"_166\": \"本网站提供免费服务，因此我们不对服务或正常运行时间提供任何担保或保证。严禁任何自动化使用我们的服务，包括使用机器人、脚本、抓取工具、爬虫或类似工具。发现使用自动化的人员将被立即永久封禁。您不得对本网站或其服务进行逆向工程、反编译、反汇编、解码，或以任何其他方式试图获取或访问源代码、内部数据结构、内部应用程序编程接口（API）或任何其他非公开功能或底层技术，无论是通过调试工具、数据包捕获方法还是其他手段。任何此类尝试均构成对本条款的重大违反，并将导致您被立即永久禁止访问。Keepa 保留在任何时间和不经通知的情况下对本服务条款进行更改和改进的权利。通过使用本网站，您确认已阅读并同意这些条款，并愿意自行承担风险。\",\n  \"_167\": \"Top\",\n  \"_168\": \"隐私政策\",\n  \"_169\": \"您可以使用我们的所有服务，而无需提供任何个人信息。但是，如果您选择提供个人信息，请放心，我们在任何情况下都不会出售或交易您的信息。通过在我们网站上设置跟踪请求，您同意我们通过您提供的联系信息与您联系。我们将努力只在必要和有用的时候联系您，因为我们和您一样讨厌垃圾邮件。如果您订阅我们的收费服务，我们将收集并与我们的支付提供商共享您的电子邮件地址和账单信息。这些信息仅用于处理支付、与支付相关事宜的沟通以及认证目的。我们的支付提供商有合同义务保护您的信息，并仅用于披露的目的。如果您通过电子邮件与我们联系，我们使用第三方电子邮件服务提供商来管理我们的电子邮件通讯。该提供商有合同义务保护您的信息，并仅用于预期的目的。您可以通过设置删除您的帐户来删除您的所有个人信息。\",\n  \"_170\": \"联系\",\n  \"_171\": \"联系表格\",\n  \"_172\": \"您的留言\",\n  \"_173\": \"激活通知通过 \",\n  \"_174\": \"停用通知通过 \",\n  \"_175\": \"您确定要更改ID吗？<br />这样做会将所有现有的价格关注更新至新ID。\",\n  \"_176\": \"请使用搜索栏进行搜索\",\n  \"_177\": \"过滤器\",\n  \"_178\": \"排序\",\n  \"_179\": \"视图\",\n  \"_180\": \"排序依据\",\n  \"_181\": \"显示更多\",\n  \"_182\": \"应用过滤器\",\n  \"_183\": \"设置默认视图\",\n  \"_184\": \"导入公开的亚马逊心愿单\",\n  \"_185\": \"电子邮件、Web Push、Telegram和RSS\",\n  \"_186\": \"亚马逊地区支持\",\n  \"_187\": \"注册可选\",\n  \"_188\": \"心愿单导入\",\n  \"_189\": \"监控<span id='productCount'></span>商品：\",\n  \"_190\": \"信息已发送。感谢您！\",\n  \"_191\": \"通过您的同步加载项\",\n  \"_192\": \"或登录为 \",\n  \"_193\": \"关闭商品浮动价格表\",\n  \"_194\": \"针对Firefox和Chrome浏览器进行了优化\",\n  \"_195\": \"很抱歉，此选择中找不到任何商品。 请尝试重新设置。\",\n  \"_196\": \"回到顶端\",\n  \"_197\": \"开始追踪\",\n  \"_198\": \"与期望价格的差价\",\n  \"_199\": \"自上次访问以来的降价\",\n  \"_200\": \"\",\n  \"_201\": \"保存为默认\",\n  \"_202\": \"分类\",\n  \"_203\": \"亚马逊地区\",\n  \"_204\": \"达到期望价格\",\n  \"_205\": \"特定商家\",\n  \"_206\": \"自上次访问后价格有变动\",\n  \"_207\": \"自上次访问后价格无变动\",\n  \"_208\": \"自商品添加后价格有变动\",\n  \"_209\": \"自商品追踪后价格无变动\",\n  \"_210\": \"过去12个月的积极评价\",\n  \"_211\": \"优惠券\",\n  \"_212\": \"含。\",\n  \"_213\": \"您指定的网址不包含有效的亚马逊国家/地区 ID。\",\n  \"_214\": \"愿望清单链接\",\n  \"_215\": \"商品目前无货\",\n  \"_216\": \"您指定的网址不包含有效的ASIN（亚马逊标准识别编号）\",\n  \"_217\": \"刷新商品信息\",\n  \"_218\": \"亚马逊心愿单页面\",\n  \"_219\": \"您可以在您的亚马逊心愿单上创建所有商品的价格关注（现有价格关注不会被覆盖）。 此过程非常简单，仅需两步。\",\n  \"_220\": \"打开 \",\n  \"_221\": \" 并在左侧导航栏中点击您想要导入的亚马逊心愿单的名称\",\n  \"_222\": \"完成后，您应当在当前地址栏中看到类似于<span style ='font-family：monospace'> ... / wishlist / 1T2H3I4586FGG / ... </ span>的链接网址。 现在只需将整个网址复制到此文本框：\",\n  \"_223\": \"列表导入成功！\",\n  \"_224\": \"如果您的心愿单很庞大（超多心愿！），此过程这可能需要一分钟。 请耐心等待。\",\n  \"_225\": \"提交\",\n  \"_226\": \"很抱歉，您输入的内容既不是亚马逊心愿单网址也不是心愿单编号。\",\n  \"_227\": \"对不起，该搜索在您的亚马逊心愿单上返回了0个项目。 您的心愿单是空的或您输入的是一个私人的亚马逊心愿单。 如果这样的话，我们不能访问它。 您可以将其设为公开访问，也可以将其隐私设置设为“共享”。 心愿单编号为 \",\n  \"_228\": \"内\",\n  \"_229\": \"找到 \",\n  \"_230\": \" 个条目。 继续设置您的追踪选项。\",\n  \"_231\": \"这些设置适用于列表中的所有项目。 您可以稍后通过&nbsp;追踪&nbsp;>&nbsp;概览来设置单件商品。\",\n  \"_232\": \"例如\",\n  \"_233\": \"列表中商品的价值 \",\n  \"_234\": \"如果您指定3％的阈值，我们会在价格下降到低于此值时通知您 \",\n  \"_235\": \"\",\n  \"_236\": \"新密码和确认密码不匹配\",\n  \"_237\": \"抱歉，您搜索的“\",\n  \"_238\": \"”没有返回任何结果。请尝试用更少的特定关键词再次搜索\",\n  \"_239\": \"管理价格关注\",\n  \"_240\": \"Keepa目前没有追踪此商品。 不过，您可以点击“追踪此商品”，我们将立即开始追踪！ <br />（请务必先选择此商品的正确的尺寸/颜色/版本）\",\n  \"_241\": \"设置清除间隔\",\n  \"_242\": \"已访问的优惠设置\",\n  \"_243\": \"设置几天后访问的优惠不再自动标记为已访问：\",\n  \"_244\": \"天\",\n  \"_245\": \"已访问的优惠的不透明度：\",\n  \"_246\": \"您已打开或标记为已访问的交易信息存储在您的浏览器中，而不是在您的账户中。\",\n  \"_247\": \"当前选择的交易API查询\",\n  \"_248\": \"如果您拥有Keepa API访问权限，您可以使用以下JSON来通过我们的API<a href=\\\"https://discuss.keepa.com/t/browsing-deals/338\\\">浏览交易</a>：\",\n  \"_249\": \"完整的API网址：\",\n  \"_250\": \"未找到匹配的子类别。\",\n  \"_251\": \"选择交易子类别\",\n  \"_252\": \"图表外观\",\n  \"_253\": \"图表用来显示\",\n  \"_254\": \"是\",\n  \"_255\": \"否\",\n  \"_256\": \"注意：亚马逊将每个愿望清单可以导入的物品限制改为10件。如果你想导入更多物品，你必须使用多个愿望清单。最好的选择是使用我们的 <a href='#!addon' class='href'>浏览器扩展</a>，通过Keepa直接从亚马逊添加物品进行追踪--而不是（或补充）使用亚马逊愿望清单。\",\n  \"_257\": \"日期标签格式\",\n  \"_258\": \"追踪设置\",\n  \"_259\": \"期望价格折扣预设值\",\n  \"_260\": \"介于1和100之间的数字\",\n  \"_261\": \"自动填写以下价格类型的所需价格\",\n  \"_262\": \"附加设置\",\n  \"_263\": \"Keepa Box垂直高度\",\n  \"_264\": \"介于170和500之间的数字\",\n  \"_265\": \"Keepa Box水平高度\",\n  \"_266\": \"动态\",\n  \"_267\": \"整个窗口宽度\",\n  \"_268\": \"有亚马逊优惠\",\n  \"_269\": \"没有亚马逊优惠\",\n  \"_270\": \"是Prime专享（测试版）\",\n  \"_271\": \"默认视图\",\n  \"_272\": \"Keepa Box\",\n  \"_273\": \"按钮\",\n  \"_274\": \"显示桌面通知\",\n  \"_275\": \"显示示例\",\n  \"_276\": \"工作正在进行中！请在评论部分提供反馈。\",\n  \"_277\": \"测试版\",\n  \"_278\": \"重置设置为默认值\",\n  \"_279\": \"设置将会在刷新后生效！\",\n  \"_280\": \"产品标题搜索结果：\",\n  \"_281\": \"清除搜索参数\",\n  \"_282\": \"给予我们反馈！\",\n  \"_283\": \"设定的数字必须介于\",\n  \"_284\": \" 和 \",\n  \"_285\": \"结果数：\",\n  \"_286\": \"设置已经恢复为默认值！\",\n  \"_287\": \"我们正在为您追踪此商品。 您可以在您的 \",\n  \"_288\": \"页面上查看和更新其状态。\",\n  \"_289\": \"由于您使用的是匿名帐户，请将以下私人访问链接添加为书签，以便将来访问您的追踪概述页面\",\n  \"_290\": \"您可以在我们的浏览器扩展的帮助下在亚马逊上使用我们所有的功能。 太好了！\",\n  \"_291\": \"点击浏览器图标了解更多。\",\n  \"_292\": \"页面\",\n  \"_293\": \"电子邮件\",\n  \"_294\": \"无上下文的分类名称。\",\n  \"_295\": \"Amazon 使用的分类节点 ID，代表该分类的标识符。\",\n  \"_296\": \"该分类下预估的商品数量。\",\n  \"_297\": \"无论你想要做什么，复制此链接，仅限个人使用它来显示图表。\",\n  \"_298\": \"每个商品的权重相同，与价格无关。正值表示典型商品变便宜了；负值表示变贵了。\",\n  \"_299\": \"突出该类别中的整体价格变动，与单个商品的价格水平无关。正 = 降价；负 = 涨价。\",\n  \"_300\": \"反映了亚马逊在该类别中的短期定价行为。正 = 降价；负 = 涨价。\",\n  \"_301\": \"显示亚马逊随着时间推移在该类别层面上如何调整价格。正 = 降价；负 = 涨价。\",\n  \"_302\": \"在该类别中所列产品中观察到的最高（根类别）#salesRank#。\",\n  \"_303\": \"在该类别中所列产品中观察到的最低（根类别）#salesRank#。\",\n  \"_304\": \"注意：这是来自 Keepa 产品数据库的估计值，并非直接从 Amazon 获取。\",\n  \"_305\": \"注意：该值来自 Keepa 产品数据库，并非直接从 Amazon 获取。\",\n  \"_306\": \"<code>websiteDisplayGroup</code>，适用于大多数根类别。\",\n  \"_307\": \"相关类别\",\n  \"_308\": \"一个包含类别ID的列表，表示该类别下的产品常常被同时列入的其他类别，按出现频率降序排列。\",\n  \"_309\": \"热门品牌\",\n  \"_310\": \"一个包含该类别中最多3个出现频率最高品牌名称的列表。品牌按出现频率降序排列（最频繁的在前）。如果该类别中唯一品牌少于3个，则列表元素相应减少。\",\n  \"_311\": \"评价最多的格式\",\n  \"_312\": \"评论最多的格式\",\n  \"_313\": \"分享图表\",\n  \"_314\": \"国际配送信息\",\n  \"_315\": \"比较国际亚马逊价格\",\n  \"_316\": \"商品未在其他亚马逊地区找到\",\n  \"_317\": \"比较在不同亚马逊地区的价格\",\n  \"_318\": \"也许亚马逊在不同的国家销售您的商品更便宜？<br>在这里找出来，为您省钱！ 虽然这些价格不包括运费，亚马逊为许多类别的商品提供了廉价的国际配送。\",\n  \"_319\": \"Keepa是什么？\",\n  \"_320\": \"Keepa保存亚马逊上所有商品的价格历史，亚马逊是世界上最大和最可信的在线商家。 用户可以单独追踪他们感兴趣的商品的价格走势，Keepa将在价格达到预定阈值时通知他们。 除了这个跟踪功能，Keepa利用清晰和精心布局的Keepa接口还可以用于浏览亚马逊的全系列产品，还有强大的便于省钱的搜索功能，比如每日降价。 来试试吧！\",\n  \"_321\": \"哪些条目可以追踪？\",\n  \"_322\": \"除了受到严格营销法规限制的产品外，Amazon 上可用的所有产品都可以跟踪。\",\n  \"_323\": \"支持哪些亚马逊商店？\",\n  \"_324\": \"Keepa正在追踪美国，德国，英国，法国，意大利，西班牙，加拿大，日本，印度，墨西哥，中国和巴西的亚马逊产品。 您可以从标题菜单中选择您所需的区域。 您还可以选择站点语言，这和亚马逊区域设置是相互独立的。\",\n  \"_325\": \"必须要创建一个帐户才能使用Keepa吗？\",\n  \"_326\": \"不，如果您想的话，不注册也可以使用Keepa。 然而，如果您决定注册一个帐户，您会获得很多好处。 这样做，您可以方便地管理您的商品追踪，提醒偏好等；当然，注册是完全免费的。\",\n  \"_327\": \"价格靠谱么？\",\n  \"_328\": \"我们的数据保持更新，通常是最新的。 然而，价格时刻波动，因此理论上我们的展示价格有时可能稍微延迟。 因此，在您实际完成购买之前，仔细检查亚马逊的价格会是个明智的决定。\",\n  \"_329\": \"数据多久更新？\",\n  \"_330\": \"更新频率是动态的，根据所关注的商品的一般兴趣而变化。 如果某人正在跟踪产品，则该产品将每小时至少更新一次。 大多数其他的每天更新几次。\",\n  \"_331\": \"提醒是什么？\",\n  \"_332\": \"警报是Keepa发出的关于你所追踪的产品的通知，例如，当某样东西恢复库存或价格达到某个阈值时。只有当你特别要求时，你才会收到这些信息。我们可以通过电子邮件、Telegram Messenger、网络推送或我们的iOS和Android应用程序（以您的喜好为准）来发送这些信息。\",\n  \"_333\": \"如何导入亚马逊心愿单？\",\n  \"_334\": \"导入您的亚马逊心愿单是开始追踪您所感兴趣的许多产品而不是单独追踪每件商品的非常有效的方法。 为此，请点击主菜单中的“追踪”，然后选择“心愿单导入”功能。 可以在那儿找到更进一步的说明。\",\n  \"_335\": \"什么是Keepa应用程序？\",\n  \"_336\": \"出于使用便利，Keepa提供了Chrome和Firefox浏览器扩展。 安装后，您无需访问Keepa网站就可以在每个亚马逊商品页面上直接查看我们的价格历史图表。 您可以在主菜单中的“应用程序”下找到它们。\",\n  \"_337\": \"销售排行图&emsp;有效值：1（绘制），0（不绘制）&emsp;默认值：0\",\n  \"_338\": \"总结果中\",\n  \"_339\": \"如果我有问题或想反馈怎么办？\",\n  \"_340\": \"请随时通过**************与我们联系，提出您可能有的任何建议、意见或问题，或者直接在我们的网站上使用<a href='#?comment'>意见功能</a>。\",\n  \"_341\": \"✜Keepa：亚马逊不提供此商品的任何数据。 就目前，Keepa无法跟踪它。\",\n  \"_342\": \"期望&nbsp;价格\",\n  \"_343\": \"差价\",\n  \"_344\": \"目前\",\n  \"_345\": \"是否为 Amazon Renewed\",\n  \"_346\": \"表示该商品是否经过专业翻新，<br>并由 Amazon 认证可像新的一样运行，通常价格更低。\",\n  \"_347\": \"您的子集\",\n  \"_348\": \"排除分类\",\n  \"_349\": \"Keepa 可以为您在多个 Amazon 地区跟踪此产品。我们不能保证该产品在所有地区都可发货。\",\n  \"_350\": \"追踪多个亚马逊区域？\",\n  \"_351\": \"您可以追踪多个亚马逊区域\",\n  \"_352\": \"美洲\",\n  \"_353\": \"欧洲\",\n  \"_354\": \"亚洲\",\n  \"_355\": \"期望价格\",\n  \"_356\": \"目前价格在\",\n  \"_357\": \"继续您在亚马逊上的搜索\",\n  \"_358\": \"欢迎来到Keepa.com\",\n  \"_359\": \"您的电子邮件地址成功验证。 您现在可以使用我们所有的服务。\",\n  \"_360\": \"您的电子邮件地址已成功验证。 您可以使用我们所有的服务。\",\n  \"_361\": \"请验证您的电子邮件地址\",\n  \"_362\": \"在您验证电子邮件地址之前，我们不会向您发送任何电子邮件（价格提醒等）。\",\n  \"_363\": \"重新发送验证邮件\",\n  \"_364\": \"您的电子邮件地址：\",\n  \"_365\": \"（更改）\",\n  \"_366\": \"验证邮件已发送。 请检查您的收件箱。\",\n  \"_367\": \"很抱歉，我们无法向[MAIL]发送任何电子邮件。 请输入其他电子邮件地址。\",\n  \"_368\": \"很抱歉，我们封锁了您提供的电子邮件地址。 这可能有多种原因：您将我们的一封电子邮件标记为垃圾邮件，地址未经验证，或者您的电子邮件提供商阻止了我们。 如果您认为这是个错误，请联系**************。\",\n  \"_369\": \"我们已在最近一小时内向您发送验证电子邮件。 请检查您的收件箱（也许我们的电子邮件被标记为垃圾邮件？）。 如果您无法收到我们的电子邮件，请检查您是否提供了正确的电子邮件地址，请联系**************或稍后重试。\",\n  \"_370\": \"您无法验证提供的电子邮件地址过多次。 显然您不能收到我们的电子邮件，请联系**************或尝试其他电子邮件地址。\",\n  \"_371\": \"删除所选追踪\",\n  \"_372\": \"忘记密码？\",\n  \"_373\": \"密码重置\",\n  \"_374\": \"输入与您的Keepa帐户相关联的电子邮件地址\",\n  \"_375\": \"如果您所提供的电子邮件地址与Keepa帐户使用的电子邮件地址相同的话，我们已向您发送了一封电子邮件，其中包含进一步的说明。请检查您的收件箱（或者垃圾邮件文件夹）。\",\n  \"_376\": \"您的密码已成功重置。 您现在可以使用新密码登录。\",\n  \"_377\": \"您的密码重置请求已过期。 请索取新的密码重置邮件。\",\n  \"_378\": \"对所选追踪的操作\",\n  \"_379\": \"删除\",\n  \"_380\": \"中止\",\n  \"_381\": \"您即将删除选定的0个商品追踪 - 继续？\",\n  \"_382\": \"统计\",\n  \"_383\": \"最低\",\n  \"_384\": \"最高\",\n  \"_385\": \"平均\",\n  \"_386\": \"最后X天\",\n  \"_387\": \"操作\",\n  \"_388\": \"清空搜索\",\n  \"_389\": \"找到1个结果。\",\n  \"_390\": \"找到X个结果。\",\n  \"_391\": \"销售排行\",\n  \"_392\": \"登出\",\n  \"_393\": \"导出\",\n  \"_394\": \"您可以以CSV或JSON格式导出0个选定的追踪。\",\n  \"_395\": \"类别销售排名\",\n  \"_396\": \"降价\",\n  \"_397\": \"每月\",\n  \"_398\": \"列表价格\",\n  \"_399\": \"收藏品\",\n  \"_400\": \"翻新品\",\n  \"_401\": \"全新品，第三方由商家负责发货\",\n  \"_402\": \"闪电特价\",\n  \"_403\": \"退换货实惠商品\",\n  \"_404\": \"全新品，第三方由亚马逊负责发货\",\n  \"_405\": \"全新品计数\",\n  \"_406\": \"非全新品计数\",\n  \"_407\": \"翻新品计数\",\n  \"_408\": \"收藏品计数\",\n  \"_409\": \"由制造商、供应商或卖家提供的建议零售价。\",\n  \"_410\": \"加载失败：\",\n  \"_411\": \"允许附加组件收集亚马逊价格以改善我们的价格数据\",\n  \"_412\": \"最近通知\",\n  \"_413\": \"未发现缺货或历史变动。\",\n  \"_414\": \"好的！\",\n  \"_415\": \"不，谢了。\",\n  \"_416\": \"包括国际亚马逊价格\",\n  \"_417\": \"包括缺货和历史变化\",\n  \"_418\": \"将鼠标悬停在亚马逊产品上时，将价格历史图表显示为浮动图\",\n  \"_419\": \"心愿单\",\n  \"_420\": \"找到相关促销\",\n  \"_421\": \"社区追踪\",\n  \"_422\": \"有&nbsp;X个期望&nbsp;价格\",\n  \"_423\": \"您的期望价格\",\n  \"_424\": \"正在跟踪此商品的用户\",\n  \"_425\": \"正在跟踪此商品的用户\",\n  \"_426\": \"只有您正在追踪此商品\",\n  \"_427\": \"全部标记已阅\",\n  \"_428\": \"在您继续操作之前，您必须在Keepa帐户中设置并验证电子邮件地址。 <a href=\\\"#!channels/0\\\">点击此处立即执行。</a>\",\n  \"_429\": \"仅最后一次价格变动\",\n  \"_430\": \"Amazon优惠的预计发货延迟（天）。\",\n  \"_431\": \"接收即将有闪电特价的追踪商品的通知\",\n  \"_432\": \"提醒重整定时器\",\n  \"_433\": \"从未\",\n  \"_434\": \"更新现有追踪\",\n  \"_435\": \"停止追踪此商品\",\n  \"_436\": \"您已经在追踪此商品。\",\n  \"_437\": \"您的价格关注已成功删除，您将不会再收到关于它的任何通知。\",\n  \"_438\": \"在商品页面上显示部分商家的商品库存数量\",\n  \"_439\": \"更新\",\n  \"_440\": \"评分\",\n  \"_441\": \"评价数量\",\n  \"_442\": \"黄金购买框\",\n  \"_443\": \"非全新品，像新品一样 🚚\",\n  \"_444\": \"非全新品，非常好 🚚\",\n  \"_445\": \"非全新品，好 🚚\",\n  \"_446\": \"非全新品，可接受的 🚚\",\n  \"_447\": \"收藏品，像新的一样 🚚\",\n  \"_448\": \"收藏品，非常好 🚚\",\n  \"_449\": \"收藏品，好 🚚\",\n  \"_450\": \"收藏品，可接受的 🚚\",\n  \"_451\": \"翻新品 🚚\",\n  \"_452\": \"选择促销类型\",\n  \"_453\": \"您可以通过在前面加上减号来添加黑名单，就像这样：&quot;-关键词&quot;\",\n  \"_454\": \"搜索促销\",\n  \"_455\": \"选择范围\",\n  \"_456\": \"范围\",\n  \"_457\": \"没有销售排行的商品将会被排除！\",\n  \"_458\": \"限制降价区间\",\n  \"_459\": \"至今最低\",\n  \"_460\": \"只显示此商品所有全新品（亚马逊、第三方全新品）报价中的最低价\",\n  \"_461\": \"最低新报价\",\n  \"_462\": \"再次有货\",\n  \"_463\": \"必须有评论\",\n  \"_464\": \"过滤成人内容\",\n  \"_465\": \"配送包括\",\n  \"_466\": \"将所选重置为默认值\",\n  \"_467\": \"重置所选\",\n  \"_468\": \"清除已访问的促销\",\n  \"_469\": \"显示API请求\",\n  \"_470\": \"显示交易API请求\",\n  \"_471\": \"重新加载优惠\",\n  \"_472\": \"输入销售排名限制，留空表示无限制。 如果设置了数值，将排除所有没有销售排名的商品！\",\n  \"_473\": \"当前排行：#\",\n  \"_474\": \"是至今最低的（最优价）\",\n  \"_475\": \"是当前全新品（亚马逊、第三方全新品）中价格最低的\",\n  \"_476\": \"找到促销：\",\n  \"_477\": \"365天的价格历史\",\n  \"_478\": \"使用当前选择覆盖\",\n  \"_479\": \"重命名\",\n  \"_480\": \"载入书签\",\n  \"_481\": \"编辑书签\",\n  \"_482\": \"您的促销＃ \",\n  \"_483\": \"将选择加入书签\",\n  \"_484\": \"创建此选择的书签\",\n  \"_485\": \"最新的\",\n  \"_486\": \"百分比\",\n  \"_487\": \"降价\",\n  \"_488\": \"价格\",\n  \"_489\": \"降价百分比范围\",\n  \"_490\": \"降价范围\",\n  \"_491\": \"价格范围\",\n  \"_492\": \"最小#\",\n  \"_493\": \"最大#\",\n  \"_494\": \"下面列出的价格是最便宜的退换货实惠促销，可以在亚马逊的非全新品列表页面上找到。\",\n  \"_495\": \"本部分包含Prime配送的第三方商家（由亚马逊负责发货）。<br>下面列出的价格为最低亚马逊发货的价格，可在亚马逊上的商家列表页面上找到。\",\n  \"_496\": \"本部分包含有配送费的第三方商家（由商家负责发货）。<br>下面列出的价格是最低的价格，包括运费，并可以在亚马逊的商家列表页面上找到。\",\n  \"_497\": \"本部分包含黄金购买框卖家，包含运费。<br> 购买框是产品详细信息页面上的框，客户可以将商品添加到购物车中。\",\n  \"_498\": \"交易可能已过期！ \",\n  \"_499\": \"由亚马逊销售和发货。 图表中的间隙是亚马逊没有提供产品的时间段（缺货）。\",\n  \"_500\": \"由第三方卖家或亚马逊提供的全新品最低价格，不包括运费\",\n  \"_501\": \"由第三方卖家或亚马逊（退换货实惠）提供的非全新品最低价格，不包括运费\",\n  \"_502\": \"亚马逊卖家排名\",\n  \"_503\": \"收藏品最低价格，不包括运费\",\n  \"_504\": \"翻新品最低价格，不包括运费\",\n  \"_505\": \"由卖家自己发货的第三方卖家全新品最低价格，包括运费。以方形显示。\",\n  \"_506\": \"由亚马逊负责发货的第三方卖家全新品最低价格，包括运费。以三角形显示。\",\n  \"_507\": \"黄金购买框的价格，包括运费。 购买框是产品详细信息页面上的框，客户可以将商品添加到购物车中。\",\n  \"_508\": \"非全新品最低价格，包括运费。\",\n  \"_509\": \"选择区域以放大。双击以重置。\",\n  \"_510\": \"通过点击图例切换显示的数据。\",\n  \"_511\": \"始终在图表正下方显示“比较亚马逊价格”。\",\n  \"_512\": \"您可以将您的Keepa帐户与外部帐户相关联，以便一键登录。\",\n  \"_513\": \"添加关联：\",\n  \"_514\": \"已经关联：\",\n  \"_515\": \"您用来推送通知的移动设备：\",\n  \"_516\": \"帐户设置\",\n  \"_517\": \"网站设置\",\n  \"_518\": \"移除关联\",\n  \"_519\": \"回顾近期Keepa发送给您的通知\",\n  \"_520\": \"通知的时间\",\n  \"_521\": \"通知以\",\n  \"_522\": \"触发通知的价格类型\",\n  \"_523\": \"原因\",\n  \"_524\": \"通知通过\",\n  \"_525\": \"追踪已过期\",\n  \"_526\": \"第一次到达期望价格\",\n  \"_527\": \"价格变动\",\n  \"_528\": \"期望价格到达后价格已经变动\",\n  \"_529\": \"再次到达期望价格\",\n  \"_530\": \"未知\",\n  \"_531\": \"通知价格\",\n  \"_532\": \"上次价格变动\",\n  \"_533\": \"评论\",\n  \"_534\": \"搜索子类目\",\n  \"_535\": \"您导入的心愿单\",\n  \"_536\": \"将导入的心愿单{NAME}与亚马逊进行同步。 新添加的心愿单项目将被添加到Keepa，已移除的心愿单项目将不再被跟踪。 如果您要更改追踪设置，请重新导入心愿单。\",\n  \"_537\": \"同步心愿单\",\n  \"_538\": \"更新心愿单\",\n  \"_539\": \"添加了\",\n  \"_540\": \"上次更新\",\n  \"_541\": \"配送\",\n  \"_542\": \"最近同步了\",\n  \"_543\": \"项目在列表上\",\n  \"_544\": \"购买了\",\n  \"_545\": \"移除心愿单\",\n  \"_546\": \"在#amazonUrl#上打开评论\",\n  \"_547\": \"这将从Keepa上删除您的心愿单{NAME}\",\n  \"_548\": \"删除所有相关的追踪\",\n  \"_549\": \"保持追踪\",\n  \"_550\": \"出价细节\",\n  \"_551\": \"找到出价\",\n  \"_552\": \"没有描述\",\n  \"_553\": \"状况\",\n  \"_554\": \"卖家\",\n  \"_555\": \"价格类型\",\n  \"_556\": \"库存是\",\n  \"_557\": \"描述\",\n  \"_558\": \"从您的心愿单\",\n  \"_559\": \"{COUNT} 名用户正在使用 Keepa。\",\n  \"_560\": \"即将到来的闪电特价\",\n  \"_561\": \"未验证是否在eBay上销售\",\n  \"_562\": \"我们会通过电子邮件通知您。\",\n  \"_563\": \"eBay 全新 🚚\",\n  \"_564\": \"eBay 二手 🚚\",\n  \"_565\": \"eBay 定价 全新 🚚\",\n  \"_566\": \"eBay 定价 二手 🚚\",\n  \"_567\": \"免费运输\",\n  \"_568\": \"Web 推送通知\",\n  \"_569\": \"<em>在桌面上：</em>只要您的浏览器打开，就会收到通知。<em>在移动设备上：</em>浏览器可以关闭。<br>需要为每个要接收 Web 推送通知的浏览器和设备重复注册。\",\n  \"_570\": \"要发送测试消息，请<span class='settings-chpw' id='testMessage'>单击此处</span>。\",\n  \"_571\": \"您可以随时在帐户设置中更改您的决定。\",\n  \"_572\": \"置换\",\n  \"_573\": \"亚马逊置换价值\",\n  \"_574\": \"桌面推送通知\",\n  \"_575\": \"需要安装我们的浏览器扩展程序，仅在浏览器打开时才能工作。\",\n  \"_576\": \"RSS 订阅\",\n  \"_577\": \"在您的个人 Keepa RSS 订阅中阅读价格警报通知\",\n  \"_578\": \"图表范围（天）：\",\n  \"_579\": \"提示：您的浏览器还支持 Web 推送通知。我们建议激活这些通知。\",\n  \"_580\": \"安装我们的<a href='#!addon'>浏览器扩展程序</a>\",\n  \"_581\": \"<a href='#!addon'>浏览器扩展程序</a>已安装\",\n  \"_582\": \"租赁\",\n  \"_583\": \"租金\",\n  \"_584\": \"在任何支持的 Telegram Messenger 平台上立即接收价格警报。\",\n  \"_585\": \"通过 Telegram Messenger 连接\",\n  \"_586\": \"管理价格警报的通知渠道。您可以单独切换每个通道的 <i class='fa fa-toggle-on fa-fw fa-lg'></i> 或 <i class='fa fa-toggle-off fa-fw fa-lg'></i>。\",\n  \"_587\": \"通知设置\",\n  \"_588\": \"以访客身份继续\",\n  \"_589\": \"图像\",\n  \"_590\": \"标题\",\n  \"_591\": \"颜色\",\n  \"_592\": \"尺寸\",\n  \"_593\": \"版本\",\n  \"_594\": \"平台\",\n  \"_595\": \"格式\",\n  \"_596\": \"清除过滤器\",\n  \"_597\": \"包含\",\n  \"_598\": \"不相等\",\n  \"_599\": \"相等\",\n  \"_600\": \"正在加载...\",\n  \"_601\": \"大于\",\n  \"_602\": \"大于或等于\",\n  \"_603\": \"在范围内\",\n  \"_604\": \"小于\",\n  \"_605\": \"小于或等于\",\n  \"_606\": \"无行可显示\",\n  \"_607\": \"不包含\",\n  \"_608\": \"不相等\",\n  \"_609\": \"以...开头\",\n  \"_610\": \"第一\",\n  \"_611\": \"最后\",\n  \"_612\": \"下一页\",\n  \"_613\": \"的\",\n  \"_614\": \"上一页\",\n  \"_615\": \"至\",\n  \"_616\": \"唯一 ID\",\n  \"_617\": \"此报价的唯一 ID（在产品范围内）。与亚马逊使用的报价 ID 无关\",\n  \"_618\": \"卖家 ID\",\n  \"_619\": \"Prime\",\n  \"_620\": \"此报价是否可通过 Prime 发货。\",\n  \"_621\": \"可发货\",\n  \"_622\": \"隐藏的\",\n  \"_623\": \"如果由于最低广告价格（\\\"MAP\\\"）限制而在 Amazon 上隐藏此报价的价格。\",\n  \"_624\": \"产品图片\",\n  \"_625\": \"预订\",\n  \"_626\": \"仓库交易\",\n  \"_627\": \"潜在骗局\",\n  \"_628\": \"Keepa 认为这个报价是一个潜在的骗局。在从该卖家购买之前，请查看报价和卖家的评论。\",\n  \"_629\": \"由 Amazon 出售\",\n  \"_630\": \"如果卖家是亚马逊（例如\\\"Amazon.com\\\"），则为 true。注意：Amazon 的仓库交易卖家帐户或其他由 Amazon 以不同名称维护的帐户不被视为亚马逊。\",\n  \"_631\": \"Prime 专属\",\n  \"_632\": \"库存\",\n  \"_633\": \"运费\",\n  \"_634\": \"总价（价格+运费）\",\n  \"_635\": \"最后查看\",\n  \"_636\": \"首次查看\",\n  \"_637\": \"非全新品 - 像新品一样\",\n  \"_638\": \"非全新品 - 非常好\",\n  \"_639\": \"非全新品 - 好\",\n  \"_640\": \"非全新品 - 可以\",\n  \"_641\": \"收藏品 - 像新品一样\",\n  \"_642\": \"收藏品 - 非常好\",\n  \"_643\": \"收藏 - 良好\",\n  \"_644\": \"收藏 - 可接受\",\n  \"_645\": \"翻新\",\n  \"_646\": \"显示在\",\n  \"_647\": \"订阅\",\n  \"_648\": \"起价\",\n  \"_649\": \"上一个闪购\",\n  \"_650\": \"的价格为\",\n  \"_651\": \"了解更多，请访问 Vimeo\",\n  \"_652\": \"\",\n  \"_653\": \"具有圆角输入字段的价格类型更新频率较低。\",\n  \"_654\": \"选择要在叠加中绘制的图形\",\n  \"_655\": \"租赁\",\n  \"_656\": \"仅限同一地区\",\n  \"_657\": \"通过限制要检索的语言环境来减少加载时间：\",\n  \"_658\": \"未找到 \\\"{NAME}\\\" 类别的产品。\",\n  \"_659\": \"售出估计仅基于库存变化。如果存在每个客户的限制，则无法准确确定库存。并非所有库存更改都会记录。\",\n  \"_660\": \"查看为\",\n  \"_661\": \"表格\",\n  \"_662\": \"产品盒子\",\n  \"_663\": \"大洋洲\",\n  \"_664\": \"北美\",\n  \"_665\": \"南美洲\",\n  \"_666\": \"无价格\",\n  \"_667\": \"单击任何列标题以获取其他排序选项。\",\n  \"_668\": \"活动跟踪\",\n  \"_669\": \"无愿望清单\",\n  \"_670\": \"在产品查看器中打开\",\n  \"_671\": \"全选 / 取消全选\",\n  \"_672\": \"单击以加载亚马逊的历史记录图形\",\n  \"_673\": \"当前活动历史记录图形\",\n  \"_674\": \"主跟踪域ID\",\n  \"_675\": \"<i class='fa fa-fw fa-eye'></i> #current# / #total# 件商品\",\n  \"_676\": \"产品标题\",\n  \"_677\": \"复制\",\n  \"_678\": \"带标题复制\",\n  \"_679\": \"Ctrl+C\",\n  \"_680\": \"重置列\",\n  \"_681\": \"更新跟踪\",\n  \"_682\": \"自动调整所有列\",\n  \"_683\": \"工具\",\n  \"_684\": \"重置过滤器和搜索\",\n  \"_685\": \"货币\",\n  \"_686\": \"您可以以CSV格式导出0个选定的追踪。\",\n  \"_687\": \"已删除\",\n  \"_688\": \"我们的<a target='_blank' href='https://discuss.keepa.com/t/new-tracking-overview/5844'>跟踪概览</a>正在进行大规模升级。<span id='switch-manage'>现在就试用并切换吧！</span>\",\n  \"_689\": \"欢迎使用我们的新<a target='_blank' href='https://discuss.keepa.com/t/new-tracking-overview/5844'>跟踪概览</a>！\",\n  \"_690\": \"如果您发现任何问题，请<a target='_blank' href='https://keepa.com/#!discuss/c/bug-reports'>报告</a>。\",\n  \"_691\": \"错误的卖家 ID 格式。请输入由 9 到 21 个大写字母或数字组成的值。\",\n  \"_692\": \"图表\",\n  \"_693\": \"默认\",\n  \"_694\": \"舒适\",\n  \"_695\": \"紧凑\",\n  \"_696\": \"仅显示跟踪数据的图表\",\n  \"_697\": \"显示完整图表\",\n  \"_698\": \"显示小型图像\",\n  \"_699\": \"打开Amazon的产品详情\",\n  \"_700\": \"在过滤之前，所有值都会在内部转换为 #currency#。\",\n  \"_701\": \"自上次访问以来的变化\",\n  \"_702\": \"自开始跟踪以来的变化\",\n  \"_703\": \"或更多\",\n  \"_704\": \"要跟踪价格上涨，需要订阅月度<br>数据访问订阅。\",\n  \"_705\": \"要跟踪价格上涨，需要注册帐户，并订阅月度<br>数据访问订阅。\",\n  \"_706\": \"您可以在此处订阅<a class='href' href='https://keepa.com/#!settings/1'>。</a>\",\n  \"_707\": \"自动同步愿望清单\",\n  \"_708\": \"您已为此产品设置了价格增长跟踪，<span class='switch-manage-trigger' style='cursor:pointer;text-decoration:underline;'>请使用新的管理概览查看所有详细信息。</span>旧视图将很快被弃用，并且不支持新的跟踪类型。\",\n  \"_709\": \"由于缺少数据访问订阅，增长跟踪已被停用。\",\n  \"_710\": \"跟踪类型\",\n  \"_711\": \"降价\",\n  \"_712\": \"涨价\",\n  \"_713\": \"一旦已跟踪产品的闪购开始，即可收到通知。\",\n  \"_714\": \"闪购通知仅通过电子邮件渠道发送。其他渠道可能会跟进。\",\n  \"_715\": \"启用后，您只会收到已启用闪电促销提醒的跟踪通知，因此不会收到不感兴趣商品的更新。所有新的跟踪项将默认启用闪电促销提醒。<br>关闭此设置可禁用所有闪电促销提醒，而不受个别跟踪设置的影响。<br>注意：这不会影响针对特定目标价格的闪电促销价格监控通知。\",\n  \"_716\": \"默认视图模式仅显示跟踪数据。单击左侧的设置工具以切换到舒适视图模式，以访问完整的价格图形。\",\n  \"_717\": \"了解详情\",\n  \"_718\": \"价格历史图表\",\n  \"_719\": \"超过50亿个亚马逊产品的详细价格历史记录图表。\",\n  \"_720\": \"前往Firefox附加组件\",\n  \"_721\": \"前往Chrome网络应用商店\",\n  \"_722\": \"我们的Chrome扩展程序与Opera兼容\",\n  \"_723\": \"在Windows 上安装Edge\",\n  \"_724\": \"在移动设备上，仅适用于<em>Firefox</em>和Android版<em>Microsoft Edge</em>。\",\n  \"_725\": \"直接从产品页面设置价格监视。我们为您跟踪任何产品，并在产品价格低于您期望的价格时通知您。\",\n  \"_726\": \"价格下降和可用性警报\",\n  \"_727\": \"比较和跟踪国际亚马逊价格。<br>列出了所有支持的亚马逊商店的价格，并可以轻松跟踪。\",\n  \"_728\": \"每日特惠\",\n  \"_729\": \"最近价格下降的概述。<br>找到真正的特价。Keepa每天找到最佳交易。在您最喜欢的产品类别中查找价格最大降幅的产品。\",\n  \"_730\": \"试用一下\",\n  \"_731\": \"国际亚马逊价格\",\n  \"_732\": \"最近价格下降的概述。\",\n  \"_733\": \"阅读完整公告\",\n  \"_734\": \"高级数据访问\",\n  \"_735\": \"您是否已经了解我们的高级数据功能？\",\n  \"_736\": \"产品搜索工具\",\n  \"_737\": \"通过搜索我们的整个产品数据库，查找符合任何标准的产品！\",\n  \"_738\": \"产品查看器\",\n  \"_739\": \"导入和导出大量产品列表，以一目了然地查看所有价格和产品详细信息。\",\n  \"_740\": \"畅销商品和热门商品列表\",\n  \"_741\": \"按类别查看最畅销的产品和商家。\",\n  \"_742\": \"价格增长追踪\",\n  \"_743\": \"设置价格增长警报，并在产品达到您的阈值时收到通知。\",\n  \"_744\": \"要享受我们的 Data Access 功能，需要 <a class='href -data' href='https://keepa.com/#!settings/1'>每月订阅</a>（19 € / 月）\",\n  \"_745\": \"请输入有效值，或将其留空以删除特定阈值。\",\n  \"_746\": \"双击以更新\",\n  \"_747\": \"变体\",\n  \"_748\": \"视频数量\",\n  \"_749\": \"立即获取您的链接！\",\n  \"_750\": \"浏览器扩展程序\",\n  \"_751\": \"数据功能\",\n  \"_752\": \"通过推广Keepa，您可以赚钱！只需将以下您的唯一推荐链接分享给他人。\",\n  \"_753\": \"选择您想要推广的内容：\",\n  \"_754\": \"已复制到剪贴板！\",\n  \"_755\": \"它是如何工作的？\",\n  \"_756\": \"在社交媒体、您的博客或任何您喜欢的地方分享链接！\",\n  \"_757\": \"如果用户点击链接、安装并使用我们的<a href=\\\"https://keepa.com/#!addon\\\" target=\\\"_blank\\\">浏览器扩展程序</a>，您将在您的账户中获得<span>{FEE}</span>的信用。链接将自动重定向到用户浏览器的扩展程序页面。\",\n  \"_758\": \"如果一个被推荐的用户通过您的链接订阅我们的<a href=\\\"https://keepa.com/#!data\\\" target=\\\"_blank\\\" class=\\\"href\\\">数据功能</a>，您将在您的账户中获得<span>{FEE}</span>的信用。\",\n  \"_759\": \"如果一个用户通过您的链接订阅我们的<a href=\\\"https://keepa.com/#!api\\\" target=\\\"_blank\\\">Keepa API</a>，您将在您的账户中获得<span>{FEE}</span>的信用。\",\n  \"_760\": \"您可以随时请求即时付款，无需最低支付门槛。付款可通过预付VISA卡或亚马逊礼品卡接收。\",\n  \"_761\": \"您的推荐统计信息\",\n  \"_762\": \"点击次数\",\n  \"_763\": \"待验证\",\n  \"_764\": \"成功\",\n  \"_765\": \"每次成功推荐{FEE}\",\n  \"_766\": \"您当前的信用额度为：\",\n  \"_767\": \"申请支付\",\n  \"_768\": \"您没有可用于支付的信用额度。\",\n  \"_769\": \"提交后，您将收到一张礼品卡。我们只需要您的地址用于会计目的。\",\n  \"_770\": \"您可以通过此链接兑换礼品卡:\",\n  \"_771\": \"需要更多信息吗？ 请通过<a href=\\\"mailto:<EMAIL>?subject=Question about Affiliate Program\\\" class=\\\"href\\\"><EMAIL></a>的电子邮件与我们联系。\",\n  \"_772\": \"使用我们的联盟计划，即表示您同意以下条款：\",\n  \"_773\": \"在满足资格支付之前，所有引荐均需经过验证。 这个过程可能需要长达一个月，具体取决于引荐类型。\",\n  \"_774\": \"信用余额只能以礼品卡余额的形式支付到Amazon.com帐户。\",\n  \"_775\": \"所述引荐费率以美元表示，随时可能更改，无需事先公告。\",\n  \"_776\": \"获得的信用取决于验证时的引荐费率。\",\n  \"_777\": \"您必须是个人才能参加。 无法向Amazon业务帐户支付。\",\n  \"_778\": \"此程序随时可能终止，无需事先公告。\",\n  \"_779\": \"联盟计划\",\n  \"_780\": \"请求引荐信用支付\",\n  \"_781\": \"个人\",\n  \"_782\": \"商业\",\n  \"_783\": \"不支持。\",\n  \"_784\": \"全名\",\n  \"_785\": \"必填\",\n  \"_786\": \"地址\",\n  \"_787\": \"需要地址\",\n  \"_788\": \"城市\",\n  \"_789\": \"城市是必填项\",\n  \"_790\": \"邮政编码\",\n  \"_791\": \"邮政编码/邮政编码必填\",\n  \"_792\": \"请选择\",\n  \"_793\": \"继续并使用Amazon登录\",\n  \"_794\": \"将信用额度用于\",\n  \"_795\": \"信用额度将直接应用于您登录的Amazon帐户。\",\n  \"_796\": \"您将不会收到礼品卡。我们仅需要您的地址用于会计目的。只有个人才能支付 - 我们不支持Amazon Business帐户。\",\n  \"_797\": \"您的Amazon账户\",\n  \"_798\": \"单击\\\"{SUBMIT}\\\"以继续向此账户支付。关闭弹出窗口以取消。\",\n  \"_799\": \"处理您的支付时出错。请联系<a href=\\\"mailto:<EMAIL>?subject=Question about Affiliate Program\\\"><EMAIL></a>。\",\n  \"_800\": \"您的信用已成功应用于您的亚马逊账户。谢谢！\",\n  \"_801\": \"好的，关闭\",\n  \"_802\": \"以前的支付\",\n  \"_803\": \"日期\",\n  \"_804\": \"应用到\",\n  \"_805\": \"金额\",\n  \"_806\": \"双击进行编辑\",\n  \"_807\": \"标签\",\n  \"_808\": \"备注\",\n  \"_809\": \"无\",\n  \"_810\": \"图表\",\n  \"_811\": \"仅跟踪\",\n  \"_812\": \"自定义\",\n  \"_813\": \"导出带货币符号格式化的价格\",\n  \"_814\": \"导入已购买的产品\",\n  \"_815\": \"覆盖现有的跟踪\",\n  \"_816\": \"即使没有当前的价格类型的报价也要跟踪（一旦价格类型再次有报价，就会收到提醒）\",\n  \"_817\": \"刷新筛选和排序\",\n  \"_818\": \"由亚马逊售出并履行\",\n  \"_819\": \"第三方卖家或亚马逊的新价，不含运费\",\n  \"_820\": \"第三方卖家或亚马逊的二手价（Warehouse），不含运费\",\n  \"_821\": \"收藏品价格，不含运费\",\n  \"_822\": \"翻新价格，不含运费\",\n  \"_823\": \"由第三方卖家以商家履行方式提供的新价，包含运费\",\n  \"_824\": \"由第三方卖家以亚马逊履行方式提供的新价（Prime）\",\n  \"_825\": \"新购买框的价格，包括运费\",\n  \"_826\": \"“用过，像新的”，包括运费。\",\n  \"_827\": \"“用过，非常好的”，包括运费。\",\n  \"_828\": \"“用过，好的”，包括运费。\",\n  \"_829\": \"“用过，可以接受的”，包括运费。\",\n  \"_830\": \"“收藏品，像新的”，包括运费。\",\n  \"_831\": \"“收藏品，非常好的”，包括运费。\",\n  \"_832\": \"“收藏品，好的”，包括运费。\",\n  \"_833\": \"“收藏品，可以接受的”，包括运费。\",\n  \"_834\": \"翻新价格，包括运费\",\n  \"_835\": \"亚马逊交易价格\",\n  \"_836\": \"包括运费\",\n  \"_837\": \"仅限Prime会员\",\n  \"_838\": \"仅限Warehouse Deals\",\n  \"_839\": \"获取闪购即将开始的电子邮件通知\",\n  \"_840\": \"一旦闪购开始（任何价格），即获得通知\",\n  \"_841\": \"销售排行、评分和 #ratingCount#\",\n  \"_842\": \"还有更多...\",\n  \"_843\": \"追踪模式\",\n  \"_844\": \"基础版\",\n  \"_845\": \"高级版\",\n  \"_846\": \"专业版\",\n  \"_847\": \"选择您的追踪模式！<br><i>基础版：</i>  <b>亚马逊</b>、<b>新品</b>和<b>二手</b><br><i>高级版：</i>  <i>基础版</i> + <b>闪电优惠和仓库优惠</b><br><i>专业版：</i>  <i>高级版</i> + <b>销售排名</b>、<b>评分</b>、<b>报价数量</b>等！\",\n  \"_848\": \"启用选定的区域设置\",\n  \"_849\": \"切换以减少追踪\",\n  \"_850\": \"切换以增加追踪\",\n  \"_851\": \"由于缺少价格历史记录（价格类型从未提供），无法提供建议。\",\n  \"_852\": \"由于缺少历史数据，无法提供建议。\",\n  \"_853\": \"数值\",\n  \"_854\": \"折扣\",\n  \"_855\": \"增加\",\n  \"_856\": \"任意\",\n  \"_857\": \"天平均\",\n  \"_858\": \"历史最高\",\n  \"_859\": \"历史最低\",\n  \"_860\": \"期望值\",\n  \"_861\": \"与期望值的差值\",\n  \"_862\": \"触发通知的类型\",\n  \"_863\": \"通知时的值\",\n  \"_864\": \"首次达到期望值\",\n  \"_865\": \"数值已更改\",\n  \"_866\": \"在达到期望值后更改数值\",\n  \"_867\": \"再次达到期望值\",\n  \"_868\": \"通知\",\n  \"_869\": \"即将到来的提醒\",\n  \"_870\": \"开始\",\n  \"_871\": \"你没有登录。 您可以使用您的用户名和密码进行登录，或者使用您第一次设置追踪请求时我们提供给您的链接。\",\n  \"_872\": \"或者，您可以使用我们的<a href='#!addon' class='href'>浏览器扩展程序</a>之一，直接从Amazon的产品详细页面设置价格监控。\",\n  \"_873\": \"您仅为<span>Amazon</span>价格类型设置了期望价格，但该产品从未由Amazon直接销售。<br>您是否还要为<span>新品</span>类型设置期望价格？\",\n  \"_874\": \"跟踪\",\n  \"_875\": \"周\",\n  \"_876\": \"周\",\n  \"_877\": \"月\",\n  \"_878\": \"月\",\n  \"_879\": \"年\",\n  \"_880\": \"年\",\n  \"_881\": \"选择的期间结束后，跟踪将过期并被删除。<br>每次通过跟踪表单更新都会重置该期间。\",\n  \"_882\": \"到期时间\",\n  \"_883\": \"小时\",\n  \"_884\": \"小时\",\n  \"_885\": \"分钟\",\n  \"_886\": \"分钟\",\n  \"_887\": \"天\",\n  \"_888\": \"到期\",\n  \"_889\": \"即将到期！\",\n  \"_890\": \"自动禁用的图表类型通知\",\n  \"_891\": \"查看以下历史数据现在需要我们的<a href='https://keepa.com/#!data' class='href' target='_blank'>Data Access</a>订阅。由于您未订阅，因此以下图表已被禁用：\",\n  \"_892\": \"查看以下历史数据需要我们的<a href='https://keepa.com/#!data' class='href' target='_blank'>Data Access</a>订阅。\",\n  \"_893\": \"只要我们能将一个产品与eBay上的产品匹配，就可以查看其eBay价格历史。\",\n  \"_894\": \"现在订阅\",\n  \"_895\": \"仅适用于我们的数据订阅\",\n  \"_896\": \"高级图表类型\",\n  \"_897\": \"更多细节请参见我们的<a class='href' target='_blank' href='https://discuss.keepa.com/t/new-restricted-graph-types/7415?u=keepa'>公告</a>\",\n  \"_898\": \"注意：您目前至少有一个活动订阅！删除是不可逆转的，我们的订阅是不可退款的。\",\n  \"_899\": \"请再次确认您要删除您的帐户。这无法撤消。\",\n  \"_900\": \"支持\",\n  \"_901\": \"如果您正在为我们的浏览器扩展或我们提供的任何其他功能寻求支持，请使用我们的<a class='href' href='https://keepa.com/#!discussion/' target='_blank'>讨论板</a>或向我们发送电子邮件<a class='href mailto' href='mailto:<EMAIL>?subject=Question about Keepa' target='_blank'><EMAIL> </a>。\",\n  \"_902\": \"搜索洞察\",\n  \"_903\": \"基于你 #total# 条搜索结果的汇总数据获取有用的洞察信息。\",\n  \"_904\": \"暂无可用洞察。\",\n  \"_905\": \"暂无图片\",\n  \"_906\": \"继续跟踪下一个\",\n  \"_907\": \"MAP限制\",\n  \"_908\": \"由于MAP（最低广告价格）限制，此报价的价格在亚马逊上是隐藏的。\",\n  \"_909\": \"卖家查询\",\n  \"_910\": \"寻找亚马逊卖家的信息吗？ 使用我们的卖家查询工具了解他们的评级，评论数量和其他重要细节。\",\n  \"_911\": \"最小评分\",\n  \"_912\": \"您的密码重置请求无效，找不到匹配的用户。请重新请求密码重置电子邮件。\",\n  \"_913\": \"其他清单导入设置\",\n  \"_914\": \"如果您的列表非常大，此过程可能需要一分钟。请耐心等待。\",\n  \"_915\": \"启用/禁用跟踪模式\",\n  \"_916\": \"跟踪 #selectedAsinCount# 个选择的产品\",\n  \"_917\": \"通过在第一列中单击复选框或使用“Ctrl + 单击”在行上选择/取消选择行。\",\n  \"_918\": \"请至少选择一个要跟踪的产品。\",\n  \"_919\": \"返回表格\",\n  \"_920\": \"我们允许最多 #maxTrackingAllowed# 跟踪. 您需要删除旧的价格观察记录以便能够提交新的。\",\n  \"_921\": \"创建多个帐户以绕过此限制是不允许的。\",\n  \"_922\": \"您当前正在跟踪 #trackingCount# 个产品，且正在尝试添加 #selectedAsinCount# 个跟踪。\",\n  \"_923\": \"仅显示所选列\",\n  \"_924\": \"如果您已经订阅，请确保登录到已订阅的帐户。您可以在我们发送给您的确认电子邮件的主题中找到您的用户名。\",\n  \"_925\": \"您尚未登录。请先登录或注册以访问我们的数据功能。\",\n  \"_926\": \"您尚未登录。请先登录或注册以访问我们的<a href='https://keepa.com/#!data' target='_blank'>数据功能</a>。\",\n  \"_927\": \"现在登录/注册以订阅\",\n  \"_928\": \"立即订阅，每月19欧元\",\n  \"_929\": \"如有任何问题、功能请求或错误报告，请联系 <a class='href -green' href='mailto:<EMAIL>?subject=关于 Keepa 移动应用程序的问题' target='_blank'><EMAIL></a>。\",\n  \"_930\": \"Keepa.com 跟踪全球超过 50 亿件亚马逊产品。通过我们的应用程序，您可以：\",\n  \"_931\": \"查看价格历史\",\n  \"_932\": \"查看广泛的产品数据\",\n  \"_933\": \"搜索亚马逊产品并扫描条形码\",\n  \"_934\": \"设置和管理价格表\",\n  \"_935\": \"接收价格下跌提醒\",\n  \"_936\": \"电子邮件已经存在\",\n  \"_937\": \"排序/筛选参考\",\n  \"_938\": \"对任何“差异...”或“变化...”列进行排序或筛选时，将考虑百分比值。\",\n  \"_939\": \"打开评论\",\n  \"_940\": \"发送邮件至 <a href=\\\"mailto:<EMAIL>?subject=Delete my Keepa account\\\"><EMAIL></a>。\",\n  \"_941\": \"此浏览器已退出您的帐户。这可能是由于您使用了太多设备、更改了密码或与他人共享了帐户。如果您有任何疑问，请联系<a href=\\\"mailto:<EMAIL>?subject=Question about Keepa\\\" class='href'><EMAIL></a>。\",\n  \"_942\": \"更改您的电子邮件地址\",\n  \"_943\": \"要查看亚马逊的畅销商品，请选择上方的一个部门。每个列表限制为 5,000 个 ASIN。\",\n  \"_944\": \"寻找要跟踪的产品！\",\n  \"_945\": \"管理现有的跟踪！\",\n  \"_946\": \"通过标题、ASIN或亚马逊产品链接搜索产品\",\n  \"_947\": \"您最近没有从Keepa收到通知。\",\n  \"_948\": \"附加通知设置\",\n  \"_949\": \"设置货币转换\",\n  \"_950\": \"仅适用于Chrome、Firefox和Edge。\",\n  \"_951\": \"货币设置\",\n  \"_952\": \"购物国际化？\",\n  \"_953\": \"Keepa可以通过为您转换显示的价格来提供帮助。\",\n  \"_954\": \"永远不要转换货币\",\n  \"_955\": \"价格历史记录不会因为汇率不断变化而转换，但价格工具提示（当悬停在图表上时显示）将除原始价格外还显示转换后的价格。\",\n  \"_956\": \"您的设备\",\n  \"_957\": \"国家\",\n  \"_958\": \"最后活动\",\n  \"_959\": \"首次访问\",\n  \"_960\": \"取消访问\",\n  \"_961\": \"表示过去一个月购买了多少单位该产品 -<br>如在亚马逊的SERP上显示（搜索引擎结果页面）。\",\n  \"_962\": \"产品畅销榜\",\n  \"_963\": \"按类别查看亚马逊上最畅销的产品。\",\n  \"_964\": \"热销商家列表\",\n  \"_965\": \"查看亚马逊上热销的商家。 然后，您可以单击列表中的任何卖家以查看卖家指标和店面。 默认情况下，列表按商家评级数量排序。\",\n  \"_966\": \"类别树\",\n  \"_967\": \"使用深链接浏览亚马逊的完整类别树，查看对应的畅销产品列表。\",\n  \"_968\": \"在Keepa或亚马逊上查看产品时\",\n  \"_969\": \"详细的产品信息和平均价格\",\n  \"_970\": \"快速查看包括历史价格的上架商品\",\n  \"_971\": \"导出产品的所有变体数据\",\n  \"_972\": \"eBay新品和二手价格历史记录\",\n  \"_973\": \"过滤列...\",\n  \"_974\": \"更多详情请参见<a href='https://discuss.keepa.com/t/data-features-access-free-vs-monthly-subscription/5845'>这里</a>。\",\n  \"_975\": \"在库存量旁边显示<i>最大订单限制</i>（需要较长的加载时间）\",\n  \"_976\": \"需要重新启动浏览器才能使新设置生效。\",\n  \"_977\": \"上次更改\",\n  \"_978\": \"保存新的电子邮件地址\",\n  \"_979\": \"确认电子邮件地址更改\",\n  \"_980\": \"请使用密码确认电子邮件地址更改：\",\n  \"_981\": \"特点\",\n  \"_982\": \"特点\",\n  \"_983\": \"黄金购买框 二手\",\n  \"_984\": \"新的，独家的\",\n  \"_985\": \"二手购买箱报价的价格，包括运费。购买框是产品详情页上的方框，客户可以在这里将商品加入他们的购物车。\",\n  \"_986\": \"最低新，第三方卖家或亚马逊的Prime专属，不包括运费\",\n  \"_987\": \"在价格提醒中考虑优惠券\",\n  \"_988\": \"接收Prime特价商品的价格提醒\",\n  \"_989\": \"二手购买箱报价的价格，包括运费\",\n  \"_990\": \"每个产品只显示一个变体\",\n  \"_991\": \"谢谢你使用我们的浏览器扩展。我们的扩展会在每一个支持的亚马逊产品页面上自动插入一个价格历史图。不需要点击浏览器按钮!\",\n  \"_992\": \"你可以取消你的数据访问订阅<a href='#settings/1' class='href switch-tab' data-tab='1'>&raquo;这里&laquo;</a>。只需点击橙色的 \\\"取消 \\\"按钮并确认取消。\",\n  \"_993\": \"展开\",\n  \"_994\": \"收起\",\n  \"_995\": \"上个月购买\",\n  \"_996\": \"需要购买我们的<a href='https://keepa.com/#!data' class='href -data' target='_blank'>Data Access</a>订阅。\",\n  \"_997\": \"想要追踪亚马逊上最优惠的商品吗？跟踪Buy Box！\",\n  \"_998\": \"Buy Box 通常显示亚马逊上最具竞争力的报价。\",\n  \"_999\": \"为了增加安全性，我们已向与您的帐户相关联的电子邮件地址发送了一次性密码 (OTP)：\",\n  \"_1000\": \"请检查您的收件箱和垃圾邮件文件夹，查找我们发送的包含OTP的电子邮件。检索到OTP后，请在下面输入它：\",\n  \"_1001\": \"OTP有效期为15分钟。如果您未收到OTP，请等待几分钟。如果您遇到任何问题，请联系我们的<a id='mailtoOtp' href=\\\"mailto:<EMAIL>?subject=关于Keepa的问题-OTP\\\" class='href'>支持团队</a>以获取进一步帮助。\",\n  \"_1002\": \"OTP无效，请重试。\",\n  \"_1003\": \"寻找您的产品监控？\",\n  \"_1004\": \"如果您的搜索或筛选不再匹配任何产品监控，请通过单击左侧工具栏中的逆时针箭头图标进行重置。\",\n  \"_1005\": \"Buy Box 合格报价计数\",\n  \"_1006\": \"危险品\",\n  \"_1007\": \"显示元分类\",\n  \"_1008\": \"基于过去90天的数据，平均每月发生一次相关销售排行下降（例如，从#1000下降到#500）的次数。\\\\n销售排行下降通常表示有销售发生，尽管每次下降可能对应任意数量的销售单位。\\\\n如果排名非常好（例如，#5），则必须持续购买产品以保持其排名，因此缺乏任何下降并不表示产品没有销售。\\\\n如果将下降次数作为销售指标解读，应该考虑实际销售量可能更高（不是更低）。\",\n  \"_1009\": \"下降次数 / 月\",\n  \"_1010\": \"（可能在所有变体之间共享）\",\n  \"_1011\": \"最高销售排行\",\n  \"_1012\": \"类别中的产品\",\n  \"_1013\": \"子类别级别（类别树中的祖先数量）\",\n  \"_1014\": \"最近#days#天的平均销售排行\",\n  \"_1015\": \"启用/禁用独立销售排行图表\",\n  \"_1016\": \"变体计数\",\n  \"_1017\": \"图片计数\",\n  \"_1018\": \"是否危险品\",\n  \"_1019\": \"类别ID\",\n  \"_1020\": \"% 亚马逊 #days# 天\",\n  \"_1021\": \"% 畅销 #days# 天\",\n  \"_1022\": \"获胜者计数 #days# 天\",\n  \"_1023\": \"的数量\",\n  \"_1024\": \"的计数\",\n  \"_1025\": \"亚马逊购买框份额 %\",\n  \"_1026\": \"来自获得最高百分比的卖家的购买框份额 %（包括亚马逊）\",\n  \"_1027\": \"拥有购买框的卖家数量，无论持续时间多长。\",\n  \"_1028\": \"竞争价格阈值\",\n  \"_1029\": \"此价格基于其他零售商（不包括其他亚马逊卖家）的竞争价格。如果卖方的价格+运费（减去亚马逊积分）大于此竞争价格，则该报价可能无法获得购买框。\",\n  \"_1030\": \"建议的较低价格\",\n  \"_1031\": \"商品的建议较低价格，包括运费和亚马逊积分。建议的较低价格基于一系列因素，包括历史销售价格、最近的购买框合格价格以及客户对您产品的反馈。\",\n  \"_1032\": \"月销量\",\n  \"_1033\": \"Amazon在搜索引擎结果页面（SERP）上显示的<em>上个月购买</em>指标的历史数据。<br>这不是一个估计值。亚马逊只提供括号范围内的数据，如“50+”或“100+”，而不是准确数字。\",\n  \"_1034\": \"闪购是某些商品的短期优惠促销，可能折扣力度很大且数量有限。\",\n  \"_1035\": \"亚马逊仓库优惠是亚马逊的平台，提供退货、翻新或开箱商品的折扣价格，为客户提供各种产品的经济实惠选项。\",\n  \"_1036\": \"作为<em>全新</em>销售的市场商家数量。\",\n  \"_1037\": \"作为<em>二手</em>销售的市场商家数量。\",\n  \"_1038\": \"作为<em>翻新</em>销售的市场商家数量。\",\n  \"_1039\": \"作为<em>可收藏</em>销售的市场商家数量。\",\n  \"_1040\": \"产品的星级评定\",\n  \"_1041\": \"产品评论数量\",\n  \"_1042\": \"父级标题\",\n  \"_1043\": \"90天变化 % 每月销售量\",\n  \"_1044\": \"过去90天内销售单位数的变化。<br>正百分比表示销量下降，而负百分比则表示<br>销量增加（被视为“积极”发展）。<br>仅对每月销售至少50个单位的产品提供数据。\",\n  \"_1045\": \"缺货计数 #days# 天\",\n  \"_1046\": \"过去 #days# 天内亚马逊提供的商品缺货的次数。\",\n  \"_1047\": \"亚马逊缺货计数\",\n  \"_1048\": \"最低#days#天\",\n  \"_1049\": \"对热敏感\",\n  \"_1050\": \"是最低价\",\n  \"_1051\": \"90天内是最低价\",\n  \"_1052\": \"导出所需的列\",\n  \"_1053\": \"点击表格上方的<em>#configureColumns#</em>，然后选择您感兴趣的列。或者选择<em>#resetColumns#</em>。\",\n  \"_1054\": \"配置列\",\n  \"_1055\": \"广告\",\n  \"_1056\": \"销售排行区间:\",\n  \"_1057\": \"基于:\",\n  \"_1058\": \"历史记录\",\n  \"_1059\": \"#days# 天平均值\",\n  \"_1060\": \"根目录/主销售排行\",\n  \"_1061\": \"子类别/分类销售排行\",\n  \"_1062\": \"默认情况下，子类别的畅销书榜单是根据产品的主销售排行（如有）创建的。 要请求基于子类别销售排行（分类排行）的畅销书榜单，请使用选项“#option#”\",\n  \"_1063\": \"退货率\",\n  \"_1064\": \"无值\",\n  \"_1065\": \"低\",\n  \"_1066\": \"高\",\n  \"_1067\": \"表示此产品的退货频率。<br>“低”表示退货较少，<br>而“高”则可能表明产品存在潜在问题。\",\n  \"_1068\": \"订阅并保存\",\n  \"_1069\": \"订阅和节省折扣\",\n  \"_1070\": \"自引入Buy Box统计以来的天数（可能与产品首次在Amazon上市的日期不同）。\",\n  \"_1071\": \"隐藏所有\",\n  \"_1072\": \"变体属性\",\n  \"_1073\": \"追踪开始日期\",\n  \"_1074\": \"历史\",\n  \"_1075\": \"评级\",\n  \"_1076\": \"格式特定\",\n  \"_1077\": \"链接评论\",\n  \"_1078\": \"不在组中\",\n  \"_1079\": \"删除电子邮件地址\",\n  \"_1080\": \"删除电子邮件\",\n  \"_1081\": \"您确定要从账户中删除电子邮件地址吗？您将无法再使用电子邮件地址登录。（除非您的用户名也是电子邮件地址。）\",\n  \"_1082\": \"保留，但禁用电子邮件价格提醒\",\n  \"_1083\": \"产品详情\",\n  \"_1084\": \"优惠\",\n  \"_1085\": \"Buy Box 统计数据\",\n  \"_1086\": \"Buy Box 二手 统计数据\",\n  \"_1087\": \"详情\",\n  \"_1088\": \"最近#days#天的下降\",\n  \"_1089\": \"#days#天缺货\",\n  \"_1090\": \"过去#days#天Amazon缺货的百分比。<br>数字越高，Amazon的库存时间越少。\",\n  \"_1091\": \"过去#days#天内任何新状态报价的缺货百分比。<br>数字越高，新状态报价的库存时间越少。\",\n  \"_1092\": \"过去#days#天内任何二手状态报价的缺货百分比。<br>数字越高，二手状态报价的库存时间越少。\",\n  \"_1093\": \"过去#days#天内Buy Box缺货/压制的百分比。<br>数字越高，Buy Box报价可用的时间越少。\",\n  \"_1094\": \"检索到的实时报价计数\",\n  \"_1095\": \"检索到的特定类型（状态、商家类型）的实时报价数量。\",\n  \"_1096\": \"参考\",\n  \"_1097\": \"最后价格变动\",\n  \"_1098\": \"FBA 提货和打包费用\",\n  \"_1099\": \"推荐费用 %\",\n  \"_1100\": \"上市时间\",\n  \"_1101\": \"产品代码\",\n  \"_1102\": \"上级 ASIN\",\n  \"_1103\": \"变体ASIN\",\n  \"_1104\": \"主类别\",\n  \"_1105\": \"根\",\n  \"_1106\": \"子类\",\n  \"_1107\": \"树\",\n  \"_1108\": \"Buy Box 卖家\",\n  \"_1109\": \"基于当前Buy Box价格的推荐费用\",\n  \"_1110\": \"类型\",\n  \"_1111\": \"产品组\",\n  \"_1112\": \"型号\",\n  \"_1113\": \"捆绑\",\n  \"_1114\": \"包裹\",\n  \"_1115\": \"物品\",\n  \"_1116\": \"尺寸\",\n  \"_1117\": \"长度\",\n  \"_1118\": \"宽度\",\n  \"_1119\": \"高度\",\n  \"_1120\": \"重量\",\n  \"_1121\": \"数量\",\n  \"_1122\": \"在移动设备上仅适用于 <em>Firefox</em> 和 <em>Microsoft Edge</em>。\",\n  \"_1123\": \"更多值可用，复制单元格以获取所有内容\",\n  \"_1124\": \"? = 最后一次报价更新超过72小时\",\n  \"_1125\": \"斜体 = 最后一次报价更新超过24小时\",\n  \"_1126\": \"1天平均\",\n  \"_1127\": \"Amazon报价的可用性\",\n  \"_1128\": \"Amazon报价发货延迟\",\n  \"_1129\": \"Buy Box的可用性\",\n  \"_1130\": \"Buy Box的可用性消息\",\n  \"_1131\": \"自上次访问以来下降 %\",\n  \"_1132\": \"1天下降 %\",\n  \"_1133\": \"#days# 天下降 %\",\n  \"_1134\": \"一次性优惠券\",\n  \"_1135\": \"直接减价\",\n  \"_1136\": \"子类别 销售排行\",\n  \"_1137\": \"成人用品t\",\n  \"_1138\": \"作者\",\n  \"_1139\": \"是否FBA\",\n  \"_1140\": \"缺货待补\",\n  \"_1141\": \"不合格\",\n  \"_1142\": \"撰稿人\",\n  \"_1143\": \"经常一起购买\",\n  \"_1144\": \"语言\",\n  \"_1145\": \"最后报价更新\",\n  \"_1146\": \"最后更新\",\n  \"_1147\": \"发行日期\",\n  \"_1148\": \"发布日期\",\n  \"_1149\": \"最低 #type# 卖家\",\n  \"_1150\": \"使用期限\",\n  \"_1151\": \"Amazon 店\",\n  \"_1152\": \"物品数量\",\n  \"_1153\": \"页数\",\n  \"_1154\": \"Prime 资格\",\n  \"_1155\": \"符合以旧换新条件\",\n  \"_1156\": \"按当前显示的顺序导出表格。\",\n  \"_1157\": \"导出内容？\",\n  \"_1158\": \"所有活动列\",\n  \"_1159\": \"仅ASIN\",\n  \"_1160\": \"仅卖家ID\",\n  \"_1161\": \"仅报价深层链接\",\n  \"_1162\": \"导出格式是什么？\",\n  \"_1163\": \"Excel (.xlsx)\",\n  \"_1164\": \"CSV\",\n  \"_1165\": \"注意：导出仅包含当前显示的页面（不是所有可能的结果）。\",\n  \"_1166\": \"加载/保存预设\",\n  \"_1167\": \"加载/保存列预设\",\n  \"_1168\": \"最多可以保存10个列预设，可应用于我们的任意ASIN表*。\",\n  \"_1169\": \"将当前列配置保存为：\",\n  \"_1170\": \"预设名称\",\n  \"_1171\": \"加载并应用以下预设之一：\",\n  \"_1172\": \"导入以前导出/备份的列预设：\",\n  \"_1173\": \"选择预设 *.json 文件\",\n  \"_1174\": \"注意：如果预设名称已存在，现有预设将被覆盖。\",\n  \"_1175\": \"* 产品查找器、产品查看器、畅销产品或卖家店面\",\n  \"_1176\": \"保存\",\n  \"_1177\": \"加载\",\n  \"_1178\": \"导入\",\n  \"_1179\": \"无上下文名称\",\n  \"_1180\": \"最高（根类别）销售排名\",\n  \"_1181\": \"是否为浏览节点\",\n  \"_1182\": \"链接\",\n  \"_1183\": \"最低（根类别）销售排名\",\n  \"_1184\": \"产品数量\",\n  \"_1185\": \"前#top#%的产品数量\",\n  \"_1186\": \"网站显示组\",\n  \"_1187\": \"在我们的产品查找器中搜索“#term#”\",\n  \"_1188\": \"在我们的产品查找器中搜索“#term1#”和“#term2#”\",\n  \"_1189\": \"显示畅销书列表\",\n  \"_1190\": \"在该类别中基于 #salesRank# 排名估算进入前 #top#% 的产品数量。\",\n  \"_1191\": \"使用FBA\",\n  \"_1192\": \"经过验证的列表\",\n  \"_1193\": \"主要销售\",\n  \"_1194\": \"通过 Keepa 浏览商品列表\",\n  \"_1195\": \"找到的FBA列表\",\n  \"_1196\": \"自Keepa开始跟踪\",\n  \"_1197\": \"列表数量\",\n  \"_1198\": \"最近的客户反馈\",\n  \"_1199\": \"卖家类别列表统计\",\n  \"_1200\": \"#seller#主要销售的产品类型概览、Amazon在这些列表中出现的频率以及该卖家产品的平均销售排名。\",\n  \"_1201\": \"卖家品牌列表统计\",\n  \"_1202\": \"#seller#主要销售的品牌产品概览、Amazon在这些列表中出现的频率以及该卖家产品的平均销售排名。\",\n  \"_1203\": \"列表\",\n  \"_1204\": \"含有Amazon报价的列表\",\n  \"_1205\": \"卖家指标\",\n  \"_1206\": \"店面\",\n  \"_1207\": \"贸易编号\",\n  \"_1208\": \"增值税号\",\n  \"_1209\": \"电话号码\",\n  \"_1210\": \"业务类型\",\n  \"_1211\": \"注册资本\",\n  \"_1212\": \"代表人\",\n  \"_1213\": \"客户服务地址\",\n  \"_1214\": \"加载 #locale# 产品\",\n  \"_1215\": \"您最多可以加载 #number# 个产品* 以查看。加载后，您可以为您的列表创建书签**。\",\n  \"_1216\": \"或者，您可以上传包含 ASIN 或 UPC / EAN / GTIN 代码列表的文本文件：\",\n  \"_1217\": \"包括目前无法收集最新信息的产品\",\n  \"_1218\": \"* 建议：5000，较大的列表可能需要一些时间才能加载！\",\n  \"_1219\": \"** 目前，书签仅在结果包含最多约3000个产品时才可能。\",\n  \"_1220\": \"ASIN 列表\",\n  \"_1221\": \"UPC / EAN / GTIN 代码列表\",\n  \"_1222\": \"ASIN 列表…（最多 #number#）\",\n  \"_1223\": \"未找到有效的ASIN\",\n  \"_1224\": \"UPC, EAN 或 GTIN 列表…（最多 #number#）\",\n  \"_1225\": \"未找到有效的 UPC / EAN / GTIN\",\n  \"_1226\": \"加载列表\",\n  \"_1227\": \"选择文本文件（例如 *.txt, *.csv, ...）\",\n  \"_1228\": \"* <a href=\\\"https://keepa.com/#!settings/1\\\" target=\\\"_blank\\\">立即订阅</a> 以加载最多10000行并完全访问所有 <a href=\\\"https://keepa.com/#!data\\\" target=\\\"_blank\\\">Data Access</a>。\",\n  \"_1229\": \"导入列表\",\n  \"_1230\": \"移除\",\n  \"_1231\": \"移除所选行（按住 Ctrl + 单击选择或取消选择行）\",\n  \"_1232\": \"更新产品数据\",\n  \"_1233\": \"强制更新所有超过几个小时的产品数据。\",\n  \"_1234\": \"关于产品查找器\",\n  \"_1235\": \"通过搜索我们的整个产品数据库，找到符合任何条件的产品！您可以按几乎任何产品属性进行搜索和排序。例如，您可以搜索价格在#currencySymbol#50至#currencySymbol#100之间、销售排行平均不高于5,000、由Sony制造、产品列表上少于5位卖家的所有电子产品，并按销售排行升序排列输出。可以通过搜索结果表格指定排序，默认排序顺序为销售排行。\",\n  \"_1236\": \"有建议或发现错误？您可以通过我们的评论区提供反馈。\",\n  \"_1237\": \"每次搜索根据您的表格行设置和分页最多返回10,000个ASIN。由于所有的过滤和排序操作都是远程执行的（而不是在您本地加载的表格中），我们建议使用较低的行设置，这将加快加载速度并减少配额使用。仅当您要导出表格或查看当前选择的更多产品时，才使用更高的行设置。\",\n  \"_1238\": \"提交查询后，您可以随时点击产品表格上方的\\\"#advancedFilter#\\\"进行更改。您还可以直接在表格中使用列菜单添加或删除筛选器或更改排序方向。在任何情况下，您的查询都会编码在URL中，因此您可以保存选择的浏览器书签。离开查找器将清除所有筛选器。\",\n  \"_1239\": \"每个筛选器属性将由AND条件连接。\",\n  \"_1240\": \"在允许多个条目的字段（例如制造商）中，每个条目被视为OR，并支持最多#number#个条目。\",\n  \"_1241\": \"可以通过使用<span style=\\\"color:brown\\\">###</span>作为分隔符一次提供多个条目。\",\n  \"_1242\": \"查找器搜索我们的数据库，可能无法找到所有符合您查询的Amazon上可用的产品。\",\n  \"_1243\": \"通过单击列标题（您还可以按住shift键对多个列进行排序）在产品结果表中设置排序。\",\n  \"_1244\": \"每次搜索/排序都会消耗您的一小部分配额。\",\n  \"_1245\": \"销售排行和价格类型\",\n  \"_1246\": \"定义您希望的销售排行和/或价格范围。您可以定义任意多个范围以优化结果。\",\n  \"_1247\": \"无销售排行\",\n  \"_1248\": \"从\",\n  \"_1249\": \"到\",\n  \"_1250\": \"更多筛选条件！\",\n  \"_1251\": \"标题、类别和属性\",\n  \"_1252\": \"按类别、标题、品牌等过滤结果。\",\n  \"_1253\": \"所有根类别\",\n  \"_1254\": \"子类别\",\n  \"_1255\": \"包含子类别\",\n  \"_1256\": \"排除子类别\",\n  \"_1257\": \"文本字段\",\n  \"_1258\": \"以下字段支持多个条目，每个条目视为OR，最多支持50个条目（每个字段）。多个值用<i>###</i>分隔。\",\n  \"_1259\": \"是以下之一\",\n  \"_1260\": \"不是以下任何一个\",\n  \"_1261\": \"报价数量\",\n  \"_1262\": \"将结果限制为当前#buyBox#卖家 - 亚马逊，第三方或特定卖家（需要卖家ID，多个卖家ID用逗号分隔）。\",\n  \"_1263\": \"将结果限制为当前#buyBox#卖家 - 亚马逊仓库，第三方或特定卖家（需要卖家ID，多个卖家ID用逗号分隔）。\",\n  \"_1264\": \"第三方\",\n  \"_1265\": \"卖家ID\",\n  \"_1266\": \"附加#buyBox#选项\",\n  \"_1267\": \"将结果限制为来自特定卖家的报价（需要卖家ID，多个卖家ID用逗号分隔）。\",\n  \"_1268\": \"进一步优化您的搜索！\",\n  \"_1269\": \"产品类型\",\n  \"_1270\": \"实物产品\",\n  \"_1271\": \"数字产品\",\n  \"_1272\": \"电子书\",\n  \"_1273\": \"重置整个表单\",\n  \"_1274\": \"无变体\",\n  \"_1275\": \"是变体\",\n  \"_1276\": \"90天缺货百分比 (OOS %)\",\n  \"_1277\": \"#buyBox# 缺货 / 被抑制\",\n  \"_1278\": \"销售排行下降\",\n  \"_1279\": \"重置部分\",\n  \"_1280\": \"重置行\",\n  \"_1281\": \"无亚马逊报价\",\n  \"_1282\": \"亚马逊报价有库存且可发货\",\n  \"_1283\": \"亚马逊报价为预购\",\n  \"_1284\": \"亚马逊报价的可用性“未知”\",\n  \"_1285\": \"亚马逊报价已下单\",\n  \"_1286\": \"亚马逊报价发货延迟\",\n  \"_1287\": \"附加选项\",\n  \"_1288\": \"附加设置\",\n  \"_1289\": \"允许包含可能过期的价格信息的产品\",\n  \"_1290\": \"您当前的筛选条件符合<br><span>0</span>个产品\",\n  \"_1291\": \"显示活动筛选器\",\n  \"_1292\": \"\",\n  \"_1293\": \"查找产品\",\n  \"_1294\": \"高级筛选\",\n  \"_1295\": \"活动产品查找器筛选\",\n  \"_1296\": \"当前选择的Product Finder API查询\",\n  \"_1297\": \"如果您有Keepa API访问权限，可以使用以下JSON通过我们的API<a href=\\\"https://keepa.com/#!discuss/t/product-finder/5473\\\">查询我们的产品数据库</a>：\",\n  \"_1298\": \"允许的值格式：9999或19.90\",\n  \"_1299\": \"允许的值格式：5或4.2\",\n  \"_1300\": \"允许的值格式：125或74.088\",\n  \"_1301\": \"允许的值格式：1.5或4；允许范围：1.0 - 5.0\",\n  \"_1302\": \"允许的值格式：50；允许范围：0 - 100\",\n  \"_1303\": \"允许的值格式：50；允许范围：0 - 99\",\n  \"_1304\": \"只允许大于或等于50的整数\",\n  \"_1305\": \"允许的值格式：50；允许范围：从-1000到99\",\n  \"_1306\": \"只允许整数，例如99\",\n  \"_1307\": \"查看工具提示\",\n  \"_1308\": \"特定子类别的亚马逊畅销商品排名。<br>要激活，请选择子类别以及所需范围。<br>禁用上述所有其他销售排名过滤器。\",\n  \"_1309\": \"销售排名子类别 - 只支持一个。如果设置了子类别范围，则为必填项。没有范围将被忽略。\",\n  \"_1310\": \"作者名称。例：<i>匿名</i>\",\n  \"_1311\": \"绑定名称。例：<i>平装本</i>\",\n  \"_1312\": \"品牌名称。例：<i>佳能</i>\",\n  \"_1313\": \"产品直接列在亚马逊子类别中。\",\n  \"_1314\": \"产品未直接列在亚马逊子类别中。\",\n  \"_1315\": \"颜色名称。例：<i>黑色</i>\",\n  \"_1316\": \"版本名称。例：<i>第一版</i>\",\n  \"_1317\": \"格式名称。例：<i>光盘</i>\",\n  \"_1318\": \"语言名称。例：<i>英语</i>\",\n  \"_1319\": \"制造商名称。例：<i>佳能</i>\",\n  \"_1320\": \"型号名称。例：<i>2016</i>\",\n  \"_1321\": \"部件编号名称。例：<i>DSC-H300/BM-RB</i>.<br>在<i>部件编号</i>字段内过滤精确匹配。\",\n  \"_1322\": \"产品组名称。例：<i>服装</i>\",\n  \"_1323\": \"列出产品的亚马逊根类别。\",\n  \"_1324\": \"尺寸名称。例：<i>大号</i>\",\n  \"_1325\": \"产品标题。基于关键字的工作方式，产品标题必须包含<br>指定字符串的关键字，关键字之间用空格分隔。支持最多<br>50个关键字。搜索不区分大小写。不支持部分关键字匹配。\",\n  \"_1326\": \"示例：\",\n  \"_1327\": \"<i>数码相机 佳能</i> - 标题必须包含所有三个关键字，无论顺序或位置。\",\n  \"_1328\": \"<i>&quot;数码相机&quot; 佳能</i> - 标题必须包含关键字<i>数码相机</i>和<i>佳能</i>。\",\n  \"_1329\": \"<i>-数码相机</i> - 标题必须不包含关键字<i>数码</i>，并且必须包含<i>相机</i>。\",\n  \"_1330\": \"亚马逊产品类型分类。例：<i>数码相机</i>\",\n  \"_1331\": \"当前#buyBox#持有者的卖家ID。<br>要搜索抑制的#buyBox#，请提供-1作为卖家ID。\",\n  \"_1332\": \"当前#buyBoxUsed#持有者的卖家ID。\",\n  \"_1333\": \"确定产品的可用数据。\",\n  \"_1334\": \"实体产品：所有可访问\",\n  \"_1335\": \"数字产品：无市场/第三方价格数据\",\n  \"_1336\": \"电子书：无市场/第三方价格数据\",\n  \"_1337\": \"产品是否有变体\",\n  \"_1338\": \"商家的卖家ID，<br>在卖家ID前加上-<br>将其排除在结果之外。\",\n  \"_1339\": \"我们登记的最后一次价格变化时间（任何价格类型）\",\n  \"_1340\": \"我们开始跟踪产品的时间\",\n  \"_1341\": \"产品首次在亚马逊上列出的时间\",\n  \"_1342\": \"数字越高，特定类型（状态，商人类型）的报价在库存中的时间越少。\",\n  \"_1343\": \"产品包含的项目或页面数量\",\n  \"_1344\": \"可用的图片和产品变体数量。\",\n  \"_1345\": \"产品的 #buyBox# 报价的优惠券详情：<br>- <em>绝对值</em>：可点击的一次性优惠券，例如“申请 #currency#10 优惠券”<br>- <em>百分比</em>：可点击的一次性优惠券，例如“立省 5%”<br>- <em>订阅并保存 %</em>：仅适用于首个订阅省订单的<br>可点击一次性优惠券。\",\n  \"_1346\": \"最低的新价格（无论是价格<br>类型亚马逊或新）是否受到MAP<br>（最低广告价格）的限制。使用此方法区分<br>缺货与MAP限制\",\n  \"_1347\": \"指示产品是否被视为仅限成人。\",\n  \"_1348\": \"指示产品是否符合换购条件。\",\n  \"_1349\": \"指示产品的#buyBox#是否符合免费送货条件。\",\n  \"_1350\": \"指示产品的#buyBox#是否符合定期订阅和节省条件。\",\n  \"_1351\": \"指示产品是否根据亚马逊的安全规定被分类为危险材料（HazMat）。\",\n  \"_1352\": \"指示产品是否根据亚马逊的安全规定被分类为热敏材料。\",\n  \"_1353\": \"指示产品是否有活动促销。\",\n  \"_1354\": \"某ASIN的FBA拣货和包装费用。\",\n  \"_1355\": \"在过去X天内，销售排名下降的计数（从高值到低值）<br>被认为表明销售。\",\n  \"_1356\": \"产品的出版日期\",\n  \"_1357\": \"产品的发布日期\",\n  \"_1358\": \"指示#buyBox#是否由亚马逊履行。\",\n  \"_1359\": \"指示#buyBoxUsed#是否由亚马逊履行。\",\n  \"_1360\": \"指示卖家是否赢得#buyBox#。如果只有提供差的报价的卖家，则没有卖家符合#buyBox#的资格。\",\n  \"_1361\": \"指示#buyBox#是否为预购\",\n  \"_1362\": \"指示#buyBox#是否为后续订单\",\n  \"_1363\": \"指示#buyBox#是否为Prime专属\",\n  \"_1364\": \"指示产品是否属于亚马逊Launchpad。\",\n  \"_1365\": \"#buyBoxUsed#的商品状态。\",\n  \"_1366\": \"附加的#buyBoxUsed#开关\",\n  \"_1367\": \"如果您按任何与报价相关的数据进行排序或筛选，结果将仅限于<br>最后一次报价更新不超过3天的产品，除非选中复选框。\",\n  \"_1368\": \"赢得的百分比\",\n  \"_1369\": \"卖家赢得#buyBox#的百分比\",\n  \"_1370\": \"平均价格\",\n  \"_1371\": \"该卖家的#buyBox#报价的平均价格\",\n  \"_1372\": \"运费\",\n  \"_1373\": \"仅FBA\",\n  \"_1374\": \"仅FBM\",\n  \"_1375\": \"附加选项\",\n  \"_1376\": \"包含历史报价\",\n  \"_1377\": \"显示潜在的诈骗报价\",\n  \"_1378\": \"价格历史\",\n  \"_1379\": \"已售出\",\n  \"_1380\": \"#days#天前售出\",\n  \"_1381\": \"库存历史\",\n  \"_1382\": \"此报价是否由亚马逊履行。\",\n  \"_1383\": \"(过去12个月)\",\n  \"_1384\": \"天数范围：\",\n  \"_1385\": \"#buyBox# 缺货或被抑制\",\n  \"_1386\": \"平均新报价数量\",\n  \"_1387\": \"平均二手报价数量\",\n  \"_1388\": \"卖家持有#buyBox#期间的平均新报价数量\",\n  \"_1389\": \"卖家持有#buyBoxUsed#期间的平均二手报价数量\",\n  \"_1390\": \"最近赢得\",\n  \"_1391\": \"卖家最近赢得#buyBox#的时间\",\n  \"_1392\": \"最近赢得（前）\",\n  \"_1393\": \"卖家赢得#buyBox#已经过去多长时间\",\n  \"_1394\": \"#buyBox# 可以在多个卖家之间共享。以下表格概述了每个卖家在 <b>#condition#</b> #buyBox# 中所持有的百分比。\",\n  \"_1395\": \"数据可能有限。\",\n  \"_1396\": \"暂无（最新）统计的 #buyBox# 数据可用。\",\n  \"_1397\": \"单元详细信息\",\n  \"_1398\": \"单元价值\",\n  \"_1399\": \"按单元定价的产品重量、体积或净数量<br>例如，12瓶每瓶6盎司的包装中的72盎司）。\",\n  \"_1400\": \"单元类型\",\n  \"_1401\": \"用于产品的度量类型（例如，升，件）。\",\n  \"_1402\": \"每单位计数\",\n  \"_1403\": \"产品中最小的单独标记组件中的单位数量。\",\n  \"_1404\": \"成分\",\n  \"_1405\": \"兑换链接有效期为1个月。\",\n  \"_1406\": \"兑换链接\",\n  \"_1407\": \"有效期为1个月\",\n  \"_1408\": \"图表设置\",\n  \"_1409\": \"显示价格趋势和根类别的销售排行。\",\n  \"_1410\": \"优惠券和订阅省\",\n  \"_1411\": \"显示历史一次性优惠券和订阅省优惠券数据。\",\n  \"_1412\": \"类别销售排行 & 月销售量\",\n  \"_1413\": \"按类别查看销售排行和每月销售数据。\",\n  \"_1414\": \"报价数量和评分\",\n  \"_1415\": \"显示报价数量、星级评分和评分总数。\",\n  \"_1416\": \"单击每个图表旁的标签以显示或隐藏各种历史数据。\",\n  \"_1417\": \"未通知\",\n  \"_1418\": \"仅为当前跟踪的商品显示通知。\",\n  \"_1419\": \"平均 #priceType# 降价百分比（#days# 天）\",\n  \"_1420\": \"过去#days#天内#priceType#价格的平均百分比变化，按产品计算。\",\n  \"_1421\": \"该类别中所有在售商品在过去#days#天内的时间加权#buyBox#平均价格。适用于检测长期价格趋势和季节性变化。\",\n  \"_1422\": \"过去#days#天内#priceType#报价的平均百分比变化，按产品计算。\",\n  \"_1423\": \"隐藏\",\n  \"_1425\": \"禁用范围选择\",\n  \"_1426\": \"启用范围选择\",\n  \"_1427\": \"禁用过滤器\",\n  \"_1428\": \"启用过滤器\",\n  \"_1429\": \"活性成分\",\n  \"_1430\": \"产品中包含的活性成分。<p>示例：\\\"薰衣草精油，大豆蜡\\\"</p>\",\n  \"_1431\": \"特殊成分\",\n  \"_1432\": \"产品中使用的特殊成分，可能具有独特的特性。<p>示例：\\\"蜂蜡混合物，天然染料\\\"</p>\",\n  \"_1433\": \"简短描述\",\n  \"_1434\": \"产品的简要描述。\",\n  \"_1435\": \"产品类型\",\n  \"_1436\": \"描述产品类型或类别的关键字。<p>示例：\\\"身体乳液\\\"</p>\",\n  \"_1437\": \"目标受众\",\n  \"_1438\": \"描述产品目标受众的关键字。<p>示例：\\\"女性，男性，成人通用\\\"</p>\",\n  \"_1439\": \"香味\",\n  \"_1440\": \"产品的香味。描述与产品相关的气味。<p>示例：\\\"薰衣草\\\"</p>\",\n  \"_1441\": \"产品形式\",\n  \"_1442\": \"产品的形式或物理状态。<p>示例：\\\"液体\\\", \\\"固体\\\", \\\"凝胶\\\"</p>\",\n  \"_1443\": \"图案\",\n  \"_1444\": \"产品的图案或设计。<p>示例：\\\"条纹\\\", \\\"花卉\\\"</p>\",\n  \"_1445\": \"风格\",\n  \"_1446\": \"产品的风格，可能影响其美观性。<p>示例：\\\"现代\\\", \\\"复古\\\"</p>\",\n  \"_1447\": \"材料\",\n  \"_1448\": \"产品的材料，指定主要构造材料。<p>示例：\\\"大豆蜡，棉花\\\"</p>\",\n  \"_1449\": \"推荐用途\",\n  \"_1450\": \"为客户提供产品的推荐用途。<p>示例：\\\"芳香疗法，家居装饰\\\"</p>\",\n  \"_1451\": \"具体用途\",\n  \"_1452\": \"产品的具体用途，提供详细的应用场景。<p>示例：{\\\"放松\\\", \\\"装饰\\\"}</p>\",\n  \"_1453\": \"产品优势\",\n  \"_1454\": \"使用产品的优势，突出其优点。<p>示例：\\\"促进放松和缓解压力。\\\"</p>\",\n  \"_1455\": \"是 Merch on Demand\",\n  \"_1456\": \"指示该产品是否为Amazon Merch on Demand产品。\",\n  \"_1457\": \"包含的组件\",\n  \"_1458\": \"详细说明产品随附的组件。<p>示例：\\\"蜡烛，灯芯，盒子\\\"</p>\",\n  \"_1459\": \"安全警告\",\n  \"_1460\": \"与产品相关的安全警告，以提醒用户潜在危险。<p>示例：\\\"远离明火。\\\"</p>\",\n  \"_1461\": \"是否需要电池\",\n  \"_1462\": \"指示产品是否需要电池才能工作。\",\n  \"_1463\": \"是否包含电池\",\n  \"_1464\": \"指示产品购买时是否包含电池。\",\n  \"_1465\": \"标准偏差 #days# 天\",\n  \"_1466\": \"测量过去 #days# 天内价格相对于平均值的变化幅度。\",\n  \"_1467\": \"波动性 #days# 天\",\n  \"_1468\": \"显示过去 #days# 天价格波动的分数，<br>范围为 0 到 99。0 表示价格没有变化，<br>99 表示频繁且大幅度的价格波动。\",\n  \"_1469\": \"商业折扣\",\n  \"_1470\": \"表示此产品（如果可用）适用的最大商业折扣百分比<br>。\",\n  \"_1471\": \"全部合并\",\n  \"_1472\": \"包含来自日、周、月和3个月间隔的优惠。\",\n  \"_1473\": \"点击以访问更多筛选选项\",\n  \"_1474\": \"活动筛选器：\",\n  \"_1475\": \"品牌店铺名称\",\n  \"_1476\": \"品牌店铺的名称。\",\n  \"_1477\": \"品牌店铺URL\",\n  \"_1478\": \"品牌店铺的完整URL。\",\n  \"_1479\": \"主要视频\",\n  \"_1480\": \"其他视频\",\n  \"_1481\": \"创作者\",\n  \"_1482\": \"视频\",\n  \"_1483\": \"与产品相关的视频列表。我们可以提供最多10个社区视频。\",\n  \"_1484\": \"统计视频的总数。我们最多可以识别10个社区视频。\",\n  \"_1485\": \"有主视频\",\n  \"_1486\": \"产品是否有主视频（作为产品图片轮播的一部分）。\",\n  \"_1487\": \"有A+内容\",\n  \"_1488\": \"产品是否有A+内容。\",\n  \"_1489\": \"来自制造商的A+\",\n  \"_1490\": \"A+内容是否来自制造商 / 供应商。\",\n  \"_1491\": \"品牌店铺URL名称\",\n  \"_1492\": \"品牌店铺的URL友好名称。\",\n  \"_1493\": \"A+内容\",\n  \"_1494\": \"此产品的A+内容。\",\n  \"_1495\": \"平均 #buyBox# 价格\",\n  \"_1496\": \"所有产品当前#buyBox#价格的平均值。价格较高的产品对该值的影响更大，反映整个类别的平均价格水平。\",\n  \"_1497\": \"平均 #buyBox# 价格（#days#天）\",\n  \"_1498\": \"过去#days#天#buyBox#价格的时间加权平均值。可作为识别类别价格趋势的稳定基线。\",\n  \"_1499\": \"Buy Box价格标准差\",\n  \"_1500\": \"Buy Box价格的标准差，反映价格波动情况。仅基于有销售排名的产品数据。\",\n  \"_1501\": \"平均评论数量\",\n  \"_1502\": \"每个产品的平均评论数量。仅基于有销售排名的产品数据。\",\n  \"_1503\": \"平均评分\",\n  \"_1504\": \"所有跟踪产品的平均顾客评分（满分5分）。仅基于有销售排名的产品数据。\",\n  \"_1505\": \"FBA Buy Box 占比 (%)\",\n  \"_1506\": \"Buy Box 由 FBA（亚马逊配送）卖家持有的产品百分比。仅基于有销售排名的产品数据。\",\n  \"_1507\": \"亚马逊自营产品占比 (%)\",\n  \"_1508\": \"亚马逊自身为卖家的产品所占百分比。仅基于有销售排名的产品数据。\",\n  \"_1509\": \"有活动优惠券 (%)\",\n  \"_1510\": \"当前拥有活动优惠券的产品百分比。仅基于有销售排名的产品数据。\",\n  \"_1511\": \"搜索结果\",\n  \"_1512\": \"以下 #count# 个 #type# 加载失败：\",\n  \"_1513\": \"最短延迟（天）\",\n  \"_1514\": \"#ld# 通知已禁用。要接收跟踪项的 #ld# 提醒，请访问您的 <a href='#!channels' class='href' target='_blank'>#settings#</a>。\",\n  \"_1515\": \"公司名称\",\n  \"_1516\": \"自报价首次出现以来：\",\n  \"_1517\": \"过去30天内售出数量（根据库存历史估算）：\",\n  \"_1518\": \"过去30天内卖家赢得Buy Box的百分比：\",\n  \"_1519\": \"总报价数\",\n  \"_1520\": \"所有条件 下的总报价数量。\",\n  \"_1521\": \"网站显示组\",\n  \"_1522\": \"对行为相似的产品进行分类，<br>影响销售排名的计算和显示方式，<br>尤其是针对产品变体。\",\n  \"_1523\": \"名称\",\n  \"_1524\": \"标识符\",\n  \"_1525\": \"术语表\",\n  \"_1526\": \"筛选术语表...\",\n  \"_1527\": \"未提供描述。\",\n  \"_1528\": \"当前最低的 #csvType# 价格。\",\n  \"_1529\": \"过去一天的平均 #csvType# 价格。\",\n  \"_1530\": \"过去 #days# 天的平均 #csvType# 价格。\",\n  \"_1531\": \"当前的 #csvType#。\",\n  \"_1532\": \"过去一天的平均 #csvType#。\",\n  \"_1533\": \"过去 #days# 天的平均 #csvType#。\",\n  \"_1534\": \"与#days#天平均相比的#csvType#价格下降幅度（%）。\",\n  \"_1535\": \"与#days#天平均相比的#csvType#下降幅度（%）。\",\n  \"_1536\": \"我们记录的历史最低#csvType#价格。\",\n  \"_1537\": \"我们记录的历史最高#csvType#价格。\",\n  \"_1538\": \"我们记录的历史最低#csvType#。\",\n  \"_1539\": \"我们记录的历史最高#csvType#。\",\n  \"_1540\": \"表示这是否是记录中的最低值。\",\n  \"_1541\": \"表示这是否是过去#days#天内记录的最低值。\",\n  \"_1542\": \"Data 设置\",\n  \"_1543\": \"清除此浏览器中存储的已访问产品。会影响产品查找器和查看器等数据表中的所有已访问链接。\",\n  \"_1544\": \"单位系统\",\n  \"_1545\": \"公制（cm，g）\",\n  \"_1546\": \"英制（in，lb）\",\n  \"_1547\": \"平均新品报价数\",\n  \"_1548\": \"该类别中所有产品的平均新品报价数量。\",\n  \"_1549\": \"平均二手报价数\",\n  \"_1550\": \"该类别中所有产品的平均二手报价数量。\",\n  \"_1551\": \"卖家数量\",\n  \"_1552\": \"在该类别中拥有活跃报价的不同卖家数量。\",\n  \"_1553\": \"品牌数量\",\n  \"_1554\": \"该分类中不同品牌的数量。\",\n  \"_1555\": \"打印\",\n  \"_1556\": \"产品\",\n  \"_1557\": \"#priceType# 平均下降百分比\",\n  \"_1558\": \"在过去#days1#天和#days2#天内，#priceType#价格的平均百分比变化，按产品计算。\",\n  \"_1559\": \"显示分组\",\n  \"_1560\": \"主要销售排行所依据的产品分类。\",\n  \"_1561\": \"变体间共享的销售排行\",\n  \"_1562\": \"无共享的销售排行\",\n  \"_1563\": \"评分数量\",\n  \"_1564\": \"该商品收到的1到5星评分总数，包括无文字内容的评分和附有评论的评分。\",\n  \"_1565\": \"先前标注为：'#reviewCount#'\",\n  \"_1566\": \"所有图表均已关闭。点击图表右侧的标签以显示其曲线。\",\n  \"_1567\": \"热销卖家（按 Buy Box 报价数）\",\n  \"_1568\": \"当前拥有最多 Buy Box 报价的卖家。排名不代表销售量或收入。\",\n  \"_1569\": \"通过 Microsoft Edge for iOS 在手机浏览器上获取 Keepa\",\n  \"_1570\": \"扫描下面的二维码，在手机浏览器上通过 Microsoft Edge for iOS 获取 Keepa 扩展。\",\n  \"_1571\": \"#buyBox# 平均竞争者数量\",\n  \"_1572\": \"#buyBox# #condition# 占有情况\",\n  \"_1573\": \"\",\n  \"_1574\": \"重叠最多的主要竞争者\",\n  \"_1575\": \"与 #seller# 存在大量商品目录重叠的卖家明细。\",\n  \"_1576\": \"代码前缀搜索\",\n  \"_1577\": \"输入代码前缀（1–14位数字）\",\n  \"_1578\": \"例如：400（EAN / UPC / GTIN）\",\n  \"_1579\": \"确认加载产品\",\n  \"_1580\": \"找到 #count# 个前缀为 \\\"#prefix#\\\" 的ASIN。是否要加载这些产品？\",\n  \"_1581\": \"未找到前缀为 \\\"#prefix#\\\" 的ASIN。\",\n  \"_1582\": \"获取前缀的ASIN时出错。\"\n}\n", "timestamp": 1753815606.4215019, "type": "response"}]