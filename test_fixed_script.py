#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的脚本
"""

import sys
import time
from 历史价格8 import KeepaHistoricalPriceChe<PERSON>

def test_fixed_script():
    """测试修复后的脚本"""
    print("🧪 测试修复后的脚本")
    print("=" * 50)
    
    # 创建检查器
    checker = KeepaHistoricalPriceChecker(threads=10, marketplace="US", verbose=False)
    checker.load_proxies()
    
    # 测试几个不同的ASIN
    test_asins = [
        "B07QNYTRG2",
        "B08N5WRWNW", 
        "B0BSHF7WHW",
        "B09JQMJHXY",
        "B0C1SJ1MQH"
    ]
    
    print(f"📋 测试配置:")
    print(f"   线程数: 10")
    print(f"   市场: US")
    print(f"   测试ASIN数量: {len(test_asins)}")
    print(f"   代理数量: {len(checker.proxies)}")
    
    print(f"\n🚀 开始测试...")
    start_time = time.time()
    
    results = []
    for i, asin in enumerate(test_asins, 1):
        print(f"[{i}/{len(test_asins)}] 处理 {asin}...")
        
        result = checker.reverse_engineer_keepa(asin)
        
        if result:
            highest = result.get('historical_highest_price', 'N/A')
            lowest = result.get('historical_lowest_price', 'N/A')
            
            # 检查是否是有效结果（不是$19.0）
            if highest == 19.0 and lowest == 19.0:
                print(f"   ❌ {asin}: 仍然返回默认价格 ${highest}")
                results.append({"asin": asin, "status": "default_price", "highest": highest, "lowest": lowest})
            else:
                print(f"   ✅ {asin}: 最高${highest} 最低${lowest}")
                results.append({"asin": asin, "status": "success", "highest": highest, "lowest": lowest})
        else:
            print(f"   ❌ {asin}: 无法获取价格数据")
            results.append({"asin": asin, "status": "failed", "highest": None, "lowest": None})
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n📊 测试结果统计:")
    print(f"   总耗时: {duration:.2f} 秒")
    print(f"   平均每个ASIN: {duration/len(test_asins):.2f} 秒")
    
    # 统计结果
    success_count = len([r for r in results if r["status"] == "success"])
    default_price_count = len([r for r in results if r["status"] == "default_price"])
    failed_count = len([r for r in results if r["status"] == "failed"])
    
    print(f"   成功获取真实价格: {success_count}/{len(test_asins)} ({success_count/len(test_asins)*100:.1f}%)")
    print(f"   返回默认价格($19.0): {default_price_count}/{len(test_asins)} ({default_price_count/len(test_asins)*100:.1f}%)")
    print(f"   完全失败: {failed_count}/{len(test_asins)} ({failed_count/len(test_asins)*100:.1f}%)")
    
    # 详细结果
    print(f"\n📝 详细结果:")
    for result in results:
        asin = result["asin"]
        status = result["status"]
        highest = result["highest"]
        lowest = result["lowest"]
        
        if status == "success":
            print(f"   ✅ {asin}: ${highest} - ${lowest}")
        elif status == "default_price":
            print(f"   ⚠️  {asin}: 默认价格 ${highest}")
        else:
            print(f"   ❌ {asin}: 获取失败")
    
    # 判断修复是否成功
    if default_price_count == 0:
        print(f"\n🎉 修复成功！所有ASIN都不再返回默认的$19.0价格")
    elif default_price_count < len(test_asins):
        print(f"\n⚠️  部分修复成功，但仍有{default_price_count}个ASIN返回默认价格")
    else:
        print(f"\n❌ 修复失败，所有ASIN仍然返回默认的$19.0价格")
    
    return results

if __name__ == "__main__":
    test_fixed_script()
