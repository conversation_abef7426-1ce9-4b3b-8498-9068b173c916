#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Keepa 401验证绕过功能
"""

import sys
import os
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from improved_websocket_handler import Keepa401Bypasser

def test_401_bypass():
    """测试401绕过功能"""
    print("🔓 测试Keepa 401验证绕过功能...")
    print("=" * 80)
    
    # 创建401绕过器
    bypasser = Keepa401Bypasser(verbose=True)
    
    # 测试ASIN列表 - 包含之前失败的和成功的
    test_asins = [
        "B07TZDH6G4",  # 之前成功的ASIN（对比基准）
        "B07SPTL99F",  # 之前失败的ASIN
        "B07TXJKT46",  # 之前失败的ASIN
        "B08N5WRWNW",  # 新的测试ASIN
    ]
    
    results = {}
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n🎯 [{i}/{len(test_asins)}] 测试ASIN: {asin}")
        print("-" * 60)
        
        start_time = time.time()
        
        try:
            # 使用401绕过方法
            result = bypasser.reverse_engineer_keepa_improved(asin)
            
            elapsed_time = time.time() - start_time
            
            if result:
                if isinstance(result, dict) and result.get('_bypass_failed'):
                    print(f"❌ {asin}: 401绕过失败 (401错误数: {result.get('_401_count', 0)})")
                    results[asin] = "绕过失败"
                else:
                    highest = result.get('historical_highest_price', '未知')
                    lowest = result.get('historical_lowest_price', '未知')
                    stock_status = result.get('out_of_stock_within_month', 0)
                    
                    stock_text = "有库存" if stock_status == 2 else "缺货" if stock_status == 1 else "未知"
                    
                    print(f"✅ {asin}: 401绕过成功!")
                    print(f"   📊 最高价格: ${highest}")
                    print(f"   📊 最低价格: ${lowest}")
                    print(f"   📦 库存状态: {stock_text}")
                    print(f"   ⏱️  处理时间: {elapsed_time:.1f}s")
                    
                    results[asin] = "绕过成功"
            else:
                print(f"❌ {asin}: 401绕过失败 - 无数据返回")
                results[asin] = "无数据"
                
        except Exception as e:
            elapsed_time = time.time() - start_time
            print(f"❌ {asin}: 401绕过异常 - {str(e)}")
            print(f"   ⏱️  处理时间: {elapsed_time:.1f}s")
            results[asin] = f"异常: {str(e)}"
        
        # 在测试之间添加延迟，避免过于频繁的请求
        if i < len(test_asins):
            print("⏳ 等待10秒后继续下一个测试...")
            time.sleep(10)
    
    # 输出测试总结
    print("\n" + "=" * 80)
    print("📋 401绕过测试总结")
    print("=" * 80)
    
    success_count = 0
    bypass_failed_count = 0
    error_count = 0
    
    for asin, status in results.items():
        if status == "绕过成功":
            success_count += 1
            print(f"✅ {asin}: {status}")
        elif status == "绕过失败":
            bypass_failed_count += 1
            print(f"🔒 {asin}: {status}")
        else:
            error_count += 1
            print(f"❌ {asin}: {status}")
    
    total_tests = len(results)
    success_rate = (success_count / total_tests * 100) if total_tests > 0 else 0
    
    print(f"\n📊 统计结果:")
    print(f"   总测试数: {total_tests}")
    print(f"   绕过成功: {success_count}")
    print(f"   绕过失败: {bypass_failed_count}")
    print(f"   其他错误: {error_count}")
    print(f"   成功率: {success_rate:.1f}%")
    
    if success_rate > 50:
        print("\n🎉 401绕过功能基本可用!")
    elif success_rate > 0:
        print("\n⚠️ 401绕过功能部分可用，需要进一步优化")
    else:
        print("\n❌ 401绕过功能暂时无效，需要重新设计")
    
    return results

def test_single_asin():
    """测试单个ASIN的详细过程"""
    print("🔍 单个ASIN详细测试...")
    print("=" * 80)
    
    # 使用之前确认有数据的ASIN
    test_asin = "B07TZDH6G4"
    
    bypasser = Keepa401Bypasser(verbose=True)
    
    print(f"🎯 详细测试ASIN: {test_asin}")
    print("-" * 60)
    
    result = bypasser.reverse_engineer_keepa_improved(test_asin)
    
    if result:
        print(f"\n✅ 详细测试成功!")
        print(f"📋 完整结果: {result}")
    else:
        print(f"\n❌ 详细测试失败")
    
    return result

if __name__ == "__main__":
    print("🚀 启动Keepa 401验证绕过测试...")
    print("🎯 目标: 破解Keepa的反爬虫保护，获取历史价格数据")
    print("🛠️ 方法: Cloudflare Turnstile绕过 + 高级浏览器伪装 + WebSocket监听")
    print()
    
    # 首先进行单个ASIN的详细测试
    print("第一阶段: 单个ASIN详细测试")
    single_result = test_single_asin()
    
    print("\n" + "="*80)
    
    # 然后进行批量测试
    print("第二阶段: 批量401绕过测试")
    batch_results = test_401_bypass()
    
    print("\n🏁 所有测试完成!")
