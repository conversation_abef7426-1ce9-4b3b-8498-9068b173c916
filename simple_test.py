#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入"""
    try:
        from 历史价格8 import KeepaHistoricalPriceChecker
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        from 历史价格8 import KeepaHistoricalPriceChecker
        
        print("🔍 创建分析器实例...")
        analyzer = KeepaHistoricalPriceChecker(verbose=True)
        print("✅ 实例创建成功")
        
        print("🔍 测试单个ASIN...")
        test_asin = "B07QNYTRG2"
        
        result = analyzer.reverse_engineer_keepa(test_asin)
        
        if result:
            print("✅ 获取价格数据成功:")
            print(f"   ASIN: {result.get('asin')}")
            print(f"   最高价: ${result.get('historical_highest_price', 0):.2f}")
            print(f"   最低价: ${result.get('historical_lowest_price', 0):.2f}")
            if result.get('current_price'):
                print(f"   当前价格: ${result.get('current_price'):.2f}")
            return True
        else:
            print("❌ 未获取到价格数据")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始简单测试...")
    print("=" * 50)
    
    # 测试导入
    if not test_import():
        return False
    
    # 测试基本功能
    if not test_basic_functionality():
        return False
    
    print("\n🎉 所有测试通过！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
