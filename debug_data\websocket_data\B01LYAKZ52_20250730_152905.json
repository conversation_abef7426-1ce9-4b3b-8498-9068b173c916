{"asin": "B01LYAKZ52", "timestamp": "20250730_152905", "message_count": 5, "messages": [{"status": 108, "actionUrl": "https://keepa.com/#!extguest", "guest": 1, "n": 71}, {"exchange": {"1": 1.0, "2": 0.748682, "3": 0.865802, "4": 0.865802, "5": 148.501, "6": 1.377645, "7": 7.1769, "8": 0.865802, "9": 0.865802, "10": 87.037458, "11": 18.751395, "12": 5.5685, "13": 1.535376, "14": 0.865802}, "id": 10125, "json": "{\"status\":-2}", "lastUpdate": 0, "startTimeStamp": 1753860538281, "status": 401, "timeStamp": 1753860538281, "version": 2}, {"id": 10126, "lastUpdate": 0, "startTimeStamp": 1753860538709, "status": 200, "timeStamp": 1753860538709, "version": 3}, {"basicProducts": [{"asin": "B01LYAKZ52", "categories": [], "csv": [[3217340, -1], [3217340, 599, 3221302, -1, 3227804, 599, 3361304, -1, 3364300, 599, 3453760, -1, 3472360, 599, 3502100, 664, 3732840, -1, 3782872, 664, 3814020, -1, 3862092, 664, 3930072, -1, 3938784, 664, 4160280, -1, 4199906, 664, 4471740, -1, 4511084, 664, 4518628, 594, 4602270, 590, 4723092, -1, 4885122, 560, 4921100, -1, 4978548, 590, 5012448, -1, 5141928, 590, 5162664, -1, 5170632, 590, 5225728, -1, 5272372, 690, 5338152, -1, 5364244, 690, 5367750, -1, 5382468, 690, 5388304, -1, 5637942, 690, 5751604, -1, 5772062, 690, 5784792, -1, 5963912, 690, 6057964, -1, 6109016, 690, 6307208, -1, 6383204, 690, 6880020, 399, 6909872, -1, 7666268, 1152], [3217340, -1], [3217340, -2], [3217340, 599, 3221302, -1, 3227804, 599, 3361304, -1, 3364300, 599, 3453760, -1, 3472360, 599, 3502100, 664, 3732840, -1, 3782872, 664, 3814020, -1, 3862092, 664, 3930072, -1, 3938784, 664, 4160280, -1, 4199906, 664, 4471740, -1, 4511084, 664, 4518628, 594, 4602270, 590, 4723092, -1, 4885392, 960, 4921100, -1, 5170632, 960, 5170868, -1, 5181600, 960, 5181870, -1, 5196428, 960, 5196572, -1, 5218840, 960, 5221712, -1, 5272372, 960, 5273430, -1, 5283276, 960, 5283588, -1, 5294656, 960, 5297140, -1, 5306168, 960, 5307542, -1, 5317616, 960, 5318870, -1, 5330402, 960, 5331832, -1, 5646036, 960, 5647820, -1, 5662734, 960, 5682420, -1, 5701880, 960, 5730508, -1, 5742554, 960, 5751604, -1, 5970428, 960, 5977044, -1, 5985340, 960, 6006676, -1, 6029092, 960, 6048548, -1, 6057964, 960, 6062912, -1, 6115128, 960, 6128100, -1, 6148322, 960, 6175672, -1, 6195600, 960, 6217924, -1, 6232836, 960, 6259542, -1, 6267420, 960, 6268652, -1, 6287744, 960, 6287926, -1, 6297764, 960, 6298222, -1, 6307208, 960, 6444612, -1, 6470968, 960, 6477720, -1, 6503252, 960, 6514146, -1, 6526476, 960, 6538894, -1, 6635976, 960, 6639752, -1, 6648244, 960, 6665932, -1, 6683604, 960, 6695188, -1, 6752640, 960, 6799496, -1, 6883954, 960, 6884084, -1, 6909872, 960, 6911212, -1, 6959040, 960, 7008700, -1, 7555436, 960], null, null, null, null, null, [3312972, 599, 3716616, 664, 4471740, -1, 4573444, 594, 4602270, 590, 4723092, -1, 5147160, 590, 5162664, -1, 5182704, 590, 5225728, -1, 5364244, 690, 5367750, -1, 5382596, 690, 5388304, -1, 5723872, 690, 5751604, -1, 6259542, 690, 6307208, -1, 6433454, 690, 6909872, -1], [3217340, 1, 3221302, -1, 3227804, 1, 3361304, -1, 3364300, 1, 3453760, -1, 3472360, 1, 3732840, -1, 3782872, 1, 3814020, -1, 3862092, 1, 3930072, -1, 3938784, 1, 4160280, -1, 4199906, 1, 4471740, -1, 4511084, 1, 4723092, -1, 4885122, 1, 4921100, -1, 4978548, 1, 5012448, -1, 5141928, 1, 5162664, -1, 5170632, 1, 5225728, -1, 5272372, 1, 5338152, -1, 5364244, 1, 5367750, -1, 5382468, 1, 5388304, -1, 5637942, 1, 5751604, -1, 5772062, 1, 5784792, -1, 5963912, 1, 6057964, -1, 6109016, 1, 6307208, -1, 6383204, 1, 6909872, -1, 7666268, 1], null, null, null, [3312972, 1, 3318768, 1, 3346070, 1, 3375088, 1, 3401840, 1, 3427972, 1, 3444664, 1, 3468836, 0, 3716616, 1, 3720668, 1, 3730456, 1, 3800960, 1, 3879754, 1, 3897712, 1, 3939900, 1, 3985156, 1, 4034072, 1, 4036236, 1, 4036304, 1, 4081388, 1, 4127948, 1, 4152212, 1, 4215012, 1, 4220930, 1, 4227628, 1, 4234592, 1, 4317310, 1, 4329412, 1, 4385612, 1, 4416506, 1, 4573444, 1, 4602270, 1, 5147160, 1, 5182704, 1, 5206716, 1, 5364244, 1, 5382596, 1, 5384500, 1, 5723872, 1, 5736670, 1, 6259542, 1, 6433454, 1, 6608010, 1, 6752640, 1], [6860516, 37], [6259542, 43], [3312972, 599, 0, 3468836, -1, -1, 3716616, 664, 0, 4573444, 594, 0, 4602270, 590, 0, 4855312, -1, 0, 4885122, 560, 0, 4921100, -1, -1, 4978548, 590, 0, 5012448, -1, -1, 5141928, 590, 0, 5162664, -1, -1, 5170632, 590, 0, 5225728, -1, -1, 5272372, 690, 0, 5338152, -1, -1, 5364244, 690, 0, 5367750, -1, -1, 5382468, 690, 0, 5388304, -1, -1, 5637942, 690, 0, 5751604, -1, -1, 5772062, 690, 0, 5784792, -1, -1, 5963912, 690, 0, 6057964, -1, -1, 6109016, 690, 0, 6307208, -1, -1, 6383204, 690, 0, 6880020, 399, 0, 6909872, -1, -1], null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "domainId": 1, "eanList": ["0615339700752"], "imagesCSV": "31hWvdnycFL.jpg,11TiJGyuQLL.jpg,41Ff+kKajiL.jpg,61Byms5rMvL.jpg,31OJ3K4ZnkL.jpg", "lastPriceChange": 7666268, "lastUpdate": 7666904, "manufacturer": "INNO+ALL", "productType": 0, "stats": null, "title": "INNO+AL<PERSON>, Adjustable Bow Ties for Dogs/Cats/Pets (Red/Black/Blue)", "trackingSince": 3217340, "upcList": ["615339700752"]}], "id": 10127, "lastUpdate": 0, "startTimeStamp": 1753860538709, "status": 200, "timeStamp": 1753860538709, "version": 3}, {"id": 10128, "json": "{\"status\":-2}", "lastUpdate": 0, "startTimeStamp": 1753860539048, "status": 401, "timeStamp": 1753860539049, "version": 2}]}