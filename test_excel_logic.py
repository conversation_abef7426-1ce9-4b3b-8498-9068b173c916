#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Excel保存逻辑
"""

import pandas as pd

# 模拟数据
test_results = {
    'B079ZBVS1V': {
        'historical_highest_price': 9.99,
        'historical_lowest_price': 8.01,
        'last_price_change': {
            'type': 'days',
            'value': 10,
            'days_ago': 10,
            'text': '10天前'
        }
    },
    'B07DK41XBT': {
        'historical_highest_price': 20.99,
        'historical_lowest_price': 20.99,
        'last_price_change': {
            'type': 'months',
            'value': 2,
            'days_ago': 60,
            'text': '2个月前'
        }
    },
    'B00BSJZCOG': {
        'historical_highest_price': None,
        'historical_lowest_price': None,
        'last_price_change': None
    }
}

# 创建测试DataFrame
df = pd.DataFrame({
    'ASIN': ['B079ZBVS1V', 'B07DK41XBT', 'B00BSJZCOG'],
    '历史最高价': [None, None, None],
    '历史最低价': [None, None, None],
    '断货状态': ['未知', '未知', '未知'],
    '价格符号': ['$', '$', '$']
})

print("=== 原始DataFrame ===")
print(df)
print()

# 应用逻辑
for asin, data in test_results.items():
    mask = df['ASIN'] == asin
    if mask.any():
        df.loc[mask, '历史最高价'] = data.get('historical_highest_price')
        df.loc[mask, '历史最低价'] = data.get('historical_lowest_price')
        
        # 基于价格变动时间确定分类
        last_change = data.get('last_price_change')
        print(f"处理 {asin}: last_change = {last_change}")
        
        if last_change and last_change.get('days_ago') is not None:
            days_ago = last_change['days_ago']
            if days_ago <= 15:
                df.loc[mask, '断货状态'] = '15天内'
                print(f"  设置为 '15天内' (days_ago={days_ago})")
            else:
                df.loc[mask, '断货状态'] = '15天外'
                print(f"  设置为 '15天外' (days_ago={days_ago})")
        else:
            df.loc[mask, '断货状态'] = '未知'
            print(f"  设置为 '未知' (last_change为空或无days_ago)")

print()
print("=== 处理后的DataFrame ===")
print(df)
print()

print("=== 断货状态分布 ===")
print(df['断货状态'].value_counts())
