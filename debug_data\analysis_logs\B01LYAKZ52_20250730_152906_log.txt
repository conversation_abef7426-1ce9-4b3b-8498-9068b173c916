调试日志 - ASIN: B01LYAKZ52
时间: 20250730_152906
============================================================

[15:28:53] [B01LYAKZ52] 开始WebSocket数据获取
[15:28:55] [B01LYAKZ52] 访问页面: https://keepa.com/#!product/1-B01LYAKZ52
[15:28:57] [B01LYAKZ52] WebSocket连接建立
[15:28:57] [B01LYAKZ52] 页面加载完成，等待WebSocket数据
[15:28:57] [B01LYAKZ52] 收到未压缩WebSocket消息: 81 字符
[15:28:58] [B01LYAKZ52] 收到未压缩WebSocket消息: 366 字符
[15:28:58] [B01LYAKZ52] 收到未压缩WebSocket消息: 120 字符
[15:28:58] [B01LYAKZ52] 收到未压缩WebSocket消息: 4599 字符
[15:28:58] [B01LYAKZ52] 收到未压缩WebSocket消息: 145 字符
[15:29:05] [B01LYAKZ52] 页面内容长度: 202664 字符
[15:29:05] [B01LYAKZ52] 页面内容已保存: debug_data\html_pages\B01LYAKZ52_20250730_152905.html
[15:29:05] [B01LYAKZ52] WebSocket数据已保存: debug_data\websocket_data\B01LYAKZ52_20250730_152905.json
[15:29:05] [B01LYAKZ52] 收到 5 条WebSocket消息
[15:29:05] [B01LYAKZ52] 开始解析 5 条WebSocket消息
[15:29:05] [B01LYAKZ52] 解析消息 1: <class 'dict'>
[15:29:05] [B01LYAKZ52] 消息不包含basicProducts，包含键: ['status', 'actionUrl', 'guest', 'n']
[15:29:05] [B01LYAKZ52] 解析消息 2: <class 'dict'>
[15:29:05] [B01LYAKZ52] 消息不包含basicProducts，包含键: ['exchange', 'id', 'json', 'lastUpdate', 'startTimeStamp', 'status', 'timeStamp', 'version']
[15:29:05] [B01LYAKZ52] 解析消息 3: <class 'dict'>
[15:29:05] [B01LYAKZ52] 消息不包含basicProducts，包含键: ['id', 'lastUpdate', 'startTimeStamp', 'status', 'timeStamp', 'version']
[15:29:05] [B01LYAKZ52] 解析消息 4: <class 'dict'>
[15:29:05] [B01LYAKZ52] 找到basicProducts，包含 1 个产品
[15:29:05] [B01LYAKZ52] 产品 1: ASIN=B01LYAKZ52
[15:29:05] [B01LYAKZ52] 找到匹配的ASIN，开始解析产品数据
[15:29:05] [B01LYAKZ52] 产品标题: INNO+ALL, Adjustable Bow Ties for Dogs/Cats/Pets (...
[15:29:05] [B01LYAKZ52] CSV数据长度: 34
[15:29:05] [B01LYAKZ52] Amazon价格数据: 0 个价格点
[15:29:05] [B01LYAKZ52] 第三方价格数据: 26 个价格点
[15:29:05] [B01LYAKZ52] 总价格点数: 26
[15:29:05] [B01LYAKZ52] 过滤后价格点数: 26
[15:29:05] [B01LYAKZ52] 价格范围: $3.99 - $11.52
[15:29:05] [B01LYAKZ52] 当前第三方价格: $11.52
[15:29:05] [B01LYAKZ52] 产品数据解析完成
[15:29:05] [B01LYAKZ52] 产品数据解析成功
