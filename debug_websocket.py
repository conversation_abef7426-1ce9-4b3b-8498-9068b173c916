#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试WebSocket方法
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_websocket_method():
    """测试WebSocket方法是否被正确调用"""
    try:
        from 历史价格8 import KeepaHistoricalPriceChecker
        
        print("🔍 创建分析器实例...")
        analyzer = KeepaHistoricalPriceChecker(verbose=True)
        
        print("🔍 检查reverse_engineer_keepa方法...")
        
        # 检查方法是否存在
        if hasattr(analyzer, 'reverse_engineer_keepa'):
            print("✅ reverse_engineer_keepa方法存在")
        else:
            print("❌ reverse_engineer_keepa方法不存在")
            return False
        
        # 检查WebSocket相关方法是否存在
        websocket_methods = [
            'get_keepa_websocket_data',
            'handle_websocket_message', 
            'parse_websocket_messages',
            'parse_keepa_product_data',
            'is_valid_websocket_result'
        ]
        
        for method_name in websocket_methods:
            if hasattr(analyzer, method_name):
                print(f"✅ {method_name}方法存在")
            else:
                print(f"❌ {method_name}方法不存在")
        
        print("\n🔍 测试单个ASIN处理...")
        test_asin = "B07QNYTRG2"
        
        # 添加调试信息
        print(f"📱 开始处理ASIN: {test_asin}")
        
        try:
            result = analyzer.reverse_engineer_keepa(test_asin)
            
            if result:
                print("✅ 获取到结果:")
                print(f"   ASIN: {result.get('asin')}")
                print(f"   数据源: {result.get('source', 'unknown')}")
                print(f"   最高价: ${result.get('historical_highest_price', 0):.2f}")
                print(f"   最低价: ${result.get('historical_lowest_price', 0):.2f}")
                if result.get('current_price'):
                    print(f"   当前价格: ${result.get('current_price'):.2f}")
                return True
            else:
                print("❌ 未获取到结果")
                return False
                
        except Exception as e:
            print(f"❌ 处理异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_imports():
    """检查导入是否正常"""
    print("🔍 检查导入...")
    
    try:
        import asyncio
        print("✅ asyncio导入成功")
    except Exception as e:
        print(f"❌ asyncio导入失败: {str(e)}")
    
    try:
        from playwright.async_api import async_playwright
        print("✅ playwright导入成功")
    except Exception as e:
        print(f"❌ playwright导入失败: {str(e)}")
    
    try:
        import gzip
        print("✅ gzip导入成功")
    except Exception as e:
        print(f"❌ gzip导入失败: {str(e)}")
    
    try:
        import json
        print("✅ json导入成功")
    except Exception as e:
        print(f"❌ json导入失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 开始WebSocket调试...")
    print("=" * 50)
    
    # 检查导入
    check_imports()
    
    print("\n" + "=" * 50)
    
    # 测试WebSocket方法
    success = test_websocket_method()
    
    print("\n" + "=" * 50)
    print(f"📋 调试结果: {'✅ 成功' if success else '❌ 失败'}")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
