#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户提供的ASIN: B07TZDH6G4
验证是否能获取到历史价格数据
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def test_specific_asin():
    """测试特定ASIN"""
    print("🔍 测试用户提供的ASIN: B07TZDH6G4")
    print("=" * 60)
    
    # 创建分析器
    analyzer = KeepaHistoricalPriceChecker(verbose=True)
    
    test_asin = "B07TZDH6G4"
    
    print(f"📱 测试ASIN: {test_asin}")
    print(f"🌐 Keepa页面: https://keepa.com/#!product/1-{test_asin}")
    
    try:
        # 使用当前的WebSocket方法
        result = analyzer.reverse_engineer_keepa(test_asin)
        
        if result and analyzer.is_valid_websocket_result(result):
            print(f"\n✅ 当前方法成功获取数据:")
            print(f"   ASIN: {result.get('asin', test_asin)}")
            print(f"   标题: {result.get('title', 'Unknown')[:50]}...")
            print(f"   历史最高价: ${result.get('historical_highest_price', 0):.2f}")
            print(f"   历史最低价: ${result.get('historical_lowest_price', 0):.2f}")
            if result.get('current_price'):
                print(f"   当前价格: ${result.get('current_price'):.2f}")
            print(f"   数据源: {result.get('source', 'WebSocket')}")
            return True
        else:
            print(f"\n❌ 当前方法未获取到数据")
            print(f"💡 这证实了用户的观点：需要获取'全部'范围的历史数据")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 验证用户反馈的问题...")
    
    success = test_specific_asin()
    
    print("\n" + "=" * 60)
    print("📋 分析结果:")
    
    if success:
        print("✅ 当前方法能获取到这个ASIN的数据")
        print("💡 可能其他ASIN需要不同的处理方式")
    else:
        print("❌ 当前方法确实无法获取这个ASIN的数据")
        print("💡 用户的反馈是正确的：需要设置时间范围为'全部'")
        print("\n🔧 需要修复的问题:")
        print("   1. WebSocket请求需要包含完整的时间范围参数")
        print("   2. 或者在页面加载后设置时间范围为'全部'")
        print("   3. 确保获取所有历史价格数据，而不仅仅是最近的数据")

if __name__ == "__main__":
    main()
