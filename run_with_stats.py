#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行主程序并显示统计信息
"""

import sys
import os
import time
import threading
from collections import defaultdict

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 统计信息
stats = {
    'total_processed': 0,
    'successful': 0,
    'failed': 0,
    'network_errors': 0,
    'start_time': None,
    'successful_asins': [],
    'failed_asins': []
}

# 线程锁
stats_lock = threading.Lock()

def update_stats(asin, success, has_network_error=False):
    """更新统计信息"""
    with stats_lock:
        stats['total_processed'] += 1
        if success:
            stats['successful'] += 1
            stats['successful_asins'].append(asin)
        else:
            stats['failed'] += 1
            stats['failed_asins'].append(asin)
        
        if has_network_error:
            stats['network_errors'] += 1

def print_progress():
    """打印进度信息"""
    with stats_lock:
        total = stats['total_processed']
        successful = stats['successful']
        failed = stats['failed']
        network_errors = stats['network_errors']
        
        if total > 0:
            success_rate = (successful / total) * 100
            elapsed = time.time() - stats['start_time'] if stats['start_time'] else 0
            
            print(f"\n📊 当前统计 (处理了 {total} 个ASIN):")
            print(f"   ✅ 成功: {successful} ({success_rate:.1f}%)")
            print(f"   ❌ 失败: {failed}")
            print(f"   🌐 网络错误: {network_errors}")
            print(f"   ⏱️  用时: {elapsed:.1f}秒")
            
            if successful > 0:
                print(f"   🎯 最近成功的ASIN: {stats['successful_asins'][-min(3, len(stats['successful_asins'])):]}")

# 重写print函数来捕获输出并更新统计
original_print = print

def enhanced_print(*args, **kwargs):
    """增强的print函数，用于捕获统计信息"""
    message = ' '.join(str(arg) for arg in args)
    
    # 检查是否是ASIN处理结果
    if "ASIN:" in message:
        # 提取ASIN
        try:
            asin_part = message.split("ASIN:")[1].split("|")[0].strip()
            
            # 判断是否成功
            is_success = "✅" in message
            has_network_error = "达到最大重试次数" in message
            
            update_stats(asin_part, is_success, has_network_error)
            
            # 每处理10个ASIN显示一次进度
            if stats['total_processed'] % 10 == 0:
                print_progress()
                
        except:
            pass  # 如果解析失败，忽略
    
    # 调用原始print函数
    original_print(*args, **kwargs)

def main():
    """主函数"""
    print("🚀 启动增强版主程序...")
    print("=" * 60)
    
    # 记录开始时间
    stats['start_time'] = time.time()
    
    # 替换print函数
    import builtins
    builtins.print = enhanced_print
    
    try:
        # 导入并运行主程序
        from 历史价格8 import main as main_program
        
        print("📋 开始处理ASIN文件...")
        print("💡 将显示实时统计信息...")
        print("-" * 60)
        
        # 运行主程序
        main_program()
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断程序")
    except Exception as e:
        print(f"\n\n❌ 程序异常: {str(e)}")
    finally:
        # 恢复原始print函数
        builtins.print = original_print
        
        # 显示最终统计
        print("\n" + "=" * 60)
        print("📊 最终统计结果:")
        
        total = stats['total_processed']
        successful = stats['successful']
        failed = stats['failed']
        network_errors = stats['network_errors']
        elapsed = time.time() - stats['start_time'] if stats['start_time'] else 0
        
        if total > 0:
            success_rate = (successful / total) * 100
            
            print(f"   📈 总处理数: {total}")
            print(f"   ✅ 成功数: {successful} ({success_rate:.1f}%)")
            print(f"   ❌ 失败数: {failed}")
            print(f"   🌐 网络错误: {network_errors}")
            print(f"   ⏱️  总用时: {elapsed:.1f}秒")
            print(f"   📊 平均速度: {total/elapsed:.1f} ASIN/秒")
            
            if successful > 0:
                print(f"\n🎯 成功的ASIN示例:")
                for asin in stats['successful_asins'][:5]:
                    print(f"   • {asin}")
                
                if len(stats['successful_asins']) > 5:
                    print(f"   ... 还有 {len(stats['successful_asins']) - 5} 个")
            
            # 成功率分析
            if success_rate >= 30:
                print(f"\n✅ 成功率正常 ({success_rate:.1f}%)")
                print("💡 这个成功率在价格跟踪工具中是合理的")
            elif success_rate >= 10:
                print(f"\n⚠️  成功率偏低 ({success_rate:.1f}%)")
                print("💡 建议检查网络连接或代理设置")
            else:
                print(f"\n❌ 成功率很低 ({success_rate:.1f}%)")
                print("💡 可能需要检查程序配置或网络环境")
        else:
            print("   ⚠️  没有处理任何ASIN")

if __name__ == "__main__":
    main()
