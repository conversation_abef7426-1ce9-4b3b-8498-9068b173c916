#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试小批量ASIN处理，验证修复是否有效
"""

import pandas as pd
import sys
import os

# 创建一个小的测试文件
test_asins = ['B079ZBVS1V', 'B07DK41XBT']  # 选择2个已知有价格的ASIN

# 创建测试输入文件
test_df = pd.DataFrame({
    'ASIN': test_asins
})

test_file = 'test_small_batch.xlsx'
test_df.to_excel(test_file, index=False)

print(f"创建测试文件: {test_file}")
print(f"测试ASIN: {test_asins}")
print()

# 导入历史价格8模块
sys.path.append('.')
from 历史价格8 import KeepaHistoricalPriceChecker

# 创建检查器实例
checker = KeepaHistoricalPriceChecker(
    excel_file=test_file,
    marketplace='美国',
    verbose=True,
    threads=1  # 单线程测试
)

print("开始测试...")
try:
    # 运行检查
    success = checker.process_all_asins()
    print(f"测试完成！成功: {success}")
    
    # 检查输出文件
    output_file = test_file.replace('.xlsx', '_美国_历史价格信息.xlsx')
    if os.path.exists(output_file):
        print(f"\n=== 检查输出文件: {output_file} ===")
        df = pd.read_excel(output_file)
        print(df.to_string())
        print()
        print("=== 断货状态分布 ===")
        print(df['断货状态'].value_counts())
    else:
        print(f"输出文件不存在: {output_file}")
        
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
finally:
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"\n清理测试文件: {test_file}")
