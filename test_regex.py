#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试价格变动时间正则表达式
"""

import re

# 测试文本（基于你的日志输出）
test_texts = [
    "价格变动: 15天前",
    "价格变动: 2个月前", 
    "价格变动: 3 个月前",
    "上次价格变动: 10天前",
    "价格变动: 刚刚",
    "价格变动: 5小时前",
    "价格变动: 30分钟前"
]

# 正则表达式模式
patterns = [
    # 匹配 "价格变动: 2 个月前" 格式（实际输出格式）
    r'价格变动:\s*(\d+)\s*个?月前',
    r'价格变动:\s*(\d+)\s*天前',
    r'价格变动:\s*(\d+)\s*小时前',
    r'价格变动:\s*(\d+)\s*分钟前',
    # 匹配 "上次价格变动: 2 个月前" 格式（备用格式）
    r'上次价格变动:\s*(\d+)\s*个?月前',
    r'上次价格变动:\s*(\d+)\s*天前',
    r'上次价格变动:\s*(\d+)\s*小时前',
    r'上次价格变动:\s*(\d+)\s*分钟前',
    # 匹配刚刚
    r'价格变动:\s*刚刚',
    r'上次价格变动:\s*刚刚',
]

def extract_last_price_change_time(text):
    """提取价格变动时间"""
    print(f"测试文本: '{text}'")
    
    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            print(f"  匹配模式: {pattern}")
            print(f"  匹配结果: {match.group(0)}")
            
            if '个月' in pattern or '月' in pattern:
                months = int(match.group(1))
                days_ago = months * 30
                result = {
                    'type': 'months',
                    'value': months,
                    'days_ago': days_ago,
                    'text': f"{months}个月前"
                }
            elif '天' in pattern:
                days = int(match.group(1))
                result = {
                    'type': 'days',
                    'value': days,
                    'days_ago': days,
                    'text': f"{days}天前"
                }
            elif '小时' in pattern:
                hours = int(match.group(1))
                days_ago = hours / 24
                result = {
                    'type': 'hours',
                    'value': hours,
                    'days_ago': days_ago,
                    'text': f"{hours}小时前"
                }
            elif '分钟' in pattern:
                minutes = int(match.group(1))
                days_ago = minutes / (24 * 60)
                result = {
                    'type': 'minutes',
                    'value': minutes,
                    'days_ago': days_ago,
                    'text': f"{minutes}分钟前"
                }
            elif '刚刚' in pattern:
                result = {
                    'type': 'now',
                    'value': 0,
                    'days_ago': 0,
                    'text': "刚刚"
                }
            
            print(f"  解析结果: {result}")
            return result
    
    print("  未匹配任何模式")
    return None

print("=== 测试价格变动时间正则表达式 ===")
for text in test_texts:
    result = extract_last_price_change_time(text)
    print()
