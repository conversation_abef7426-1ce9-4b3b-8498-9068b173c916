import requests
from bs4 import BeautifulSoup
import re
import time
import random
import pandas as pd
import sys
import os
import logging
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import json
import hashlib
import platform
import subprocess
from datetime import datetime
import concurrent.futures
import threading
import asyncio
import gzip

# 内置UI主题，避免依赖外部文件
class AmazonUITheme:
    """亚马逊产品分析工具统一界面主题"""
    
    # 颜色方案
    COLORS = {
        "primary": "#356cac",     # 深蓝色 - 主色
        "secondary": "#4a90e2",   # 亮蓝色 - 次要色
        "accent": "#f89406",      # 橙色 - 强调色
        "background": "#f5f5f5",  # 浅灰色 - 背景色
        "text": "#333333",        # 深灰色 - 文本色
        "light_text": "#666666",  # 中灰色 - 次要文本
        "border": "#dddddd",      # 边框色
        "success": "#28a745",     # 成功色
        "warning": "#ffc107",     # 警告色
        "error": "#dc3545",       # 错误色
        "white": "#ffffff",       # 白色
        "light_gray": "#f0f0f0"   # 更浅的灰色
    }
    
    # 字体设置
    FONTS = {
        "title": ("微软雅黑", 12, "bold"),
        "subtitle": ("微软雅黑", 11, "bold"),
        "body": ("微软雅黑", 10),
        "small": ("微软雅黑", 9),
        "menu": ("微软雅黑", 10),
        "button": ("微软雅黑", 10),
        "status": ("微软雅黑", 9)
    }
    
    # 尺寸和间距
    PADDING = {
        "frame": 15,      # 框架内边距
        "button": 8,      # 按钮内边距
        "widget": 5,      # 控件间距
        "section": 10,    # 区块间距
        "tiny": 2,        # 最小间距
    }
    
    @classmethod
    def get_image_path(cls):
        """获取图标路径"""
        # 确定工作目录
        if getattr(sys, 'frozen', False):
            base_dir = os.path.dirname(sys.executable)
        else:
            base_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 图标文件路径
        icon_path = os.path.join(base_dir, "icon.ico")
        if os.path.exists(icon_path):
            return icon_path
        return None
    
    @classmethod
    def setup_styles(cls):
        """设置通用ttk样式"""
        style = ttk.Style()
        
        # 设置全局主题
        try:
            style.theme_use("clam")  # 使用clam主题作为基础
        except:
            pass  # 如果主题不可用，使用默认主题
        
        # 背景配置
        style.configure("TFrame", background=cls.COLORS["background"])
        style.configure("TLabelframe", background=cls.COLORS["background"])
        style.configure("TLabelframe.Label", background=cls.COLORS["background"], 
                        foreground=cls.COLORS["primary"], font=cls.FONTS["subtitle"])
        
        # 按钮风格
        style.configure("TButton", 
                        background=cls.COLORS["primary"],
                        foreground=cls.COLORS["white"],
                        font=cls.FONTS["button"],
                        padding=cls.PADDING["button"])
        
        style.map("TButton",
                  background=[('active', cls.COLORS["secondary"]), 
                              ('disabled', cls.COLORS["border"])],
                  foreground=[('disabled', cls.COLORS["light_text"])])
        
        # 次要按钮风格
        style.configure("Secondary.TButton", 
                        background=cls.COLORS["secondary"],
                        foreground=cls.COLORS["white"])
        
        # 强调按钮风格
        style.configure("Accent.TButton", 
                        background=cls.COLORS["accent"],
                        foreground=cls.COLORS["white"])
        
        # 标签风格
        style.configure("TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["text"],
                        font=cls.FONTS["body"])
        
        # 标题标签风格
        style.configure("Title.TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["primary"],
                        font=cls.FONTS["title"])
        
        # 子标题标签风格
        style.configure("Subtitle.TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["secondary"],
                        font=cls.FONTS["subtitle"])
        
        # Entry风格
        style.configure("TEntry", 
                        background=cls.COLORS["white"],
                        foreground=cls.COLORS["text"],
                        fieldbackground=cls.COLORS["white"],
                        padding=3)
        
        # Combobox风格
        style.configure("TCombobox", 
                        background=cls.COLORS["white"],
                        foreground=cls.COLORS["text"],
                        fieldbackground=cls.COLORS["white"],
                        padding=3)
        
        # 复选框风格
        style.configure("TCheckbutton", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["text"],
                        font=cls.FONTS["body"])
        
        # 单选按钮风格
        style.configure("TRadiobutton", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["text"],
                        font=cls.FONTS["body"])
        
        # 进度条风格
        style.configure("TProgressbar", 
                        background=cls.COLORS["primary"],
                        troughcolor=cls.COLORS["light_gray"])
        
        # 分隔符风格
        style.configure("TSeparator", 
                        background=cls.COLORS["border"])
        
        # Treeview (表格) 风格
        style.configure("Treeview", 
                        background=cls.COLORS["white"],
                        foreground=cls.COLORS["text"],
                        fieldbackground=cls.COLORS["white"],
                        font=cls.FONTS["body"])
        
        style.map("Treeview",
                  background=[('selected', cls.COLORS["secondary"])],
                  foreground=[('selected', cls.COLORS["white"])])
        
        style.configure("Treeview.Heading", 
                        background=cls.COLORS["primary"],
                        foreground=cls.COLORS["white"],
                        font=cls.FONTS["subtitle"],
                        relief="flat")
        
        # Notebook (选项卡) 风格
        style.configure("TNotebook", 
                        background=cls.COLORS["background"],
                        tabmargins=[2, 5, 2, 0])
        
        style.configure("TNotebook.Tab", 
                        background=cls.COLORS["light_gray"],
                        foreground=cls.COLORS["text"],
                        padding=[10, 4],
                        font=cls.FONTS["body"])
        
        style.map("TNotebook.Tab",
                  background=[('selected', cls.COLORS["primary"])],
                  foreground=[('selected', cls.COLORS["white"])],
                  expand=[('selected', [1, 1, 1, 0])])
        
        return style
    
    @classmethod
    def setup_window(cls, root, title, size="800x600", resizable=(True, True), icon=True):
        """设置窗口基本属性"""
        root.title(title)
        root.geometry(size)
        root.resizable(resizable[0], resizable[1])
        root.configure(bg=cls.COLORS["background"])
        
        # 设置图标
        if icon:
            icon_path = cls.get_image_path()
            if icon_path and os.path.exists(icon_path):
                try:
                    root.iconbitmap(icon_path)
                except:
                    pass  # 图标设置失败时忽略
        
        # 设置样式
        cls.setup_styles()
        
        return root
    
    @classmethod
    def create_title_frame(cls, parent, title_text):
        """创建标题栏框架"""
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=(cls.PADDING["frame"], 0))
        
        # 标题标签
        title_label = ttk.Label(title_frame, text=title_text, style="Title.TLabel")
        title_label.pack(side=tk.LEFT, pady=cls.PADDING["section"])
        
        # 添加分隔线
        separator = ttk.Separator(parent, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=(0, cls.PADDING["section"]))
        
        return title_frame
    
    @classmethod
    def create_footer_frame(cls, parent):
        """创建底部按钮框架"""
        # 先添加分隔线
        separator = ttk.Separator(parent, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=(cls.PADDING["section"], 0))
        
        footer_frame = ttk.Frame(parent)
        footer_frame.pack(fill=tk.X, padx=cls.PADDING["frame"], pady=cls.PADDING["section"])
        
        return footer_frame
    
    @classmethod
    def create_section_frame(cls, parent, title=None):
        """创建分区框架（可选带标题）"""
        if title:
            section_frame = ttk.LabelFrame(parent, text=title)
        else:
            section_frame = ttk.Frame(parent)
        
        section_frame.pack(fill=tk.BOTH, expand=True, padx=cls.PADDING["frame"], 
                          pady=cls.PADDING["section"])
        
        return section_frame

# 添加依赖检查函数
def check_and_install_dependencies():
    """检查并安装必要的依赖库"""
    try:
        import importlib
        import subprocess
        
        required_packages = {
            'selenium': 'selenium',
            'beautifulsoup4': 'bs4',
            'pandas': 'pandas',
            'requests': 'requests',
            'webdriver_manager': 'webdriver_manager'
        }
        
        missing_packages = []
        
        for package_name, import_name in required_packages.items():
            try:
                importlib.import_module(import_name)
            except ImportError:
                missing_packages.append(package_name)
        
        if missing_packages:
            # 简化输出，不显示具体的包名
            print("配置系统环境...")
            try:
                for package in missing_packages:
                    # 隐藏具体包名和安装过程
                    subprocess.check_call(
                        [sys.executable, "-m", "pip", "install", package],
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL
                    )
                # 简化输出
                print("系统配置完成")
                # 重新导入所有模块
                for package_name, import_name in required_packages.items():
                    if package_name in missing_packages:
                        importlib.import_module(import_name)
                return True
            except Exception:
                # 隐藏错误信息
                print("系统配置失败，请联系管理员")
                return False
        # 简化输出
        print("系统检查完成")
        return True
    except Exception:
        # 隐藏错误信息
        print("环境检查发生错误")
        return False

# 检查并安装依赖
if not check_and_install_dependencies():
    print("初始化失败，程序退出")
    sys.exit(1)

# 自定义日志过滤器，用于保护敏感信息
class SensitiveInfoFilter(logging.Filter):
    def filter(self, record):
        if record.getMessage():
            # 过滤敏感信息的模式，但保留ASIN码
            patterns = [
                (r'https?://[^\s]+', '[URL]'),  # URL
                # 移除ASIN过滤，使ASIN码在日志中可见
                (r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b', '[IP]'),  # IP地址
                (r'(?:[a-zA-Z]:\\|/)(?:[^\\/:*?"<>|\r\n]+\\)*[^\\/:*?"<>|\r\n]*', '[PATH]'),  # 文件路径
                (r'\\n', ''),  # 移除可能暴露内部逻辑的换行符
                (r'TypeError|ValueError|Exception|Error', '错误')  # 统一错误消息
            ]
            
            message = record.getMessage()
            for pattern, replacement in patterns:
                message = re.sub(pattern, replacement, message)
            
            # 更新记录的消息
            record.msg = message
            if hasattr(record, 'args') and record.args:
                record.args = ()  # 清除参数
        return True

# 禁用selenium日志
logging.getLogger('selenium').setLevel(logging.ERROR)  # 只显示错误，从WARNING提升到ERROR
logging.getLogger('urllib3').setLevel(logging.ERROR)   # 只显示错误，从WARNING提升到ERROR

# 设置应用主日志
logger = logging.getLogger('price_checker')
logger.setLevel(logging.WARNING)  # 提高日志级别，只显示警告和错误

# 添加处理器和过滤器
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))  # 简化格式，不显示时间和模块
logger.addHandler(handler)

# 添加敏感信息过滤器
info_filter = SensitiveInfoFilter()
logger.addFilter(info_filter)

# HTTP请求不需要线程本地存储

class KeepaHistoricalPriceChecker:
    def __init__(self, threads=10, marketplace="US", excel_file=None, verbose=False):
        self.excel_file = excel_file or "Amazon产品信息.xlsx"
        self.marketplace = marketplace  # 市场: US, CA, JP, UK, MX
        self.market_names = {
            "US": "美国",
            "CA": "加拿大",
            "JP": "日本",
            "UK": "英国",
            "MX": "墨西哥"
        }
        # 修改输出文件名，包含国家名称
        country_name = self.market_names.get(self.marketplace, "未知")
        # 仅使用文件名，不包含路径，保存到当前目录
        base_filename = os.path.basename(self.excel_file)
        base_name_without_ext = os.path.splitext(base_filename)[0]
        self.output_file = f"{base_name_without_ext}_{country_name}_历史价格信息.xlsx"
        self.checkpoint_file = f"{base_name_without_ext}_{country_name}_checkpoint.json"
        self.threads = threads  # 线程数量
        self.results_lock = threading.Lock()  # 用于结果字典的线程锁
        self.verbose = verbose  # 控制日志输出详细程度

        # 市场对应的货币符号
        self.currency_symbols = {
            "US": "$",
            "CA": "$",
            "JP": "¥",
            "UK": "£",
            "MX": "$"
        }

        # 运行状态控制
        self.running = False
        self.stop_requested = False

        # 初始化HTTP会话和代理
        self.session = requests.Session()
        self.session.headers = self.get_headers()

        # 创建锁，用于线程安全
        self.lock = threading.Lock()

        # 从文件加载代理
        self.proxies = self.load_proxies()
        self.invalid_proxies = []

        # 用于跟踪代理失败信息
        self.proxy_failures = {}

        print(f"已加载 {len(self.proxies)} 个代理")

        # 禁用cookies，减少被跟踪风险
        self.session.cookies.clear()

        # 设置请求延迟和重试参数
        self.download_delay = 2  # 基础请求延迟(秒)
        self.download_delay_randomize = 0.5  # 随机化因子
        self.max_retries = 5  # 最大重试次数
        self.retry_delay = 5  # 重试基础延迟(秒)
        self.retry_codes = [500, 502, 503, 504, 522, 524, 408, 429]  # 触发重试的HTTP状态码

        # 添加代理黑名单字典，保存IP和暂停时间
        self.suspended_proxies = {}

        # 请求监控
        self.request_count = 0
        self.success_count = 0
        self.error_count = 0

    def load_proxies(self):
        """从文件中加载代理信息，使用改进的格式处理"""
        proxies = []
        try:
            if not os.path.exists('proxies.txt'):
                print(f"代理文件不存在: proxies.txt")
                return proxies

            with open('proxies.txt', 'r', encoding='utf-8') as f:
                content = f.read()

            # 使用更健壮的方式分割代理块
            proxy_blocks = content.strip().split('\n\n')
            print(f"找到 {len(proxy_blocks)} 个代理块")

            for block in proxy_blocks:
                if not block.strip():
                    continue

                # 使用正则表达式提取信息，更加健壮
                protocol = re.search(r'协议:\s*(\w+)', block)
                host = re.search(r'地址:\s*([0-9.]+)', block)
                port = re.search(r'端口:\s*(\d+)', block)
                username = re.search(r'用户名:\s*(\S+)', block)
                password = re.search(r'密码:\s*(\S+)', block)

                if protocol and host and port and username and password:
                    protocol_value = protocol.group(1).lower()
                    host_value = host.group(1)
                    port_value = port.group(1)
                    username_value = username.group(1)
                    password_value = password.group(1)

                    # 构建代理URL
                    proxy_url = f"{protocol_value}://{username_value}:{password_value}@{host_value}:{port_value}"

                    # 创建代理字典
                    proxy = {
                        'http': proxy_url,
                        'https': proxy_url  # 同时设置https代理
                    }

                    proxies.append(proxy)

            print(f"成功加载 {len(proxies)} 个代理")

            # 如果没有找到任何代理，打印警告
            if not proxies:
                print("警告: 没有从文件中找到有效的代理")

            return proxies
        except Exception as e:
            print(f"加载代理时出错: {str(e)}")
            return []

    def md5_encrypt(self, string):
        md5 = hashlib.md5()
        md5.update(string.encode('utf-8'))
        return md5.hexdigest()

    def get_headers(self):
        """生成随机的请求头以减少被检测的可能性并确保获取英文页面"""
        # 创建更多User-Agent变体 - 使用更现代的浏览器版本和更多平台
        user_agents = [
            f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/{random.randint(535, 537)}.{random.randint(34, 36)} (KHTML, like Gecko) Chrome/{random.randint(109, 130)}.0.0.0 Safari/{random.randint(535, 537)}.{random.randint(34, 36)}",
            f"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:{random.randint(100, 120)}.0) Gecko/20100101 Firefox/{random.randint(100, 120)}.0",
            f"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_{random.randint(14, 15)}_{random.randint(1, 7)}) AppleWebKit/{random.randint(600, 605)}.{random.randint(1, 7)}.{random.randint(1, 10)} (KHTML, like Gecko) Version/{random.randint(14, 16)}.{random.randint(0, 7)}.{random.randint(1, 10)} Safari/{random.randint(600, 605)}.{random.randint(1, 7)}.{random.randint(1, 10)}",
            f"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/{random.randint(535, 537)}.{random.randint(34, 36)} (KHTML, like Gecko) Chrome/{random.randint(109, 130)}.0.0.0 Safari/{random.randint(535, 537)}.{random.randint(34, 36)}",
        ]

        # 随机选择一个User-Agent
        user_agent = random.choice(user_agents)

        # 只使用英语作为语言，优化语言参数强制使用美国英语
        accept_language = "en-US,en;q=0.9"

        # 随机化Content-Encoding顺序
        encoding_options = [
            "gzip, deflate, br",
            "br, gzip, deflate",
            "deflate, gzip, br"
        ]
        accept_encoding = random.choice(encoding_options)

        # 生成随机的Cache-Control值
        cache_control_options = [
            f"max-age={random.randint(0, 300)}",
            "no-cache",
            "max-age=0"
        ]
        cache_control = random.choice(cache_control_options)

        # 随机化DNT (Do Not Track)
        dnt = random.choice(["0", "1"])

        # 添加更多随机化的头信息
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": accept_language,
            "Accept-Encoding": accept_encoding,
            "Connection": "keep-alive",
            "Cache-Control": cache_control,
            "DNT": dnt,
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
            "User-Agent": user_agent,
            # 增强语言和地区设置，确保获取英文内容
            "Content-Language": "en-US",
            "X-Country": "US",
            "X-Geo-Country": "US",
            "X-Locale": "en_US",
            "X-Language": "en-US",
            "X-Currency": "USD",
            "Country": "US"
        }

        # 随机添加一些额外的头信息，增加多样性
        if random.random() > 0.7:
            headers["Referer"] = "https://www.google.com/"

        # 添加随机生成的标头，进一步混淆指纹
        random_key = f"X-{self.md5_encrypt(str(time.time()))[:8]}"
        random_value = self.md5_encrypt(str(random.randint(1000, 9999)))[:10]
        headers[random_key] = random_value

        return headers

    def get_available_proxy(self):
        """获取一个可用的代理，排除被暂停的代理"""
        # 首先清理过期的暂停代理
        current_time = time.time()
        expired_proxies = [p for p, t in self.suspended_proxies.items() if t <= current_time]
        for proxy in expired_proxies:
            self.suspended_proxies.pop(proxy)
            if self.verbose:
                print(f"代理 {proxy} 恢复可用")

        # 过滤掉被暂停的代理
        active_proxies = []
        for proxy in self.proxies:
            proxy_str = proxy if isinstance(proxy, str) else (proxy.get('http') or proxy.get('https', ''))
            if proxy_str not in self.suspended_proxies:
                active_proxies.append(proxy)

        if not active_proxies:
            if self.verbose:
                print("警告: 没有可用的代理，使用无代理模式")
            return None

        return random.choice(active_proxies)

    def make_request_with_retry(self, url, max_retries=3, custom_headers=None):
        """
        使用代理和重试机制发送HTTP请求
        :param url: 请求的URL
        :param max_retries: 最大重试次数
        :param custom_headers: 自定义请求头
        :return: 响应对象或None
        """
        retry_count = 0
        while retry_count < max_retries:
            try:
                # 获取可用代理
                proxy = self.get_available_proxy()

                # 使用自定义头部或生成新的请求头
                headers = custom_headers if custom_headers else self.get_headers()

                # 记录请求
                self.request_count += 1

                if self.verbose:
                    proxy_info = "无代理" if not proxy else proxy.get('http', '').split('@')[-1] if '@' in proxy.get('http', '') else proxy.get('http', '')
                    print(f"请求 {url} (代理: {proxy_info}, 重试: {retry_count + 1}/{max_retries})")

                # 发送请求
                response = self.session.get(
                    url,
                    headers=headers,
                    proxies=proxy,
                    timeout=30,
                    allow_redirects=True
                )

                if response.status_code == 200:
                    self.success_count += 1
                    if self.verbose:
                        print(f"请求成功: {response.status_code}")
                    return response
                elif response.status_code in self.retry_codes:
                    if self.verbose:
                        print(f"收到可重试状态码: {response.status_code}")
                    retry_count += 1
                    time.sleep(self.retry_delay + random.uniform(0, 2))
                    continue
                else:
                    if self.verbose:
                        print(f"请求失败，状态码: {response.status_code}")
                    self.error_count += 1
                    return None

            except Exception as e:
                retry_count += 1
                self.error_count += 1
                if self.verbose:
                    print(f"请求异常 ({retry_count}/{max_retries}): {str(e)[:100]}...")

                if retry_count < max_retries:
                    time.sleep(self.retry_delay + random.uniform(0, 2))
                else:
                    print(f"达到最大重试次数，请求失败")

        return None

    def analyze_request_content(self, response, asin):
        """
        静默分析，不输出任何信息
        """
        # 静默保存调试数据，不输出任何信息
        self._temp_debug_data = {
            'asin': asin,
            'response': response,
            'analysis_data': {
                'url': response.url,
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds(),
                'content_length': len(response.text),
                'content_lower': response.text.lower()
            }
        }

    def save_debug_info_on_failure(self, asin):
        """仅在逆向工程失败时保存调试信息"""
        if not hasattr(self, '_temp_debug_data'):
            return

        try:
            debug_data = self._temp_debug_data
            response = debug_data['response']
            analysis = debug_data['analysis_data']

            # 保存调试HTML文件
            debug_filename = f"debug_failed_{asin}_{int(time.time())}.html"
            with open(debug_filename, 'w', encoding='utf-8') as f:
                f.write(response.text)

            # 保存分析报告
            report_filename = f"analysis_failed_{asin}_{int(time.time())}.txt"
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(f"ASIN: {asin}\n")
                f.write(f"URL: {analysis['url']}\n")
                f.write(f"状态码: {analysis['status_code']}\n")
                f.write(f"响应时间: {analysis['response_time']:.2f}秒\n")
                f.write(f"内容长度: {analysis['content_length']} 字符\n\n")

                # 检查关键内容
                content_lower = analysis['content_lower']
                f.write("=== 关键内容检查 ===\n")
                f.write(f"包含'keepa': {'是' if 'keepa' in content_lower else '否'}\n")
                f.write(f"包含'product': {'是' if 'product' in content_lower else '否'}\n")
                f.write(f"包含'price': {'是' if 'price' in content_lower else '否'}\n")

                # 检查错误指示
                f.write(f"\n=== 错误检查 ===\n")
                error_indicators = ['404', '403', '500', 'error', 'blocked', 'captcha', 'robot']
                for indicator in error_indicators:
                    if indicator in content_lower:
                        f.write(f"发现错误指示: {indicator}\n")

            print(f"❌ 逆向工程失败，调试信息已保存:")
            print(f"   📄 HTML: {debug_filename}")
            print(f"   📋 分析: {report_filename}")

        except Exception as e:
            print(f"保存调试文件失败: {str(e)}")

    def reverse_engineer_keepa(self, asin):
        """
        基于WebSocket逆向工程的Keepa价格获取
        使用真实浏览器模拟和WebSocket连接获取准确数据
        """
        try:
            # 使用线程池执行异步WebSocket方法
            import concurrent.futures
            import asyncio

            def run_websocket_in_thread():
                """在新线程中运行WebSocket方法"""
                try:
                    # 创建新的事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        return loop.run_until_complete(self.get_keepa_websocket_data(asin))
                    finally:
                        loop.close()

                except Exception as e:
                    if self.verbose:
                        print(f"WebSocket线程执行失败: {str(e)}")
                    return None

            # 使用线程池执行WebSocket方法
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(run_websocket_in_thread)
                try:
                    # 设置超时时间为30秒
                    price_data = future.result(timeout=30)

                    if price_data and self.is_valid_websocket_result(price_data):
                        # 转换为标准格式
                        result = {
                            'asin': asin,
                            'historical_highest_price': price_data.get('historical_highest_price'),
                            'historical_lowest_price': price_data.get('historical_lowest_price'),
                            'current_price': price_data.get('current_price'),
                            'title': price_data.get('title', 'Unknown'),
                            'source': 'websocket'
                        }
                        return result

                except concurrent.futures.TimeoutError:
                    pass
                except Exception as e:
                    pass

            # 如果WebSocket失败，尝试传统方法作为备用
            return self.fallback_traditional_method(asin)

        except Exception as e:
            return self.fallback_traditional_method(asin)

    async def get_keepa_websocket_data(self, asin):
        """使用WebSocket获取Keepa价格数据"""
        try:
            from playwright.async_api import async_playwright
            import gzip
            import json

            # 存储WebSocket消息
            websocket_messages = []

            async with async_playwright() as p:
                # 配置浏览器
                proxy_config = None
                if hasattr(self, 'current_proxy') and self.current_proxy:
                    proxy_parts = self.current_proxy.split(':')
                    if len(proxy_parts) >= 4:
                        proxy_config = {
                            'server': f"socks5://{proxy_parts[1]}:{proxy_parts[2]}",
                            'username': proxy_parts[3] if len(proxy_parts) > 3 else None,
                            'password': proxy_parts[4] if len(proxy_parts) > 4 else None
                        }

                browser = await p.chromium.launch(
                    headless=True,
                    args=['--disable-blink-features=AutomationControlled']
                )

                # 定义User-Agent列表
                user_agents = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ]

                context = await browser.new_context(
                    user_agent=random.choice(user_agents),
                    proxy=proxy_config
                )

                page = await context.new_page()

                # 监听WebSocket连接
                def on_websocket(websocket):
                    websocket.on('framereceived', lambda payload: self.handle_websocket_message(payload, websocket_messages))

                page.on('websocket', on_websocket)

                try:
                    # 访问Keepa产品页面
                    keepa_url = f"https://keepa.com/#!product/1-{asin}"
                    await page.goto(keepa_url, wait_until='networkidle', timeout=30000)

                    # 等待WebSocket数据
                    await asyncio.sleep(8)

                    # 解析WebSocket消息
                    return self.parse_websocket_messages(websocket_messages, asin)

                finally:
                    await browser.close()

        except Exception as e:
            if self.verbose:
                print(f"WebSocket数据获取失败: {str(e)}")
            return None

    def handle_websocket_message(self, payload, message_list):
        """处理WebSocket消息"""
        try:
            if isinstance(payload, bytes):
                try:
                    # 尝试解压缩
                    import gzip
                    decompressed = gzip.decompress(payload)
                    json_data = json.loads(decompressed.decode('utf-8'))
                    message_list.append(json_data)
                except:
                    try:
                        # 直接解析
                        json_data = json.loads(payload.decode('utf-8'))
                        message_list.append(json_data)
                    except:
                        pass
            elif isinstance(payload, str):
                try:
                    json_data = json.loads(payload)
                    message_list.append(json_data)
                except:
                    pass
        except:
            pass

    def parse_websocket_messages(self, messages, asin):
        """解析WebSocket消息中的价格数据"""
        for message in messages:
            if isinstance(message, dict) and 'basicProducts' in message:
                products = message['basicProducts']

                for product in products:
                    if product.get('asin') == asin:
                        return self.parse_keepa_product_data(product)

        return None

    def parse_keepa_product_data(self, product):
        """解析Keepa产品数据"""
        try:
            asin = product.get('asin')
            title = product.get('title', 'Unknown')
            csv_data = product.get('csv', [])

            if not csv_data:
                return None

            all_prices = []

            # 解析Amazon价格历史 (csv[0])
            amazon_prices = []
            if len(csv_data) > 0 and csv_data[0]:
                amazon_prices = self.parse_price_array(csv_data[0])
                all_prices.extend([p for p in amazon_prices if p > 0])

            # 解析第三方新品价格 (csv[1])
            third_party_prices = []
            if len(csv_data) > 1 and csv_data[1]:
                third_party_prices = self.parse_price_array(csv_data[1])
                all_prices.extend([p for p in third_party_prices if p > 0])

            if not all_prices:
                return None

            # 计算价格统计
            highest_price = max(all_prices) / 100.0  # 转换为美元
            lowest_price = min(all_prices) / 100.0   # 转换为美元

            # 获取当前价格
            current_price = None
            if amazon_prices:
                current_price = amazon_prices[-1] / 100.0
            elif third_party_prices:
                current_price = third_party_prices[-1] / 100.0

            return {
                'asin': asin,
                'title': title,
                'historical_highest_price': highest_price,
                'historical_lowest_price': lowest_price,
                'current_price': current_price
            }

        except Exception as e:
            if self.verbose:
                print(f"解析产品数据失败: {str(e)}")
            return None

    def parse_price_array(self, price_data):
        """解析价格数组"""
        if not price_data or len(price_data) < 2:
            return []

        prices = []
        for i in range(0, len(price_data), 2):
            if i + 1 < len(price_data):
                price_cents = price_data[i + 1]
                if price_cents != -1 and price_cents > 0:
                    prices.append(price_cents)

        return prices

    def is_valid_websocket_result(self, result):
        """验证WebSocket结果是否有效"""
        if not result:
            return False

        highest = result.get('historical_highest_price')
        lowest = result.get('historical_lowest_price')

        # 检查是否有有效的价格数据
        if highest is None or lowest is None:
            return False

        # 检查价格是否合理
        if highest <= 0 or lowest <= 0 or highest < lowest:
            return False

        return True

    def fallback_traditional_method(self, asin):
        """传统方法作为备用"""
        try:
            # 直接访问产品页面
            product_url = f"https://keepa.com/#!product/1-{asin}"
            response = self.make_request_with_retry(product_url, max_retries=1)

            if response and response.status_code == 200:
                # 静默分析响应内容
                self.analyze_request_content(response, asin)

                # 尝试从页面提取价格信息
                result = self.extract_price_from_html_content(response.text, asin)
                if result and self.is_valid_price_result(result):
                    return result

            return None

        except Exception:
            return None

    def is_valid_price_result(self, result):
        """验证价格结果是否有效（不是默认的$19.0）"""
        if not result:
            return False

        highest = result.get('historical_highest_price')
        lowest = result.get('historical_lowest_price')

        # 检查是否是默认的19.0价格（来自论坛讨论）
        if highest == 19.0 and lowest == 19.0:
            return False

        # 检查是否有有效的价格数据
        if highest is None and lowest is None:
            return False

        return True

    def is_valid_websocket_result(self, result):
        """检查WebSocket结果是否有效"""
        if not result:
            return False

        # 检查是否有价格信息
        has_price = (result.get('historical_highest_price') is not None or
                    result.get('historical_lowest_price') is not None or
                    result.get('current_price') is not None)

        return has_price

    def extract_price_from_html_content(self, html_content, asin):
        """从HTML内容中提取价格信息"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            result = {
                "historical_highest_price": None,
                "historical_lowest_price": None,
                "out_of_stock_within_month": None
            }

            # 尝试寻找历史最高价和最低价
            highest_price_elements = soup.find_all('tr', class_='tracking__suggestion-item')

            # 收集所有有效的价格元素（必须同时包含data-value属性和价格符号[$, ¥或£]）
            valid_prices = []

            for elem in highest_price_elements:
                if 'data-value' in elem.attrs:
                    try:
                        # 检查该元素是否包含价格符号 $, ¥ 或 £
                        has_price_sign = False
                        for td in elem.find_all('td'):
                            if '$' in td.text or '¥' in td.text or '£' in td.text:
                                has_price_sign = True
                                break

                        # 只有包含价格符号的项目才是有效价格
                        if has_price_sign:
                            # 统一处理所有市场的价格格式，只提取数字部分
                            price_value = elem['data-value']

                            # 移除所有非数字字符（除了小数点）
                            price_value = re.sub(r'[^\d.]', '', price_value)

                            # 验证价格格式
                            if re.match(r'^\d+\.?\d*$', price_value):
                                price_value = float(price_value)
                                valid_prices.append(price_value)
                    except (ValueError, TypeError):
                        continue

            # 如果找到有效价格，设置最高价和最低价
            if valid_prices:
                result["historical_highest_price"] = max(valid_prices)
                result["historical_lowest_price"] = min(valid_prices)
            else:
                # 如果未找到有效价格，尝试传统方法
                for elem in highest_price_elements:
                    if elem.find('td') and '历史最高' in elem.find('td').text:
                        price_elem = elem.find_all('td')[-1]
                        if price_elem:
                            price_text = price_elem.text.strip()

                            # 提取所有数字（包括小数点）
                            price_match = re.search(r'[\d,.]+', price_text)
                            if price_match:
                                price_value = price_match.group(0)

                                # 移除所有非数字字符（除了小数点）
                                price_value = re.sub(r'[^\d.]', '', price_value)

                                # 验证价格格式
                                if re.match(r'^\d+\.?\d*$', price_value):
                                    result["historical_highest_price"] = float(price_value)
                                    break

            # 如果在表格中找不到，尝试在statsTable中寻找
            if result["historical_highest_price"] is None:
                stats_table = soup.find('table', id='statsTable')
                if stats_table:
                    highest_rows = stats_table.find_all('tr')
                    for row in highest_rows:
                        if row.find('td', class_='statsRow1') and '最高' in row.find('td', class_='statsRow1').text:
                            price_cells = row.find_all('td', class_='statsRow3')
                            if price_cells and len(price_cells) > 0:
                                price_text = price_cells[0].text.strip()
                                # 提取所有数字（包括小数点）
                                price_match = re.search(r'[\d,.]+', price_text)
                                if price_match:
                                    result["historical_highest_price"] = float(price_match.group(0))
                                    break

            # 简单的断货状态检测
            page_text = soup.get_text()
            if "上次价格变动" not in page_text:
                result["out_of_stock_within_month"] = 2  # 超过15天
            else:
                result["out_of_stock_within_month"] = 1  # 15天内

            return result

        except Exception as e:
            if self.verbose:
                print(f"HTML价格提取失败: {str(e)}")
            return None

    def full_reverse_engineer(self, asin):
        """完整的逆向工程流程（仅在已知端点失败时使用）"""
        try:
            # 获取主页面数据
            main_page_data = self.get_keepa_main_page(asin)
            if not main_page_data:
                return None

            # 分析JavaScript文件，寻找新的API端点
            api_endpoints = self.analyze_keepa_js()
            if api_endpoints:
                for endpoint_template in api_endpoints:
                    try:
                        result = self.try_keepa_api(endpoint_template, asin)
                        if result:
                            return result
                    except Exception:
                        continue

            # 尝试WebSocket
            ws_result = self.simulate_keepa_websocket(asin, main_page_data)
            if ws_result:
                return ws_result

            return None

        except Exception:
            # 静默处理异常
            return None

    def get_keepa_main_page(self, asin):
        """获取Keepa主页面数据"""
        marketplace_id = {"US": "1", "CA": "3", "UK": "2", "MX": "11"}.get(self.marketplace, "1")
        url = f"https://keepa.com/#!product/{marketplace_id}-{asin}"

        response = self.make_request_with_retry(url, max_retries=2)
        if not response:
            return None

        # 提取关键信息
        data = {
            'html': response.text,
            'cookies': response.cookies,
            'headers': dict(response.headers)
        }

        # 查找WebSocket服务器信息
        ws_match = re.search(r'window\.server\s*=\s*\["([^"]+)"\]', response.text)
        if ws_match:
            data['ws_server'] = ws_match.group(1)

        # 查找版本信息
        version_match = re.search(r'version=([0-9.]+)', response.text)
        if version_match:
            data['version'] = version_match.group(1)

        return data

    def analyze_keepa_js(self):
        """分析Keepa的JavaScript文件，寻找API端点"""
        print("🔍 分析Keepa JavaScript文件...")

        # 获取主要的JS文件
        js_urls = [
            "https://cdn.keepa.com/20250729/keepa.js",
            "https://cdn.keepa.com/20250524/libs.js"
        ]

        api_endpoints = []

        for js_url in js_urls:
            try:
                response = self.make_request_with_retry(js_url, max_retries=1)
                if response and response.status_code == 200:
                    js_content = response.text

                    # 查找API端点模式
                    patterns = [
                        r'["\']https://[^"\']*api[^"\']*["\']',
                        r'["\']https://[^"\']*keepa[^"\']*["\']',
                        r'/api/[^"\']*',
                        r'endpoint["\']?\s*:\s*["\']([^"\']+)["\']',
                        r'url["\']?\s*:\s*["\']([^"\']+)["\']'
                    ]

                    for pattern in patterns:
                        matches = re.findall(pattern, js_content)
                        for match in matches:
                            if isinstance(match, tuple):
                                match = match[0] if match[0] else match[1]
                            if 'keepa' in match.lower() or 'api' in match.lower():
                                api_endpoints.append(match.strip('"\''))

            except Exception as e:
                print(f"⚠️  获取JS文件失败 {js_url}: {str(e)}")

        # 添加基于逆向工程的真实API端点
        marketplace_id = {"US": "1", "CA": "3", "UK": "2", "MX": "11"}.get(self.marketplace, "1")

        # 基于Keepa实际架构的API端点
        real_endpoints = [
            # 官方API端点（需要API密钥）
            f"https://api.keepa.com/product?key=&domain={marketplace_id}&asin={{asin}}",
            f"https://api.keepa.com/query?key=&domain={marketplace_id}&type=product&asin={{asin}}",

            # 内部API端点（可能无需密钥）
            f"https://keepa.com/api/product/{marketplace_id}/{{asin}}",
            f"https://keepa.com/api/stats/{marketplace_id}/{{asin}}",
            f"https://keepa.com/api/history/{marketplace_id}/{{asin}}",

            # WebSocket相关端点
            f"https://push.keepa.com/apps/cloud/product/{marketplace_id}/{{asin}}",
            f"https://push.keepa.com/api/product/{marketplace_id}/{{asin}}",

            # 可能的AJAX端点
            f"https://keepa.com/ajax/product/{marketplace_id}/{{asin}}",
            f"https://keepa.com/data/product/{marketplace_id}/{{asin}}",
            f"https://keepa.com/product/{marketplace_id}/{{asin}}/data",

            # GraphQL端点
            f"https://graph.keepa.com/product/{marketplace_id}/{{asin}}",
            f"https://api.keepa.com/graphql?query=product(domain:{marketplace_id},asin:\"{{asin}}\")",
        ]

        # 过滤掉明显不是产品API的端点
        filtered_endpoints = []
        for endpoint in api_endpoints:
            # 排除明显不相关的URL
            if any(exclude in endpoint.lower() for exclude in [
                'github.com', 'bot.keepa.com', 'channels', 'logo', 'zip',
                'css', 'js', 'font', 'image', 'static', 'cdn'
            ]):
                continue

            # 只保留可能的API端点
            if any(include in endpoint.lower() for include in [
                'api', 'product', 'data', 'query', 'ajax', 'graphql'
            ]):
                filtered_endpoints.append(endpoint)

        # 合并真实端点和过滤后的发现端点
        all_endpoints = real_endpoints + filtered_endpoints

        print(f"📡 发现 {len(all_endpoints)} 个潜在API端点")
        return list(set(all_endpoints))  # 去重

    def try_keepa_api(self, endpoint_template, asin):
        """尝试调用Keepa API端点"""
        try:
            endpoint = endpoint_template.format(asin=asin)
            # 简化输出，不显示端点信息

            # 构造请求头，模拟浏览器
            headers = self.get_headers()
            headers.update({
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin'
            })

            response = self.make_request_with_retry(endpoint, max_retries=1, custom_headers=headers)

            if response and response.status_code == 200:
                try:
                    # 尝试解析JSON响应
                    data = response.json()
                    result = self.extract_price_from_api_response(data, asin)
                    if result:
                        if result.get('historical_highest_price') or result.get('historical_lowest_price'):
                            print(f"✅ {asin}: 最高${result.get('historical_highest_price', 'N/A')} 最低${result.get('historical_lowest_price', 'N/A')}")
                        return result
                except:
                    # 如果不是JSON，检查是否包含有用信息
                    if len(response.text) > 1000 and ('price' in response.text.lower() or 'product' in response.text.lower()):
                        result = self.parse_api_text_response(response.text, asin)
                        if result and (result.get('historical_highest_price') or result.get('historical_lowest_price')):
                            print(f"✅ {asin}: 最高${result.get('historical_highest_price', 'N/A')} 最低${result.get('historical_lowest_price', 'N/A')}")
                        return result

        except Exception:
            # 静默处理失败，不输出错误信息
            pass

        return None

    def detect_javascript_app(self, response_text):
        """
        检测是否是JavaScript单页应用
        """
        # Keepa特定的指示器
        keepa_indicators = [
            'keepa.js',
            'libs.js',
            'window.keepaApp',
            'keepa.com',
            'WebSocket'
        ]

        # 通用JavaScript应用指示器
        js_indicators = [
            'document.querySelector',
            'innerHTML',
            'React',
            'Vue',
            'Angular',
            'window.',
            'document.'
        ]

        keepa_count = sum(1 for indicator in keepa_indicators if indicator in response_text)
        js_count = sum(1 for indicator in js_indicators if indicator in response_text)

        # 如果检测到Keepa特定指示器，或者多个JS指示器，认为是JS应用
        if keepa_count >= 2 or js_count >= 3:
            return True

        return False

    def try_extract_from_js_app(self, response_text, asin):
        """
        尝试从JavaScript应用中提取数据
        """
        print(f"🔧 检测到JavaScript应用，尝试提取数据...")

        # 查找可能的内联数据
        patterns = [
            r'window\.productData\s*=\s*({.*?});',
            r'window\.keepaData\s*=\s*({.*?});',
            r'data-product\s*=\s*["\']({.*?})["\']',
            r'__INITIAL_STATE__\s*=\s*({.*?});',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, response_text, re.DOTALL)
            if matches:
                print(f"✅ 找到可能的数据: {pattern}")
                try:
                    import json
                    data = json.loads(matches[0])
                    print(f"📊 解析到数据: {str(data)[:200]}...")
                    return data
                except:
                    print(f"❌ 数据解析失败")

        # 查找WebSocket连接信息
        ws_pattern = r'new WebSocket\(["\']([^"\']+)["\']'
        ws_matches = re.findall(ws_pattern, response_text)
        if ws_matches:
            print(f"🔌 发现WebSocket连接: {ws_matches[0]}")

        return None

    def extract_price_from_api_response(self, data, asin):
        """从API响应中提取价格信息"""
        try:
            # 常见的API响应格式
            if isinstance(data, dict):
                # 检查各种可能的价格字段
                price_fields = ['price', 'currentPrice', 'highestPrice', 'lowestPrice', 'stats']

                for field in price_fields:
                    if field in data:
                        price_data = data[field]
                        if isinstance(price_data, dict):
                            highest = price_data.get('highest') or price_data.get('max') or price_data.get('high')
                            lowest = price_data.get('lowest') or price_data.get('min') or price_data.get('low')

                            if highest or lowest:
                                return {
                                    "historical_highest_price": highest,
                                    "historical_lowest_price": lowest,
                                    "out_of_stock_within_month": 1 if highest else 2
                                }

                # 检查是否有产品数组
                if 'products' in data and isinstance(data['products'], list) and len(data['products']) > 0:
                    product = data['products'][0]
                    return self.extract_price_from_api_response(product, asin)

        except Exception as e:
            print(f"❌ 解析API响应失败: {str(e)}")

        return None

    def parse_api_text_response(self, text, asin):
        """解析文本格式的API响应"""
        try:
            # 查找价格模式
            price_patterns = [
                r'highest["\']?\s*:\s*[\'"]*(\d+\.?\d*)',
                r'lowest["\']?\s*:\s*[\'"]*(\d+\.?\d*)',
                r'max["\']?\s*:\s*[\'"]*(\d+\.?\d*)',
                r'min["\']?\s*:\s*[\'"]*(\d+\.?\d*)',
                r'\$(\d+\.?\d*)',
                r'price["\']?\s*:\s*[\'"]*(\d+\.?\d*)'
            ]

            highest_price = None
            lowest_price = None

            for pattern in price_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    prices = [float(m) for m in matches if m.replace('.', '').isdigit()]
                    if prices:
                        if 'highest' in pattern or 'max' in pattern:
                            highest_price = max(prices)
                        elif 'lowest' in pattern or 'min' in pattern:
                            lowest_price = min(prices)
                        else:
                            # 通用价格，取最高和最低
                            highest_price = max(prices) if not highest_price else max(highest_price, max(prices))
                            lowest_price = min(prices) if not lowest_price else min(lowest_price, min(prices))

            if highest_price or lowest_price:
                return {
                    "historical_highest_price": highest_price,
                    "historical_lowest_price": lowest_price,
                    "out_of_stock_within_month": 1 if highest_price else 2
                }

        except Exception as e:
            print(f"❌ 解析文本响应失败: {str(e)}")

        return None

    def simulate_keepa_websocket(self, asin, main_page_data):
        """模拟Keepa的WebSocket通信，包含认证处理"""
        print(f"🔌 尝试模拟WebSocket通信获取 {asin}")

        try:
            import websocket
            import json
            import threading
            import time

            ws_server = main_page_data.get('ws_server', 'push.keepa.com')
            version = main_page_data.get('version', '3.0')

            ws_url = f"wss://{ws_server}/apps/cloud/?app=keepaWebsite&version={version}"

            result_data = {}
            auth_info = {}

            def on_message(ws, message):
                try:
                    # 尝试解析消息
                    if isinstance(message, bytes):
                        # 二进制消息，可能需要解压缩
                        print(f"📦 收到二进制消息: {len(message)} 字节")
                        try:
                            # 尝试解压缩二进制数据
                            import gzip
                            decompressed = gzip.decompress(message)
                            print(f"📦 解压后: {decompressed[:200]}...")
                            data = json.loads(decompressed.decode('utf-8'))
                            self.process_websocket_data(data, asin, result_data)
                        except:
                            print(f"📦 无法解压缩二进制数据")
                    else:
                        # 文本消息
                        print(f"📨 收到文本消息: {message[:200]}...")
                        data = json.loads(message)

                        # 处理认证响应
                        if 'status' in data:
                            status = data['status']
                            print(f"🔍 状态码: {status}")

                            if status == 108:  # 需要认证
                                print(f"🔑 需要认证，尝试访客模式...")
                                auth_info.update(data)
                                # 尝试发送访客认证
                                self.send_guest_auth(ws, data)
                            elif status == 0:  # 成功
                                print(f"✅ 认证成功")
                                # 现在可以请求产品数据
                                self.request_product_data(ws, asin)

                        # 处理产品数据
                        self.process_websocket_data(data, asin, result_data)

                except Exception as e:
                    print(f"❌ 解析WebSocket消息失败: {str(e)}")

            def on_error(ws, error):
                print(f"❌ WebSocket错误: {error}")

            def on_close(ws, close_status_code, close_msg):
                print(f"🔌 WebSocket连接关闭: {close_status_code}")

            def on_open(ws):
                print(f"✅ WebSocket连接已建立")

                # 首先发送初始化消息
                init_msg = {"type": "init", "app": "keepaWebsite", "version": version}
                try:
                    ws.send(json.dumps(init_msg))
                    print(f"📤 发送初始化: {init_msg}")
                    time.sleep(1)
                except Exception as e:
                    print(f"❌ 发送初始化失败: {str(e)}")

            # 创建WebSocket连接
            ws = websocket.WebSocketApp(ws_url,
                                      on_open=on_open,
                                      on_message=on_message,
                                      on_error=on_error,
                                      on_close=on_close)

            # 在单独线程中运行WebSocket
            ws_thread = threading.Thread(target=ws.run_forever)
            ws_thread.daemon = True
            ws_thread.start()

            # 等待响应
            timeout = 15  # 增加超时时间
            start_time = time.time()
            while time.time() - start_time < timeout:
                if result_data:
                    break
                time.sleep(0.5)

            ws.close()

            if result_data:
                return self.extract_price_from_websocket_response(result_data['response'], asin)
            else:
                print(f"⏰ WebSocket超时，未收到产品数据")
                if auth_info:
                    print(f"🔍 认证信息: {auth_info}")
                return None

        except ImportError:
            print(f"❌ 需要安装websocket-client: pip install websocket-client")
            return None
        except Exception as e:
            print(f"❌ WebSocket通信失败: {str(e)}")
            return None

    def send_guest_auth(self, ws, auth_data):
        """发送访客认证"""
        try:
            # 基于收到的认证数据构造访客请求
            guest_msg = {
                "type": "guest",
                "n": auth_data.get("n"),
                "guest": 1
            }
            ws.send(json.dumps(guest_msg))
            print(f"📤 发送访客认证: {guest_msg}")
        except Exception as e:
            print(f"❌ 发送访客认证失败: {str(e)}")

    def request_product_data(self, ws, asin):
        """请求产品数据"""
        try:
            marketplace_id = {"US": "1", "CA": "3", "UK": "2", "MX": "11"}.get(self.marketplace, "1")

            # 尝试不同的产品请求格式
            requests = [
                {"type": "product", "domain": int(marketplace_id), "asin": asin},
                {"cmd": "product", "domain": int(marketplace_id), "asin": asin},
                {"action": "getProduct", "domain": int(marketplace_id), "asin": asin},
                {"request": "product", "marketplace": int(marketplace_id), "asin": asin}
            ]

            for req in requests:
                ws.send(json.dumps(req))
                print(f"📤 发送产品请求: {req}")
                time.sleep(1)

        except Exception as e:
            print(f"❌ 发送产品请求失败: {str(e)}")

    def process_websocket_data(self, data, asin, result_data):
        """处理WebSocket数据"""
        try:
            # 检查是否包含产品数据
            if any(key in data for key in ['product', 'stats', 'csv', 'history', 'price']):
                result_data['response'] = data
                print(f"✅ 找到产品相关数据")
                return True

            # 检查ASIN是否在数据中
            if asin in str(data):
                result_data['response'] = data
                print(f"✅ 找到ASIN相关数据")
                return True

            return False
        except Exception as e:
            print(f"❌ 处理WebSocket数据失败: {str(e)}")
            return False

    def extract_price_from_websocket_response(self, data, asin):
        """从WebSocket响应中提取价格"""
        try:
            # 这里需要根据实际的WebSocket响应格式来解析
            # 由于我们不知道确切格式，先尝试通用解析
            return self.extract_price_from_api_response(data, asin)
        except Exception as e:
            print(f"❌ 解析WebSocket响应失败: {str(e)}")
            return None

    def analyze_price_extraction_failure(self, soup, asin):
        """
        分析价格提取失败的原因
        """
        print(f"\n🔍 分析价格提取失败原因 (ASIN: {asin})")

        # 检查页面是否正确加载
        page_text = soup.get_text()

        # 1. 检查是否是正确的Keepa页面
        if "keepa" not in page_text.lower():
            print("❌ 这不是Keepa页面")
            return

        # 2. 检查是否找到了产品
        if "product not found" in page_text.lower() or "找不到产品" in page_text.lower():
            print("❌ 产品未找到")
            return

        # 3. 检查是否有价格相关的表格
        stats_table = soup.find('table', id='statsTable')
        if not stats_table:
            print("❌ 未找到statsTable")
            # 尝试查找其他可能的价格表格
            all_tables = soup.find_all('table')
            print(f"📊 页面中共有 {len(all_tables)} 个表格")
            for i, table in enumerate(all_tables):
                table_text = table.get_text()[:100]
                print(f"  表格 {i+1}: {table_text}...")
        else:
            print("✅ 找到了statsTable")

        # 4. 检查是否有tracking建议项
        tracking_items = soup.find_all('tr', class_='tracking__suggestion-item')
        print(f"📈 找到 {len(tracking_items)} 个tracking建议项")

        # 5. 搜索所有可能的价格模式
        price_patterns = [
            r'\$[\d,]+\.?\d*',  # 美元格式
            r'¥[\d,]+\.?\d*',   # 人民币格式
            r'£[\d,]+\.?\d*',   # 英镑格式
            r'€[\d,]+\.?\d*',   # 欧元格式
            r'[\d,]+\.?\d*\s*USD',  # USD后缀
        ]

        all_prices = []
        for pattern in price_patterns:
            matches = re.findall(pattern, page_text)
            all_prices.extend(matches)

        print(f"💰 在页面中找到 {len(all_prices)} 个价格模式")
        if all_prices:
            print(f"  示例价格: {all_prices[:5]}")

        # 6. 检查是否有中文价格信息
        chinese_price_keywords = ['最高价', '最低价', '当前价', '历史价格', '价格变动']
        found_chinese = []
        for keyword in chinese_price_keywords:
            if keyword in page_text:
                found_chinese.append(keyword)

        if found_chinese:
            print(f"🈶 找到中文价格关键词: {', '.join(found_chinese)}")
        else:
            print("❌ 未找到中文价格关键词")

        # 7. 检查页面语言设置
        html_lang = soup.find('html')
        if html_lang and html_lang.get('lang'):
            print(f"🌐 页面语言: {html_lang.get('lang')}")

        # 8. 保存详细的HTML结构分析
        structure_filename = f"structure_analysis_{asin}_{int(time.time())}.txt"
        try:
            with open(structure_filename, 'w', encoding='utf-8') as f:
                f.write(f"HTML结构分析 - ASIN: {asin}\n")
                f.write("=" * 50 + "\n\n")

                # 分析所有表格
                f.write("所有表格内容:\n")
                all_tables = soup.find_all('table')
                for i, table in enumerate(all_tables):
                    f.write(f"\n--- 表格 {i+1} ---\n")
                    f.write(f"ID: {table.get('id', '无')}\n")
                    f.write(f"Class: {table.get('class', '无')}\n")
                    f.write(f"内容: {table.get_text()[:500]}...\n")

                # 分析所有div
                f.write(f"\n\n包含价格关键词的div:\n")
                all_divs = soup.find_all('div')
                for div in all_divs:
                    div_text = div.get_text()
                    if any(keyword in div_text for keyword in ['price', '价格', 'highest', 'lowest', '$']):
                        f.write(f"Class: {div.get('class', '无')}\n")
                        f.write(f"内容: {div_text[:200]}...\n\n")

            print(f"📄 HTML结构分析已保存到: {structure_filename}")
        except Exception as e:
            print(f"保存结构分析失败: {str(e)}")

    def print_request_statistics(self):
        """打印请求统计信息"""
        if self.request_count > 0:
            success_rate = (self.success_count / self.request_count) * 100
            print(f"\n=== 请求统计 ===")
            print(f"总请求数: {self.request_count}")
            print(f"成功请求: {self.success_count}")
            print(f"失败请求: {self.error_count}")
            print(f"成功率: {success_rate:.1f}%")
            print(f"可用代理数: {len(self.proxies) - len(self.invalid_proxies)}")
            print("=" * 20)

    def load_asins_from_excel(self):
        """从Excel文件或txt文件中读取ASIN列表"""
        try:
            if not os.path.exists(self.excel_file):
                print(f"错误: 找不到文件 {self.excel_file}")
                return []
            
            # 获取文件扩展名
            _, file_extension = os.path.splitext(self.excel_file)
            file_extension = file_extension.lower()
            
            # 处理txt文件
            if file_extension == '.txt':
                print(f"检测到txt文件，按行读取ASIN...")
                asins = []
                with open(self.excel_file, 'r', encoding='utf-8') as file:
                    for line in file:
                        # 清理每行，去除空白字符
                        asin = line.strip().upper()
                        if asin:  # 跳过空行
                            asins.append(asin)
                
                print(f"从txt文件中加载了 {len(asins)} 个ASIN")
                return asins
            
            # 处理Excel文件
            df = pd.read_excel(self.excel_file)
            # 检查列名，不区分大小写
            asin_col = None
            for col in df.columns:
                # 完全不区分大小写比较列名
                if col.upper() == 'ASIN':
                    asin_col = col
                    break
            
            if not asin_col:
                # 尝试查找更多可能的列名变体
                possible_variants = ['ASIN', 'asin', 'Asin', 'ASin', 'ProductID', '产品ID']
                variant_found = False
                
                for variant in possible_variants:
                    if variant in df.columns:
                        asin_col = variant
                        variant_found = True
                        print(f"使用列 '{variant}' 作为ASIN列")
                        break
                
                if not variant_found:
                    print(f"错误: Excel文件中没有找到ASIN列（尝试查找：ASIN, asin, Asin等）")
                    return []
            
            asins = df[asin_col].tolist()
            # 确保ASIN格式正确
            valid_asins = [str(asin).strip().upper() for asin in asins if not pd.isna(asin) and str(asin).strip()]
            
            print(f"从Excel文件中加载了 {len(valid_asins)} 个ASIN")
            return valid_asins
        except Exception as e:
            print(f"加载ASIN时出错: {str(e)}")
            return []

    def get_keepa_info_with_browser(self, asin, max_retries=3):
        """
        使用HTTP请求从keepa.com获取ASIN的历史最高价和断货时间
        :param asin: Amazon产品的ASIN码
        :param max_retries: 最大重试次数
        :return: 包含历史最高价和断货时间的字典
        """
        result = {
            "historical_highest_price": None,
            "historical_lowest_price": None,
            "out_of_stock_within_month": None
        }

        retry_count = 0
        while retry_count < max_retries:
            try:
                # 构建URL
                domain_mapping = {
                    "US": "1",  # 美国
                    "CA": "6",  # 加拿大
                    "JP": "5",  # 日本
                    "UK": "2",  # 英国
                    "MX": "11"  # 墨西哥
                }
                marketplace_id = domain_mapping.get(self.marketplace, "5")  # 默认日本

                # 尝试使用Keepa的API接口（如果有的话）
                # 首先尝试直接的产品页面
                product_url = f"https://keepa.com/#!product/{marketplace_id}-{asin}"

                # 也尝试可能的API端点
                api_urls = [
                    product_url,
                    f"https://api.keepa.com/product?key=&domain={marketplace_id}&asin={asin}",
                    f"https://keepa.com/api/product/{marketplace_id}/{asin}",
                ]

                if self.verbose:
                    print(f"线程 {threading.current_thread().name} 请求 {asin} Keepa URL: {product_url} (市场: {self.marketplace})")

                # 使用HTTP请求获取页面
                response = None
                for url in api_urls:
                    response = self.make_request_with_retry(url, max_retries=1)
                    if response and response.status_code == 200:
                        # 检查响应是否包含有用数据
                        if 'statsTable' in response.text or 'price' in response.text.lower():
                            if self.verbose:
                                print(f"✅ 在URL {url} 找到有用数据")
                            break
                        elif len(response.text) > 20000:  # 如果内容很长，可能是完整页面
                            if self.verbose:
                                print(f"📄 URL {url} 返回完整页面")
                            break
                    if self.verbose and response:
                        print(f"❌ URL {url} 无有用数据 (状态: {response.status_code}, 长度: {len(response.text)})")

                if not response:
                    response = self.make_request_with_retry(product_url, max_retries=3)
                if not response:
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"请求失败，第 {retry_count} 次重试...")
                        time.sleep(5)
                        continue
                    else:
                        print(f"达到最大重试次数，跳过ASIN: {asin}")
                        break

                # 直接使用WebSocket方法获取数据（与test_known_good_asin.py相同的方法）
                reverse_result = self.reverse_engineer_keepa(asin)
                if reverse_result and self.is_valid_websocket_result(reverse_result):
                    # 转换为主程序期望的格式
                    result = {
                        "historical_highest_price": reverse_result.get('historical_highest_price'),
                        "historical_lowest_price": reverse_result.get('historical_lowest_price'),
                        "out_of_stock_within_month": 1 if reverse_result.get('current_price') else 2
                    }

                    # 输出成功结果
                    price_symbol = self.currency_symbols.get(self.marketplace, "$")
                    highest_price_str = f"{price_symbol}{result['historical_highest_price']:.2f}" if result['historical_highest_price'] else "未知"
                    lowest_price_str = f"{price_symbol}{result['historical_lowest_price']:.2f}" if result['historical_lowest_price'] else "未知"
                    stock_status = "有货" if result['out_of_stock_within_month'] == 1 else "断货"
                    print(f"✅ ASIN: {asin} | 历史最高价: {highest_price_str} | 历史最低价: {lowest_price_str} | 状态: {stock_status}")

                    return result

                # 如果WebSocket方法失败，直接返回空结果，不进行HTML解析
                # 输出简单的结果信息
                price_symbol = self.currency_symbols.get(self.marketplace, "$")
                print(f"❌ ASIN: {asin} | 历史最高价: 未知 | 历史最低价: 未知 | 断货状态: 未知")

                # 返回空结果
                return result

            except Exception as e:
                retry_count += 1
                error_msg = str(e)
                print(f"请求Keepa信息时出错 ({retry_count}/{max_retries}): {error_msg[:50]}...")

                if retry_count < max_retries:
                    print(f"将在5秒后重试...")
                    time.sleep(5)
                else:
                    print(f"已达最大重试次数，跳过ASIN: {asin}")

        # 达到最大重试次数，返回空结果
        return result

    def process_asin_batch(self, asins, all_results):
        """
        处理一批ASIN并更新结果
        :param asins: 要处理的ASIN列表
        :param all_results: 共享的结果字典
        """
        batch_results = {}
        
        try:
            for i, asin in enumerate(asins):
                # 检查是否要求停止
                if self.stop_requested:
                    print(f"线程 {threading.current_thread().name} 收到停止请求，中断处理")
                    break
                
                # 简化输出
                pass
                
                try:
                    # 使用HTTP请求方法获取信息
                    result = self.get_keepa_info_with_browser(asin, max_retries=3)
                    batch_results[asin] = result
                except Exception as e:
                    print(f"处理ASIN {asin} 时遇到严重错误: {str(e)[:100]}...")
                    # 记录一个空结果，确保不中断批次处理
                    batch_results[asin] = {
                        "historical_highest_price": None,
                        "historical_lowest_price": None,
                        "out_of_stock_within_month": None
                    }
                
                # 每处理完5个ASIN保存一次临时结果到共享结果字典
                if (i + 1) % 5 == 0 or i == len(asins) - 1:
                    with self.results_lock:
                        all_results.update(batch_results)
                        # 实时更新Excel文件
                        self.update_excel_with_historical_data(all_results)
                        # 保存进度检查点
                        self.save_checkpoint(asin)
                    
                # 移除延迟，直接继续处理
                pass
        except Exception as e:
            # 捕获批次处理中的任何异常
            print(f"批次处理发生严重错误: {str(e)[:100]}...")
            # 尝试保存已处理的结果
            with self.results_lock:
                if batch_results:
                    all_results.update(batch_results)
                    self.update_excel_with_historical_data(all_results)
                    if batch_results:
                        last_asin = list(batch_results.keys())[-1]
                        self.save_checkpoint(last_asin)
        finally:
            # HTTP请求不需要关闭浏览器
            pass

    def save_checkpoint(self, last_processed_asin):
        """保存处理进度检查点"""
        try:
            checkpoint_data = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "marketplace": self.marketplace,
                "last_processed_asin": last_processed_asin
            }
            
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
                
            print(f"已保存进度检查点: {last_processed_asin}")
        except Exception as e:
            print(f"保存检查点时出错: {str(e)}")

    def load_checkpoint(self):
        """加载上次的处理进度"""
        try:
            if not os.path.exists(self.checkpoint_file):
                print("没有找到检查点文件，将从头开始处理")
                return None
                
            with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
                
            print(f"已加载检查点: {checkpoint_data}")
            return checkpoint_data
        except Exception as e:
            print(f"加载检查点时出错: {str(e)}")
            return None

    def get_remaining_asins(self, all_asins):
        """获取尚未处理的ASIN列表"""
        checkpoint = self.load_checkpoint()
        if not checkpoint:
            return all_asins
            
        if checkpoint.get("marketplace") != self.marketplace:
            print(f"检查点市场({checkpoint.get('marketplace')})与当前市场({self.marketplace})不一致，将从头开始处理")
            return all_asins
            
        last_asin = checkpoint.get("last_processed_asin")
        if not last_asin or last_asin not in all_asins:
            return all_asins
            
        last_index = all_asins.index(last_asin)
        remaining = all_asins[last_index + 1:]
        
        print(f"将从检查点继续，跳过 {last_index + 1} 个已处理的ASIN，剩余 {len(remaining)} 个待处理")
        return remaining

    def process_all_asins(self):
        """多线程处理所有ASIN，获取历史价格信息"""
        print(f"开始使用 {self.threads} 个线程处理所有ASIN，获取历史价格信息...")
        print(f"当前使用的市场: {self.marketplace}")
        
        # 设置运行状态
        self.running = True
        self.stop_requested = False
        
        # 从Excel加载ASIN
        all_asins = self.load_asins_from_excel()
        if not all_asins:
            print("没有有效的ASIN可处理")
            self.running = False
            return False
        
        # 获取剩余待处理的ASIN
        asins = self.get_remaining_asins(all_asins)
        if not asins:
            print("所有ASIN已处理完毕")
            self.running = False
            return True
        
        # 共享的结果字典
        all_results = {}
        
        # 将ASIN列表分成多个批次
        batch_size = max(1, len(asins) // self.threads + (1 if len(asins) % self.threads else 0))
        asin_batches = [asins[i:i+batch_size] for i in range(0, len(asins), batch_size)]
        
        if self.verbose:
            print(f"创建 {len(asin_batches)} 个批次，每批次约 {batch_size} 个ASIN")
        else:
            print(f"处理 {len(asins)} 个ASIN，已分为 {len(asin_batches)} 个批次")
        
        # 使用线程池同时处理多个批次
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.threads) as executor:
            # 提交任务
            futures = [executor.submit(self.process_asin_batch, batch, all_results) for batch in asin_batches]
            
            # 等待所有任务完成
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()  # 获取结果，如果有异常会被抛出
                except Exception as e:
                    print(f"处理批次时出错: {str(e)}")
        
        print("所有ASIN处理完成" if not self.stop_requested else "处理被用户中断")

        # 显示请求统计信息
        self.print_request_statistics()

        # 最后一次更新Excel
        self.update_excel_with_historical_data(all_results)

        # 更新运行状态
        self.running = False
        self.stop_requested = False

        return not self.stop_requested

    def request_stop(self):
        """请求停止处理"""
        print("收到停止处理请求，将在完成当前ASIN后停止...")
        self.stop_requested = True

    def update_excel_with_historical_data(self, results):
        """更新Excel文件，添加历史最高价和断货信息"""
        try:
            if self.verbose:
                print(f"更新Excel文件 {self.excel_file} 添加历史价格数据...")
            
            # 检查输入文件类型
            _, file_extension = os.path.splitext(self.excel_file)
            file_extension = file_extension.lower()
            
            # 如果是txt文件，需要创建一个新的DataFrame
            if file_extension == '.txt':
                # 创建一个包含ASIN列的新DataFrame
                df = pd.DataFrame({'ASIN': list(results.keys())})
            else:
                # 读取原始Excel文件
                df = pd.read_excel(self.excel_file)
            
            # 确保有必要的列
            if '历史最高价' not in df.columns:
                df['历史最高价'] = None
            if '历史最低价' not in df.columns:
                df['历史最低价'] = None
            if '断货状态' not in df.columns:
                df['断货状态'] = None
            if '价格符号' not in df.columns:
                df['价格符号'] = None
            
            # 设置价格符号
            price_symbol = self.currency_symbols.get(self.marketplace, "$")
            
            # 查找正确的ASIN列名（可能是任何大小写形式）
            asin_col = None
            for col in df.columns:
                if col.upper() == 'ASIN':
                    asin_col = col
                    break
            
            # 如果没有找到标准ASIN列，尝试其他可能的变体
            if not asin_col:
                possible_variants = ['ASIN', 'asin', 'Asin', 'ASin', 'ProductID', '产品ID']
                for variant in possible_variants:
                    if variant in df.columns:
                        asin_col = variant
                        break
            
            # 如果仍然找不到ASIN列，则报错并返回
            if not asin_col:
                print("错误: 在Excel文件中找不到ASIN列，无法更新数据")
                return False
            
            # 更新数据
            for asin, data in results.items():
                # 确保比较时不区分大小写
                mask = df[asin_col].astype(str).str.upper() == asin.upper()
                if mask.any():
                    df.loc[mask, '历史最高价'] = data.get('historical_highest_price')
                    df.loc[mask, '历史最低价'] = data.get('historical_lowest_price')
                    df.loc[mask, '价格符号'] = price_symbol
                    
                    # 断货状态: None-未知, 1-15天内断货, 2-超过15天断货
                    out_of_stock_status = data.get('out_of_stock_within_month')
                    if out_of_stock_status == 1:
                        df.loc[mask, '断货状态'] = '15天内'
                    elif out_of_stock_status == 2:
                        df.loc[mask, '断货状态'] = '超过15天'
                    else:
                        df.loc[mask, '断货状态'] = '未知'
            
            # 保存到新文件
            df.to_excel(self.output_file, index=False)
            if self.verbose:
                print(f"历史价格数据已保存到 {self.output_file}")
            return True
        except Exception as e:
            print(f"更新Excel文件时出错: {str(e)}")
            return False

class AmazonKeepaPriceApp:
    def __init__(self, root):
        self.root = root
        
        # 使用统一UI主题设置窗口
        AmazonUITheme.setup_window(
            self.root,
            "亚马逊历史价格查询工具", 
            size="950x650", 
            resizable=(True, True)
        )
        
        # 初始化变量
        self.file_path = tk.StringVar()
        self.thread_count = tk.IntVar(value=10)  # 默认为10个线程
        self.checker = None
        self.running = False
        self.marketplace = tk.StringVar(value="US")
        self.verbose = tk.BooleanVar(value=False)  # 默认不显示详细日志
        
        # 创建界面
        self.show_main_interface()
        
        # 重定向stdout到文本控件
        self.redirect_stdout()
        
    def show_main_interface(self):
        # 创建标题栏
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=15, pady=(15, 5))
        
        title_label = ttk.Label(
            title_frame, 
            text="亚马逊历史价格查询工具",
            font=("微软雅黑", 16, "bold"),
            foreground=AmazonUITheme.COLORS["primary"]
        )
        title_label.pack(side=tk.LEFT)
        
        # 添加横向分隔线
        separator = ttk.Separator(self.root, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=15, pady=5)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        
        # 创建左右分栏
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 10), pady=0)
        
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # ===== 左侧配置区域 =====
        
        # 文件选择框架
        file_frame = ttk.LabelFrame(left_frame, text="数据文件", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件路径显示
        file_path_entry = ttk.Entry(file_frame, textvariable=self.file_path, width=30)
        file_path_entry.pack(fill=tk.X, pady=5)
        
        # 浏览按钮
        browse_button = ttk.Button(
            file_frame, 
            text="选择文件(Excel/TXT)", 
            command=self.browse_file,
            width=20
        )
        browse_button.pack(pady=5)
        
        # 市场选择框架
        market_frame = ttk.LabelFrame(left_frame, text="市场设置", padding=10)
        market_frame.pack(fill=tk.X, pady=10)
        
        # 市场选择
        ttk.Label(market_frame, text="选择市场:").pack(anchor=tk.W, pady=(0, 5))
        
        self.market_names = {
            "US": "美国",
            "CA": "加拿大",
            "JP": "日本",
            "UK": "英国",
            "MX": "墨西哥"
        }
        
        # 创建单选按钮组
        for code, name in self.market_names.items():
            ttk.Radiobutton(
                market_frame, 
                text=name, 
                variable=self.marketplace, 
                value=code
            ).pack(anchor=tk.W, pady=2)
        
        # ===== 右侧日志区域 =====
        
        log_frame = ttk.LabelFrame(right_frame, text="运行日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本框与滚动条
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, bg="#fafafa", font=("微软雅黑", 9))
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        # ===== 底部按钮区域 =====
        
        # 先添加横向分隔线
        separator2 = ttk.Separator(self.root, orient=tk.HORIZONTAL)
        separator2.pack(fill=tk.X, padx=15, pady=5)
        
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill=tk.X, padx=15, pady=(5, 15))
        
        # 按钮样式
        style = ttk.Style()
        style.configure(
            "Accent.TButton", 
            background=AmazonUITheme.COLORS["accent"],
            foreground="white"
        )
        
        # 开始按钮
        self.start_button = ttk.Button(
            button_frame, 
            text="开始查询", 
            command=self.start_process,
            style="Accent.TButton",
            width=20
        )
        self.start_button.pack(side=tk.RIGHT, padx=5)
        
        # 继续按钮
        self.continue_button = ttk.Button(
            button_frame, 
            text="继续上次查询", 
            command=self.continue_process,
            width=20
        )
        self.continue_button.pack(side=tk.RIGHT, padx=5)
        
        # 停止按钮
        self.stop_button = ttk.Button(
            button_frame, 
            text="停止查询", 
            command=self.stop_process,
            width=20
        )
        self.stop_button.pack(side=tk.RIGHT, padx=5)
        self.stop_button.config(state=tk.DISABLED)
        
        # 打开结果文件按钮
        def open_output_file():
            if self.checker and os.path.exists(self.checker.output_file):
                if platform.system() == "Windows":
                    os.startfile(self.checker.output_file)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", self.checker.output_file])
                else:  # Linux
                    subprocess.run(["xdg-open", self.checker.output_file])
            else:
                messagebox.showinfo("提示", "结果文件不存在，请先运行查询")
        
        self.open_file_button = ttk.Button(
            button_frame, 
            text="打开结果文件", 
            command=open_output_file,
            width=20
        )
        self.open_file_button.pack(side=tk.LEFT, padx=5)
        
        # 添加状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=15, pady=5)
        
        self.status_label = ttk.Label(
            status_frame, 
            text="准备就绪",
            font=("微软雅黑", 9),
            foreground="#666666"
        )
        self.status_label.pack(side=tk.LEFT)

    def redirect_stdout(self):
        class TextRedirector:
            def __init__(self, text_widget):
                self.text_widget = text_widget
                self.buffer = ""
            
            def write(self, string):
                self.buffer += string
                self.text_widget.insert(tk.END, string)
                self.text_widget.see(tk.END)
            
            def flush(self):
                pass
        
        self.stdout_redirector = TextRedirector(self.log_text)
        sys.stdout = self.stdout_redirector
        
    def browse_file(self):
        file_path = filedialog.askopenfilename(
            title="选择文件",
            filetypes=[
                ("支持的文件", "*.xlsx *.xls *.txt"),
                ("Excel文件", "*.xlsx *.xls"),
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.file_path.set(file_path)
    
    def toggle_buttons(self, running=False):
        """根据运行状态切换按钮状态"""
        if running:
            self.start_button.config(state="disabled")
            self.continue_button.config(state="disabled")
            self.stop_button.config(state="normal")
        else:
            self.start_button.config(state="normal")
            self.continue_button.config(state="normal")
            self.stop_button.config(state="disabled")
            
    def start_process(self):
        # 获取当前设置值
        self.excel_file = self.file_path.get().strip()
        
        if not self.excel_file:
            messagebox.showerror("错误", "请选择Excel文件或TXT文件")
            return
            
        if not os.path.exists(self.excel_file):
            messagebox.showerror("错误", f"文件不存在: {self.excel_file}")
            return
        
        # 检查文件扩展名
        _, file_extension = os.path.splitext(self.excel_file)
        file_extension = file_extension.lower()
        
        if file_extension not in ['.xlsx', '.xls', '.txt']:
            messagebox.showerror("错误", "请选择.xlsx、.xls格式的Excel文件或.txt文本文件")
            return
            
        marketplace = self.marketplace.get()
        verbose = self.verbose.get()
        
        # 显示设置信息
        print(f"开始处理文件: {self.excel_file}")
        print(f"市场: {marketplace} ({self.market_names.get(marketplace, '未知')})")
        
        # 禁用开始按钮，启用停止按钮
        self.toggle_buttons(running=True)
        
        # 创建并启动工作线程
        self.checker = KeepaHistoricalPriceChecker(
            threads=self.thread_count.get(),
            marketplace=marketplace,
            excel_file=self.excel_file,
            verbose=verbose
        )
        
        self.process_thread = threading.Thread(target=self.run_process)
        self.process_thread.daemon = True
        self.process_thread.start()
    
    def continue_process(self):
        # 获取用户设置
        file_path = self.file_path.get()
        market_str = "JP"  # 固定为日本市场
        
        if not file_path:
            self.log_text.insert(tk.END, "请选择包含ASIN的文件\n")
            return
            
        if not os.path.exists(file_path):
            self.log_text.insert(tk.END, f"无法找到文件: {file_path}\n")
            return
            
        # 创建检查器实例以获取正确的检查点文件路径
        self.checker = KeepaHistoricalPriceChecker(
            threads=1,  # 固定线程数
            marketplace=market_str,
            excel_file=file_path,
            verbose=False  # 设置为False简化输出
        )
            
        # 检查检查点文件
        checkpoint_file = self.checker.checkpoint_file
        if not os.path.exists(checkpoint_file):
            self.log_text.insert(tk.END, "没有找到处理检查点，将从头开始处理\n")
        
        # 设置运行状态和按钮
        self.running = True
        self.toggle_buttons(running=True)
        
        # 保留日志区域内容，添加分隔线
        self.log_text.insert(tk.END, "\n" + "-"*80 + "\n继续处理...\n" + "-"*80 + "\n")
        
        # 在新线程中执行处理
        threading.Thread(target=self.run_process, daemon=True).start()
    
    def stop_process(self):
        """请求停止处理"""
        if self.checker and self.running:
            self.checker.request_stop()
            print("已发送停止请求，将在完成当前任务后停止...")
            
            # 禁用停止按钮防止重复点击
            self.stop_button.config(state="disabled")
    
    def run_process(self):
        try:
            print(f"开始处理文件: {self.checker.excel_file}")
            print(f"选择的市场: {self.checker.marketplace}")
            
            success = self.checker.process_all_asins()
            
            # 处理完成后重新启用按钮
            self.running = False
            self.root.after(0, lambda: self.toggle_buttons(running=False))
            
            if success:
                print(f"处理完成! 结果已保存至: {self.checker.output_file}")
            else:
                print("处理过程中发生错误或被用户中断，请检查日志")
                
        except Exception as e:
            print(f"处理过程中发生错误: {str(e)}")
            self.running = False
            self.root.after(0, lambda: self.toggle_buttons(running=False))
            print(f"处理过程中发生错误: {str(e)}")

def main():
    # 禁用原生selenium日志
    logging.getLogger('selenium').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    # 创建并运行GUI
    root = tk.Tk()
    
    # 设置应用程序图标和任务栏图标
    try:
        # 先设置应用程序ID - 这会帮助Windows正确识别应用并更新任务栏图标
        if platform.system() == "Windows":
            try:
                import ctypes
                app_id = "AmazonKeepaPriceApp.App.1.0"  # 唯一应用ID
                ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)
            except Exception:
                pass
        
        # 设置窗口图标
        icon_path = "icon.ico"
        if os.path.exists(icon_path):
            root.iconbitmap(icon_path)
            
            # 在Windows上使用多种方法设置任务栏图标
            if platform.system() == "Windows":
                try:
                    import ctypes
                    # 获取窗口根句柄 - 更可靠的方法
                    hwnd = ctypes.windll.user32.GetForegroundWindow()
                    
                    # 常量定义
                    ICON_SMALL = 0
                    ICON_BIG = 1
                    WM_SETICON = 0x0080
                    LR_LOADFROMFILE = 0x0010
                    IMAGE_ICON = 1
                    
                    # 使用绝对路径
                    abs_icon_path = os.path.abspath(icon_path)
                    
                    # 加载小图标
                    h_icon_small = ctypes.windll.user32.LoadImageW(
                        None, abs_icon_path, IMAGE_ICON, 16, 16, LR_LOADFROMFILE
                    )
                    if h_icon_small:
                        ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_SMALL, h_icon_small)
                    
                    # 加载大图标
                    h_icon_big = ctypes.windll.user32.LoadImageW(
                        None, abs_icon_path, IMAGE_ICON, 32, 32, LR_LOADFROMFILE
                    )
                    if h_icon_big:
                        ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_BIG, h_icon_big)
                    
                    # 刷新窗口以应用更改
                    ctypes.windll.user32.UpdateWindow(hwnd)
                    
                    # 在窗口显示后再次设置图标 - 使用事件回调
                    def set_icon_after_visible():
                        try:
                            hwnd = ctypes.windll.user32.GetForegroundWindow()
                            if h_icon_small:
                                ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_SMALL, h_icon_small)
                            if h_icon_big:
                                ctypes.windll.user32.SendMessageW(hwnd, WM_SETICON, ICON_BIG, h_icon_big)
                        except Exception:
                            pass
                    
                    # 在100ms后执行以确保窗口已完全加载
                    root.after(100, set_icon_after_visible)
                except Exception:
                    pass
    except:
        pass  # 忽略图标设置错误
        
    app = AmazonKeepaPriceApp(root)
    
    # 窗口关闭确认
    def on_closing():
        if app.running:
            if messagebox.askyesno("确认", "处理正在进行中，确定要退出吗？"):
                if app.checker:
                    app.checker.request_stop()
                    # 确保最终清理管理配置文件
                    app.checker.manage_profiles()
                root.destroy()
        else:
            # 即使不在运行中也进行配置文件管理
            if app.checker:
                app.checker.manage_profiles()
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 开始主循环
    root.mainloop()
    
    # 恢复标准输出
    if hasattr(app, 'old_stdout'):
        sys.stdout = app.old_stdout

if __name__ == "__main__":
    main() 