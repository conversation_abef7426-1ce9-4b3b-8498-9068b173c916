#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件中的ASIN是否有效
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def test_file_asins():
    """测试文件中的前几个ASIN"""
    print("🔍 测试文件中的ASIN...")
    
    # 创建分析器实例
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    
    # 从文件中读取前10个ASIN进行测试
    test_asins = [
        "B07QNYTRG2",  # 第1个
        "B07SPTL99F",  # 第2个
        "B07TXJKT46",  # 第3个
        "B006PKE302",  # 第4个
        "B004VMM718",  # 第5个
        "B0BQZ9K2N2",  # 第14个（已知有效）
        "B07RB7XNRP",  # 第18个
        "B08GSQWWRB",  # 第19个
        "B08WWHDSX8",  # 第20个
    ]
    
    success_count = 0
    total_count = len(test_asins)
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n📱 测试ASIN {i}: {asin}")
        print("-" * 40)
        
        try:
            # 直接调用WebSocket方法
            result = analyzer.reverse_engineer_keepa(asin)
            
            if result and analyzer.is_valid_websocket_result(result):
                print(f"✅ 成功获取数据:")
                print(f"   最高价: ${result.get('historical_highest_price', 0):.2f}")
                print(f"   最低价: ${result.get('historical_lowest_price', 0):.2f}")
                if result.get('current_price'):
                    print(f"   当前价格: ${result.get('current_price'):.2f}")
                success_count += 1
            else:
                print(f"❌ 无法获取数据")
                print(f"   可能原因: ASIN无效或Keepa未跟踪此产品")
                
        except Exception as e:
            print(f"❌ 异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📋 测试结果: {success_count}/{total_count} 成功")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == 0:
        print("\n⚠️  所有测试ASIN都失败了")
        print("💡 可能的问题:")
        print("   1. 这些ASIN在Keepa上没有价格跟踪数据")
        print("   2. 网络或代理问题")
        print("   3. ASIN格式不正确或无效")
    elif success_count < total_count * 0.5:
        print(f"\n⚠️  成功率较低 ({success_count/total_count*100:.1f}%)")
        print("💡 建议:")
        print("   1. 检查ASIN的有效性")
        print("   2. 确认这些产品在Amazon美国站有售")
        print("   3. 这些产品可能没有在Keepa上被跟踪")
    else:
        print(f"\n✅ 成功率正常 ({success_count/total_count*100:.1f}%)")
        print("💡 说明:")
        print("   - 部分ASIN有效，部分无效是正常的")
        print("   - 主程序应该能正常处理有效的ASIN")
    
    return success_count > 0

def main():
    """主函数"""
    print("🚀 检查ASIN文件中的产品...")
    print("=" * 60)
    
    success = test_file_asins()
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
