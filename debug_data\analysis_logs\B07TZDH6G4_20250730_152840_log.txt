调试日志 - ASIN: B07TZDH6G4
时间: 20250730_152840
============================================================

[15:28:27] [B07TZDH6G4] 开始WebSocket数据获取
[15:28:29] [B07TZDH6G4] 访问页面: https://keepa.com/#!product/1-B07TZDH6G4
[15:28:31] [B07TZDH6G4] WebSocket连接建立
[15:28:31] [B07TZDH6G4] 页面加载完成，等待WebSocket数据
[15:28:32] [B07TZDH6G4] 收到未压缩WebSocket消息: 81 字符
[15:28:32] [B07TZDH6G4] 收到未压缩WebSocket消息: 365 字符
[15:28:32] [B07TZDH6G4] 收到未压缩WebSocket消息: 119 字符
[15:28:32] [B07TZDH6G4] 收到未压缩WebSocket消息: 8013 字符
[15:28:33] [B07TZDH6G4] 收到未压缩WebSocket消息: 144 字符
[15:28:39] [B07TZDH6G4] 页面内容长度: 205021 字符
[15:28:39] [B07TZDH6G4] 页面内容已保存: debug_data\html_pages\B07TZDH6G4_20250730_152839.html
[15:28:39] [B07TZDH6G4] WebSocket数据已保存: debug_data\websocket_data\B07TZDH6G4_20250730_152839.json
[15:28:39] [B07TZDH6G4] 收到 5 条WebSocket消息
[15:28:39] [B07TZDH6G4] 开始解析 5 条WebSocket消息
[15:28:39] [B07TZDH6G4] 解析消息 1: <class 'dict'>
[15:28:39] [B07TZDH6G4] 消息不包含basicProducts，包含键: ['status', 'actionUrl', 'guest', 'n']
[15:28:39] [B07TZDH6G4] 解析消息 2: <class 'dict'>
[15:28:39] [B07TZDH6G4] 消息不包含basicProducts，包含键: ['exchange', 'id', 'json', 'lastUpdate', 'startTimeStamp', 'status', 'timeStamp', 'version']
[15:28:39] [B07TZDH6G4] 解析消息 3: <class 'dict'>
[15:28:39] [B07TZDH6G4] 消息不包含basicProducts，包含键: ['id', 'lastUpdate', 'startTimeStamp', 'status', 'timeStamp', 'version']
[15:28:39] [B07TZDH6G4] 解析消息 4: <class 'dict'>
[15:28:39] [B07TZDH6G4] 找到basicProducts，包含 1 个产品
[15:28:39] [B07TZDH6G4] 产品 1: ASIN=B07TZDH6G4
[15:28:39] [B07TZDH6G4] 找到匹配的ASIN，开始解析产品数据
[15:28:39] [B07TZDH6G4] 产品标题: Digital Picture Frame 8 Inch Digital Photo Frame H...
[15:28:39] [B07TZDH6G4] CSV数据长度: 34
[15:28:39] [B07TZDH6G4] Amazon价格数据: 0 个价格点
[15:28:39] [B07TZDH6G4] 第三方价格数据: 48 个价格点
[15:28:39] [B07TZDH6G4] 总价格点数: 48
[15:28:39] [B07TZDH6G4] 过滤后价格点数: 47
[15:28:39] [B07TZDH6G4] 价格范围: $48.89 - $128.99
[15:28:39] [B07TZDH6G4] 产品数据解析完成
[15:28:39] [B07TZDH6G4] 产品数据解析成功
