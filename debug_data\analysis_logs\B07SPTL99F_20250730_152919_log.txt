调试日志 - ASIN: B07SPTL99F
时间: 20250730_152919
============================================================

[15:29:06] [B07SPTL99F] 开始WebSocket数据获取
[15:29:08] [B07SPTL99F] 访问页面: https://keepa.com/#!product/1-B07SPTL99F
[15:29:09] [B07SPTL99F] WebSocket连接建立
[15:29:10] [B07SPTL99F] 收到未压缩WebSocket消息: 81 字符
[15:29:10] [B07SPTL99F] 页面加载完成，等待WebSocket数据
[15:29:10] [B07SPTL99F] 收到未压缩WebSocket消息: 365 字符
[15:29:10] [B07SPTL99F] 收到未压缩WebSocket消息: 119 字符
[15:29:11] [B07SPTL99F] 无法解析bytes WebSocket消息: 5470 bytes
[15:29:11] [B07SPTL99F] 收到未压缩WebSocket消息: 144 字符
[15:29:18] [B07SPTL99F] 页面内容长度: 202191 字符
[15:29:18] [B07SPTL99F] 页面内容已保存: debug_data\html_pages\B07SPTL99F_20250730_152918.html
[15:29:18] [B07SPTL99F] WebSocket数据已保存: debug_data\websocket_data\B07SPTL99F_20250730_152918.json
[15:29:18] [B07SPTL99F] 收到 4 条WebSocket消息
[15:29:18] [B07SPTL99F] 开始解析 4 条WebSocket消息
[15:29:18] [B07SPTL99F] 解析消息 1: <class 'dict'>
[15:29:18] [B07SPTL99F] 消息不包含basicProducts，包含键: ['status', 'actionUrl', 'guest', 'n']
[15:29:18] [B07SPTL99F] 解析消息 2: <class 'dict'>
[15:29:18] [B07SPTL99F] 消息不包含basicProducts，包含键: ['exchange', 'id', 'json', 'lastUpdate', 'startTimeStamp', 'status', 'timeStamp', 'version']
[15:29:18] [B07SPTL99F] 解析消息 3: <class 'dict'>
[15:29:18] [B07SPTL99F] 消息不包含basicProducts，包含键: ['id', 'lastUpdate', 'startTimeStamp', 'status', 'timeStamp', 'version']
[15:29:18] [B07SPTL99F] 解析消息 4: <class 'dict'>
[15:29:18] [B07SPTL99F] 消息不包含basicProducts，包含键: ['id', 'json', 'lastUpdate', 'startTimeStamp', 'status', 'timeStamp', 'version']
[15:29:18] [B07SPTL99F] 所有WebSocket消息解析完成，未找到有效数据
