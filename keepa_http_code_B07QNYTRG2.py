import requests
import json

def get_keepa_price_data(asin):
    """获取Keepa价格数据"""
    # 方法 1: https://keepa.com/
    url = 'https://keepa.com/'
    method = 'GET'
    headers = {
        ":authority": "keepa.com",
        ":method": "GET",
        ":path": "/",
        ":scheme": "https",
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9",
        "priority": "u=0, i",
        "sec-ch-ua": "\"Chromium\";v=\"139\", \"Not;A=Brand\";v=\"99\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "document",
        "sec-fetch-mode": "navigate",
        "sec-fetch-site": "none",
        "sec-fetch-user": "?1",
        "upgrade-insecure-requests": "1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json() if 'json' in response.headers.get('content-type', '') else response.text
    return None

    # 方法 2: https://cdn.keepa.com/20250524/libs.js
    url = 'https://cdn.keepa.com/20250524/libs.js'
    method = 'GET'
    headers = {
        ":authority": "cdn.keepa.com",
        ":method": "GET",
        ":path": "/20250524/libs.js",
        ":scheme": "https",
        "accept": "*/*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9",
        "origin": "https://keepa.com",
        "priority": "u=1",
        "referer": "https://keepa.com/",
        "sec-ch-ua": "\"Chromium\";v=\"139\", \"Not;A=Brand\";v=\"99\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "script",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json() if 'json' in response.headers.get('content-type', '') else response.text
    return None

    # 方法 3: https://cdn.keepa.com/velocity.min.1.4.1.js
    url = 'https://cdn.keepa.com/velocity.min.1.4.1.js'
    method = 'GET'
    headers = {
        ":authority": "cdn.keepa.com",
        ":method": "GET",
        ":path": "/velocity.min.1.4.1.js",
        ":scheme": "https",
        "accept": "*/*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9",
        "origin": "https://keepa.com",
        "priority": "u=1",
        "referer": "https://keepa.com/",
        "sec-ch-ua": "\"Chromium\";v=\"139\", \"Not;A=Brand\";v=\"99\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "script",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json() if 'json' in response.headers.get('content-type', '') else response.text
    return None

    # 方法 4: https://cdn.keepa.com/20250729/keepa.js
    url = 'https://cdn.keepa.com/20250729/keepa.js'
    method = 'GET'
    headers = {
        ":authority": "cdn.keepa.com",
        ":method": "GET",
        ":path": "/20250729/keepa.js",
        ":scheme": "https",
        "accept": "*/*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9",
        "origin": "https://keepa.com",
        "priority": "u=1",
        "referer": "https://keepa.com/",
        "sec-ch-ua": "\"Chromium\";v=\"139\", \"Not;A=Brand\";v=\"99\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "script",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json() if 'json' in response.headers.get('content-type', '') else response.text
    return None

    # 方法 5: https://cdn.keepa.com/languages/cn.json?20250729
    url = 'https://cdn.keepa.com/languages/cn.json?20250729'
    method = 'GET'
    headers = {
        ":authority": "cdn.keepa.com",
        ":method": "GET",
        ":path": "/languages/cn.json?20250729",
        ":scheme": "https",
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9",
        "priority": "u=1, i",
        "referer": "https://keepa.com/",
        "sec-ch-ua": "\"Chromium\";v=\"139\", \"Not;A=Brand\";v=\"99\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json() if 'json' in response.headers.get('content-type', '') else response.text
    return None
