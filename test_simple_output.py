#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单输出测试
直接测试主程序的输出格式
"""

import sys
from 历史价格8 import KeepaHistoricalPriceChecker

def test_simple_output():
    """简单输出测试"""
    print("🚀 简单输出格式测试")
    print("🎯 验证主程序输出是否显示黄金购买框价格")
    print("=" * 50)
    
    # 创建价格检查器实例
    checker = KeepaHistoricalPriceChecker(verbose=True)
    
    # 测试单个ASIN
    test_asin = "B07TZDH6G4"
    
    print(f"\n🔍 测试ASIN: {test_asin}")
    print("-" * 30)
    
    try:
        # 直接调用主程序使用的方法
        print("📡 调用 get_keepa_info_with_browser...")
        result = checker.get_keepa_info_with_browser(test_asin, max_retries=1)
        
        if result:
            print(f"\n✅ 方法调用成功")
            print(f"📊 结果类型: {type(result)}")
            print(f"📊 结果内容: {result}")
        else:
            print(f"\n❌ 方法调用失败")
            
    except Exception as e:
        print(f"\n❌ 异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_output()
