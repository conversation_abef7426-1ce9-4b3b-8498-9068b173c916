#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试已知有效的ASIN
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def test_known_good_asin():
    """测试已知有效的ASIN"""
    print("🔍 测试已知有效的ASIN...")
    
    # 创建分析器实例，启用详细输出
    analyzer = KeepaHistoricalPriceChecker(verbose=True)
    
    # 使用已知有效的ASIN
    test_asin = "B08WWHDSX8"  # 这个ASIN之前测试成功过
    
    print(f"📱 测试ASIN: {test_asin}")
    print("=" * 50)
    
    try:
        result = analyzer.reverse_engineer_keepa(test_asin)
        
        if result:
            print("✅ 测试成功:")
            print(f"   ASIN: {result.get('asin')}")
            print(f"   数据源: {result.get('source', 'unknown')}")
            print(f"   最高价: ${result.get('historical_highest_price', 0):.2f}")
            print(f"   最低价: ${result.get('historical_lowest_price', 0):.2f}")
            if result.get('current_price'):
                print(f"   当前价格: ${result.get('current_price'):.2f}")
            return True
        else:
            print("❌ 测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_failed_asin():
    """测试失败的ASIN"""
    print("\n🔍 测试失败的ASIN...")
    
    # 创建分析器实例，启用详细输出
    analyzer = KeepaHistoricalPriceChecker(verbose=True)
    
    # 使用用户输出中失败的ASIN
    test_asin = "B07ZNW4TLG"
    
    print(f"📱 测试ASIN: {test_asin}")
    print("=" * 50)
    
    try:
        result = analyzer.reverse_engineer_keepa(test_asin)
        
        if result:
            print("✅ 测试成功:")
            print(f"   ASIN: {result.get('asin')}")
            print(f"   数据源: {result.get('source', 'unknown')}")
            print(f"   最高价: ${result.get('historical_highest_price', 0):.2f}")
            print(f"   最低价: ${result.get('historical_lowest_price', 0):.2f}")
            if result.get('current_price'):
                print(f"   当前价格: ${result.get('current_price'):.2f}")
            return True
        else:
            print("❌ 测试失败 - 这是预期的结果")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始ASIN对比测试...")
    print("=" * 60)
    
    # 测试已知有效的ASIN
    good_result = test_known_good_asin()
    
    # 测试失败的ASIN
    bad_result = test_failed_asin()
    
    print("\n" + "=" * 60)
    print("📋 对比测试结果:")
    print(f"   有效ASIN (B07QNYTRG2): {'✅ 成功' if good_result else '❌ 失败'}")
    print(f"   无效ASIN (B07ZNW4TLG): {'❌ 失败' if not bad_result else '✅ 意外成功'}")
    
    if good_result and not bad_result:
        print("\n🎉 测试结果符合预期！WebSocket方法正常工作！")
        print("💡 某些ASIN失败是正常的，可能是产品不存在或无数据")
        return True
    else:
        print("\n⚠️  测试结果异常，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
