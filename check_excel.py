import pandas as pd
import sys

try:
    # 读取最新的Excel文件
    import os
    excel_files = [f for f in os.listdir('.') if f.endswith('历史价格信息.xlsx')]
    if excel_files:
        latest_file = max(excel_files, key=os.path.getmtime)
        print(f'读取文件: {latest_file}')
        df = pd.read_excel(latest_file)
    else:
        print('未找到历史价格信息Excel文件')
        sys.exit(1)
    
    print('=== Excel文件内容 ===')
    print(f'总行数: {len(df)}')
    print(f'列名: {list(df.columns)}')
    print()
    
    # 显示前几行数据
    print('=== 前10行数据 ===')
    print(df.head(10).to_string())
    print()
    
    # 检查断货状态列的分布
    if '断货状态' in df.columns:
        print('=== 断货状态分布 ===')
        status_counts = df['断货状态'].value_counts()
        print(status_counts)
        print()
        
        # 显示每个状态的具体ASIN
        for status in status_counts.index:
            asins = df[df['断货状态'] == status]['ASIN'].tolist()
            print(f'{status}: {asins}')
    
    # 检查历史最高价列
    if '历史最高价' in df.columns:
        print()
        print('=== 历史最高价信息 ===')
        price_col = '历史最高价'
        print(f'有价格的产品数量: {df[price_col].notna().sum()}')
        print(f'无价格的产品数量: {df[price_col].isna().sum()}')
        
except Exception as e:
    print(f'读取Excel文件时出错: {e}')
    sys.exit(1)
