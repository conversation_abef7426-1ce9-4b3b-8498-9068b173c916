#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keepa WebSocket客户端
基于逆向工程的真实API调用实现
"""

import asyncio
import json
import gzip
import time
import random
from datetime import datetime, timedelta
from playwright.async_api import async_playwright

class KeepaWebSocketClient:
    def __init__(self):
        self.keepa_epoch = datetime(2011, 1, 1)
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
    async def get_keepa_price_data(self, asin, proxy=None):
        """获取Keepa价格数据"""
        try:
            print(f"🔍 获取ASIN {asin} 的价格数据...")
            
            # 使用playwright模拟真实浏览器行为
            async with async_playwright() as p:
                # 配置浏览器启动参数
                launch_args = [
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--no-first-run',
                    '--disable-default-apps'
                ]
                
                # 如果有代理，添加代理配置
                if proxy:
                    proxy_parts = proxy.split(':')
                    if len(proxy_parts) >= 4:
                        proxy_config = {
                            'server': f"socks5://{proxy_parts[1]}:{proxy_parts[2]}",
                            'username': proxy_parts[3] if len(proxy_parts) > 3 else None,
                            'password': proxy_parts[4] if len(proxy_parts) > 4 else None
                        }
                    else:
                        proxy_config = None
                else:
                    proxy_config = None
                
                browser = await p.chromium.launch(
                    headless=True,  # 无头模式，提高效率
                    args=launch_args,
                    proxy=proxy_config
                )
                
                context = await browser.new_context(
                    user_agent=random.choice(self.user_agents),
                    viewport={'width': 1920, 'height': 1080},
                    locale='en-US'
                )
                
                page = await context.new_page()
                
                # 存储WebSocket消息
                websocket_messages = []
                
                # 监听WebSocket连接
                def on_websocket(websocket):
                    websocket.on('framereceived', lambda payload: self.on_websocket_message(payload, websocket_messages))
                
                page.on('websocket', on_websocket)
                
                try:
                    # 访问Keepa产品页面
                    keepa_url = f"https://keepa.com/#!product/1-{asin}"
                    await page.goto(keepa_url, wait_until='networkidle', timeout=30000)
                    
                    # 等待WebSocket连接和数据加载
                    await asyncio.sleep(8)
                    
                    # 分析收集的WebSocket消息
                    price_data = self.parse_websocket_messages(websocket_messages, asin)
                    
                    if price_data:
                        print(f"✅ 成功获取 {asin} 的价格数据")
                        return price_data
                    else:
                        print(f"⚠️  未找到 {asin} 的价格数据")
                        return None
                        
                except Exception as e:
                    print(f"❌ 页面加载失败: {str(e)}")
                    return None
                
                finally:
                    await browser.close()
                    
        except Exception as e:
            print(f"❌ 获取价格数据失败: {str(e)}")
            return None
    
    def on_websocket_message(self, payload, message_list):
        """WebSocket消息处理"""
        try:
            if isinstance(payload, bytes):
                # 尝试解析二进制数据
                try:
                    # 可能是压缩的JSON数据
                    decompressed = gzip.decompress(payload)
                    json_data = json.loads(decompressed.decode('utf-8'))
                    message_list.append(json_data)
                except:
                    try:
                        # 尝试直接解析为JSON
                        json_data = json.loads(payload.decode('utf-8'))
                        message_list.append(json_data)
                    except:
                        pass  # 忽略无法解析的消息
            elif isinstance(payload, str):
                try:
                    json_data = json.loads(payload)
                    message_list.append(json_data)
                except:
                    pass  # 忽略无法解析的消息
                    
        except Exception as e:
            pass  # 静默处理错误
    
    def parse_websocket_messages(self, messages, asin):
        """解析WebSocket消息中的价格数据"""
        for message in messages:
            if isinstance(message, dict) and 'basicProducts' in message:
                products = message['basicProducts']
                
                for product in products:
                    if product.get('asin') == asin:
                        return self.parse_product_data(product)
        
        return None
    
    def parse_product_data(self, product):
        """解析产品数据"""
        try:
            asin = product.get('asin')
            title = product.get('title', 'Unknown')
            csv_data = product.get('csv', [])
            
            if not csv_data:
                return None
            
            # 解析价格数据
            result = {
                'asin': asin,
                'title': title,
                'historical_highest_price': None,
                'historical_lowest_price': None,
                'current_price': None,
                'amazon_prices': [],
                'third_party_prices': []
            }
            
            all_prices = []
            
            # 解析Amazon价格历史 (csv[0])
            if len(csv_data) > 0 and csv_data[0]:
                amazon_prices = self.parse_price_array(csv_data[0])
                result['amazon_prices'] = amazon_prices
                all_prices.extend([p['price_usd'] for p in amazon_prices if p['price_usd'] > 0])
            
            # 解析第三方新品价格 (csv[1])
            if len(csv_data) > 1 and csv_data[1]:
                third_party_prices = self.parse_price_array(csv_data[1])
                result['third_party_prices'] = third_party_prices
                all_prices.extend([p['price_usd'] for p in third_party_prices if p['price_usd'] > 0])
            
            # 计算最高最低价格
            if all_prices:
                result['historical_highest_price'] = max(all_prices)
                result['historical_lowest_price'] = min(all_prices)
                
                # 获取当前价格（最新的有效价格）
                current_amazon = self.get_latest_price(result['amazon_prices'])
                current_third_party = self.get_latest_price(result['third_party_prices'])
                
                if current_amazon:
                    result['current_price'] = current_amazon
                elif current_third_party:
                    result['current_price'] = current_third_party
            
            return result
            
        except Exception as e:
            print(f"❌ 解析产品数据失败: {str(e)}")
            return None
    
    def parse_price_array(self, price_data):
        """解析价格数组"""
        if not price_data or len(price_data) < 2:
            return []
        
        prices = []
        for i in range(0, len(price_data), 2):
            if i + 1 < len(price_data):
                timestamp = price_data[i]
                price_cents = price_data[i + 1]
                
                if price_cents != -1 and price_cents > 0:  # -1表示无价格数据
                    prices.append({
                        'timestamp': timestamp,
                        'price_cents': price_cents,
                        'price_usd': price_cents / 100.0
                    })
        
        return prices
    
    def get_latest_price(self, price_list):
        """获取最新的有效价格"""
        if not price_list:
            return None
        
        # 按时间戳排序，获取最新的
        sorted_prices = sorted(price_list, key=lambda x: x['timestamp'], reverse=True)
        for price_entry in sorted_prices:
            if price_entry['price_usd'] > 0:
                return price_entry['price_usd']
        
        return None

# 测试函数
async def test_keepa_client():
    """测试Keepa客户端"""
    client = KeepaWebSocketClient()
    
    # 测试ASIN
    test_asin = "B07QNYTRG2"
    
    result = await client.get_keepa_price_data(test_asin)
    
    if result:
        print(f"\n📊 价格数据结果:")
        print(f"   ASIN: {result['asin']}")
        print(f"   标题: {result['title'][:50]}...")
        print(f"   历史最高价: ${result['historical_highest_price']:.2f}")
        print(f"   历史最低价: ${result['historical_lowest_price']:.2f}")
        if result['current_price']:
            print(f"   当前价格: ${result['current_price']:.2f}")
        print(f"   Amazon价格记录: {len(result['amazon_prices'])} 条")
        print(f"   第三方价格记录: {len(result['third_party_prices'])} 条")
    else:
        print("❌ 获取价格数据失败")

if __name__ == "__main__":
    asyncio.run(test_keepa_client())
