#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试WebSocket方法是否还能正常工作
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def test_websocket_method():
    """测试WebSocket方法是否还能正常工作"""
    print("🔍 测试WebSocket方法状态...")
    
    # 创建分析器实例，启用详细输出以便调试
    analyzer = KeepaHistoricalPriceChecker(verbose=True)
    
    # 使用之前成功的ASIN
    test_asin = "B0BQZ9K2N2"
    
    print(f"📱 测试ASIN: {test_asin}")
    print("=" * 50)
    
    try:
        # 直接调用WebSocket方法
        result = analyzer.reverse_engineer_keepa(test_asin)
        
        if result:
            print("✅ WebSocket方法仍然有效:")
            print(f"   ASIN: {result.get('asin')}")
            print(f"   数据源: {result.get('source', 'unknown')}")
            print(f"   最高价: ${result.get('historical_highest_price', 0):.2f}")
            print(f"   最低价: ${result.get('historical_lowest_price', 0):.2f}")
            if result.get('current_price'):
                print(f"   当前价格: ${result.get('current_price'):.2f}")
            return True
        else:
            print("❌ WebSocket方法失效了")
            print("   可能的原因:")
            print("   1. Keepa网站更新了反爬虫机制")
            print("   2. WebSocket连接被阻止")
            print("   3. 网络或代理问题")
            return False
            
    except Exception as e:
        print(f"❌ WebSocket方法异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_failed_asins():
    """测试一些失败的ASIN"""
    print("\n🔍 测试失败的ASIN...")
    
    # 创建分析器实例
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    
    # 从用户输出中选择一些失败的ASIN
    failed_asins = ["B0CKQBCFDX", "B07D553F2M", "B002EJULB8"]
    
    for asin in failed_asins:
        print(f"\n📱 测试ASIN: {asin}")
        print("-" * 30)
        
        try:
            # 直接调用WebSocket方法
            result = analyzer.reverse_engineer_keepa(asin)
            
            if result:
                print(f"✅ 成功获取数据:")
                print(f"   最高价: ${result.get('historical_highest_price', 0):.2f}")
                print(f"   最低价: ${result.get('historical_lowest_price', 0):.2f}")
            else:
                print(f"❌ 无法获取数据")
                
        except Exception as e:
            print(f"❌ 异常: {str(e)}")

def main():
    """主函数"""
    print("🚀 检查WebSocket方法状态...")
    print("=" * 60)
    
    # 测试WebSocket方法
    websocket_works = test_websocket_method()
    
    # 测试失败的ASIN
    test_failed_asins()
    
    print("\n" + "=" * 60)
    print("📋 诊断结果:")
    
    if websocket_works:
        print("✅ WebSocket方法正常工作")
        print("💡 问题可能是:")
        print("   - 这些ASIN在Keepa上确实没有价格数据")
        print("   - 需要检查ASIN的有效性")
    else:
        print("❌ WebSocket方法已失效")
        print("💡 建议:")
        print("   - 检查网络连接和代理设置")
        print("   - Keepa可能更新了反爬虫机制")
        print("   - 可能需要使用其他方法获取价格数据")
    
    return websocket_works

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
