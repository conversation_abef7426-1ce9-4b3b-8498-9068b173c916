#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试"未知"结果的详细分析工具
保存失败的页面和数据进行分析
"""

import sys
import os
import json
import datetime
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

class DebugKeepaChecker(KeepaHistoricalPriceChecker):
    """增强的调试版本"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 创建调试目录
        self.debug_dir = Path("debug_data")
        self.debug_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        (self.debug_dir / "html_pages").mkdir(exist_ok=True)
        (self.debug_dir / "websocket_data").mkdir(exist_ok=True)
        (self.debug_dir / "analysis_logs").mkdir(exist_ok=True)
        
        self.debug_log = []
    
    def log_debug(self, message, asin=None):
        """记录调试信息"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        if asin:
            log_entry = f"[{timestamp}] [{asin}] {message}"
        
        self.debug_log.append(log_entry)
        print(log_entry)
    
    async def get_keepa_websocket_data_debug(self, asin):
        """增强的WebSocket数据获取，包含详细调试"""
        try:
            from playwright.async_api import async_playwright
            import gzip
            import json

            self.log_debug("开始WebSocket数据获取", asin)
            
            # 存储WebSocket消息和页面内容
            websocket_messages = []
            page_content = None
            page_url = None

            async with async_playwright() as p:
                # 配置浏览器
                proxy_config = None
                if hasattr(self, 'current_proxy') and self.current_proxy:
                    proxy_parts = self.current_proxy.split(':')
                    if len(proxy_parts) >= 4:
                        proxy_config = {
                            'server': f"socks5://{proxy_parts[1]}:{proxy_parts[2]}",
                            'username': proxy_parts[3] if len(proxy_parts) > 3 else None,
                            'password': proxy_parts[4] if len(proxy_parts) > 4 else None
                        }

                browser = await p.chromium.launch(
                    headless=True,
                    args=['--disable-blink-features=AutomationControlled']
                )

                # 定义User-Agent列表
                user_agents = [
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                ]

                import random
                context = await browser.new_context(
                    user_agent=random.choice(user_agents),
                    proxy=proxy_config
                )

                page = await context.new_page()

                # 监听WebSocket连接
                def on_websocket(websocket):
                    self.log_debug("WebSocket连接建立", asin)
                    websocket.on('framereceived', lambda payload: self.handle_websocket_message_debug(payload, websocket_messages, asin))

                page.on('websocket', on_websocket)

                try:
                    # 访问Keepa产品页面
                    keepa_url = f"https://keepa.com/#!product/1-{asin}"
                    page_url = keepa_url
                    self.log_debug(f"访问页面: {keepa_url}", asin)
                    
                    await page.goto(keepa_url, wait_until='networkidle', timeout=30000)
                    
                    self.log_debug("页面加载完成，等待WebSocket数据", asin)

                    # 等待WebSocket数据
                    import asyncio
                    await asyncio.sleep(8)
                    
                    # 获取页面内容
                    page_content = await page.content()
                    self.log_debug(f"页面内容长度: {len(page_content)} 字符", asin)
                    
                    # 保存页面内容
                    self.save_page_content(asin, page_content, page_url)
                    
                    # 保存WebSocket数据
                    self.save_websocket_data(asin, websocket_messages)
                    
                    self.log_debug(f"收到 {len(websocket_messages)} 条WebSocket消息", asin)

                    # 解析WebSocket消息
                    result = self.parse_websocket_messages_debug(websocket_messages, asin)
                    
                    return result

                finally:
                    await browser.close()

        except Exception as e:
            self.log_debug(f"WebSocket数据获取异常: {str(e)}", asin)
            return None
    
    def handle_websocket_message_debug(self, payload, message_list, asin):
        """处理WebSocket消息（调试版本）"""
        try:
            if isinstance(payload, bytes):
                try:
                    # 尝试解压缩
                    import gzip
                    decompressed = gzip.decompress(payload)
                    json_data = json.loads(decompressed.decode('utf-8'))
                    message_list.append(json_data)
                    self.log_debug(f"收到压缩WebSocket消息: {len(str(json_data))} 字符", asin)
                except:
                    try:
                        # 直接解析
                        json_data = json.loads(payload.decode('utf-8'))
                        message_list.append(json_data)
                        self.log_debug(f"收到未压缩WebSocket消息: {len(str(json_data))} 字符", asin)
                    except:
                        self.log_debug(f"无法解析bytes WebSocket消息: {len(payload)} bytes", asin)
            elif isinstance(payload, str):
                try:
                    json_data = json.loads(payload)
                    message_list.append(json_data)
                    self.log_debug(f"收到字符串WebSocket消息: {len(payload)} 字符", asin)
                except:
                    self.log_debug(f"无法解析字符串WebSocket消息: {len(payload)} 字符", asin)
        except Exception as e:
            self.log_debug(f"处理WebSocket消息异常: {str(e)}", asin)
    
    def parse_websocket_messages_debug(self, messages, asin):
        """解析WebSocket消息中的价格数据（调试版本）"""
        self.log_debug(f"开始解析 {len(messages)} 条WebSocket消息", asin)
        
        for i, message in enumerate(messages):
            self.log_debug(f"解析消息 {i+1}: {type(message)}", asin)
            
            if isinstance(message, dict):
                if 'basicProducts' in message:
                    self.log_debug(f"找到basicProducts，包含 {len(message['basicProducts'])} 个产品", asin)
                    products = message['basicProducts']

                    for j, product in enumerate(products):
                        product_asin = product.get('asin')
                        self.log_debug(f"产品 {j+1}: ASIN={product_asin}", asin)
                        
                        if product_asin == asin:
                            self.log_debug("找到匹配的ASIN，开始解析产品数据", asin)
                            result = self.parse_keepa_product_data_debug(product, asin)
                            if result:
                                self.log_debug("产品数据解析成功", asin)
                                return result
                            else:
                                self.log_debug("产品数据解析失败", asin)
                else:
                    self.log_debug(f"消息不包含basicProducts，包含键: {list(message.keys())}", asin)
            else:
                self.log_debug(f"消息不是字典类型: {type(message)}", asin)

        self.log_debug("所有WebSocket消息解析完成，未找到有效数据", asin)
        return None
    
    def parse_keepa_product_data_debug(self, product, asin):
        """解析Keepa产品数据（调试版本）"""
        try:
            product_asin = product.get('asin')
            title = product.get('title', 'Unknown')
            csv_data = product.get('csv', [])
            
            self.log_debug(f"产品标题: {title[:50]}...", asin)
            self.log_debug(f"CSV数据长度: {len(csv_data)}", asin)

            if not csv_data:
                self.log_debug("CSV数据为空", asin)
                return None

            all_prices = []

            # 解析Amazon价格历史 (csv[0])
            amazon_prices = []
            if len(csv_data) > 0 and csv_data[0]:
                amazon_prices = self.parse_price_array(csv_data[0])
                self.log_debug(f"Amazon价格数据: {len(amazon_prices)} 个价格点", asin)
                all_prices.extend([p for p in amazon_prices if p > 0])

            # 解析第三方新品价格 (csv[1])
            third_party_prices = []
            if len(csv_data) > 1 and csv_data[1]:
                third_party_prices = self.parse_price_array(csv_data[1])
                self.log_debug(f"第三方价格数据: {len(third_party_prices)} 个价格点", asin)
                all_prices.extend([p for p in third_party_prices if p > 0])

            self.log_debug(f"总价格点数: {len(all_prices)}", asin)

            if not all_prices:
                self.log_debug("没有有效的价格数据", asin)
                return None

            # 价格过滤：移除异常价格
            filtered_prices = []
            for price in all_prices:
                price_dollars = price / 100.0
                # 过滤异常价格：保留 $0.01 到 $2000 之间的价格
                if 0.01 <= price_dollars <= 2000:
                    filtered_prices.append(price)
            
            self.log_debug(f"过滤后价格点数: {len(filtered_prices)}", asin)
            
            if not filtered_prices:
                self.log_debug("过滤后没有有效价格", asin)
                return None

            # 计算价格统计
            highest_price = max(filtered_prices) / 100.0  # 转换为美元
            lowest_price = min(filtered_prices) / 100.0   # 转换为美元
            
            self.log_debug(f"价格范围: ${lowest_price:.2f} - ${highest_price:.2f}", asin)

            # 获取当前价格（也需要过滤）
            current_price = None
            if amazon_prices:
                last_amazon_price = amazon_prices[-1] / 100.0
                if 0.01 <= last_amazon_price <= 2000:
                    current_price = last_amazon_price
                    self.log_debug(f"当前Amazon价格: ${current_price:.2f}", asin)
            elif third_party_prices:
                last_third_party_price = third_party_prices[-1] / 100.0
                if 0.01 <= last_third_party_price <= 2000:
                    current_price = last_third_party_price
                    self.log_debug(f"当前第三方价格: ${current_price:.2f}", asin)

            result = {
                'asin': product_asin,
                'title': title,
                'historical_highest_price': highest_price,
                'historical_lowest_price': lowest_price,
                'current_price': current_price
            }
            
            self.log_debug("产品数据解析完成", asin)
            return result

        except Exception as e:
            self.log_debug(f"解析产品数据异常: {str(e)}", asin)
            return None
    
    def save_page_content(self, asin, content, url):
        """保存页面内容"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{asin}_{timestamp}.html"
            filepath = self.debug_dir / "html_pages" / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"<!-- URL: {url} -->\n")
                f.write(f"<!-- ASIN: {asin} -->\n")
                f.write(f"<!-- Timestamp: {timestamp} -->\n")
                f.write(content)
            
            self.log_debug(f"页面内容已保存: {filepath}", asin)
        except Exception as e:
            self.log_debug(f"保存页面内容失败: {str(e)}", asin)
    
    def save_websocket_data(self, asin, messages):
        """保存WebSocket数据"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{asin}_{timestamp}.json"
            filepath = self.debug_dir / "websocket_data" / filename
            
            data = {
                'asin': asin,
                'timestamp': timestamp,
                'message_count': len(messages),
                'messages': messages
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.log_debug(f"WebSocket数据已保存: {filepath}", asin)
        except Exception as e:
            self.log_debug(f"保存WebSocket数据失败: {str(e)}", asin)
    
    def save_debug_log(self, asin):
        """保存调试日志"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{asin}_{timestamp}_log.txt"
            filepath = self.debug_dir / "analysis_logs" / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"调试日志 - ASIN: {asin}\n")
                f.write(f"时间: {timestamp}\n")
                f.write("=" * 60 + "\n\n")
                
                for log_entry in self.debug_log:
                    f.write(log_entry + "\n")
            
            self.log_debug(f"调试日志已保存: {filepath}", asin)
        except Exception as e:
            self.log_debug(f"保存调试日志失败: {str(e)}", asin)

def test_unknown_asins():
    """测试返回"未知"的ASIN"""
    print("🔍 调试未知结果分析...")
    print("=" * 60)
    
    # 创建调试分析器
    analyzer = DebugKeepaChecker(verbose=True)
    
    # 测试一些可能返回"未知"的ASIN
    test_asins = [
        "B07TZDH6G4",  # 用户提供的ASIN
        "B004VMM718",  # 文件中的ASIN
        "B01LYAKZ52",  # 检查点中的ASIN
        "B07SPTL99F",  # 文件中的ASIN
        "B07TXJKT46"   # 文件中的ASIN
    ]
    
    results = []
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n🔍 调试ASIN {i}/{len(test_asins)}: {asin}")
        print("-" * 40)
        
        # 清空调试日志
        analyzer.debug_log = []
        
        try:
            # 使用调试版本的方法
            import asyncio
            import random
            
            def run_debug_in_thread():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        return loop.run_until_complete(analyzer.get_keepa_websocket_data_debug(asin))
                    finally:
                        loop.close()
                except Exception as e:
                    analyzer.log_debug(f"调试线程执行失败: {str(e)}", asin)
                    return None
            
            result = run_debug_in_thread()
            
            # 保存调试日志
            analyzer.save_debug_log(asin)
            
            if result and analyzer.is_valid_websocket_result(result):
                print(f"✅ 成功获取数据")
                results.append({'asin': asin, 'success': True, 'result': result})
            else:
                print(f"❌ 返回未知")
                results.append({'asin': asin, 'success': False})
                
        except Exception as e:
            print(f"❌ 调试异常: {str(e)}")
            results.append({'asin': asin, 'success': False, 'error': str(e)})
    
    # 分析结果
    print("\n" + "=" * 60)
    print("📊 调试结果分析:")
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"   ✅ 成功: {len(successful)}/{len(results)}")
    print(f"   ❌ 失败: {len(failed)}/{len(results)}")
    
    print(f"\n📁 调试数据保存位置:")
    print(f"   HTML页面: debug_data/html_pages/")
    print(f"   WebSocket数据: debug_data/websocket_data/")
    print(f"   分析日志: debug_data/analysis_logs/")
    
    if failed:
        print(f"\n🔍 失败原因分析:")
        print(f"   请查看保存的HTML页面和WebSocket数据")
        print(f"   常见原因可能包括:")
        print(f"   1. 页面加载不完整")
        print(f"   2. WebSocket连接失败")
        print(f"   3. 产品数据格式变化")
        print(f"   4. 网络或代理问题")
        print(f"   5. Keepa网站反爬虫机制")

def main():
    """主函数"""
    print("🚀 开始调试未知结果...")
    test_unknown_asins()
    print("\n🎯 调试完成！请查看debug_data目录中的详细数据。")

if __name__ == "__main__":
    main()
