#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API响应内容
"""

import requests
import re
from 历史价格8 import KeepaHistoricalPrice<PERSON>he<PERSON>

def debug_api_response():
    """调试API响应内容"""
    print("🔍 调试API响应内容")
    print("=" * 50)
    
    # 创建检查器
    checker = KeepaHistoricalPriceChecker(marketplace="US", verbose=False)
    checker.load_proxies()
    
    # 测试端点
    endpoint = "https://discuss.keepa.com/t/data-features-access-free-vs-monthly-subscription/5845"
    test_asin = "B07QNYTRG2"
    
    print(f"📋 测试配置:")
    print(f"   端点: {endpoint}")
    print(f"   ASIN: {test_asin}")
    
    try:
        # 获取代理和请求头
        proxy = checker.get_available_proxy()
        headers = checker.get_headers()
        headers.update({
            'Accept': 'application/json, text/plain, */*',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': f'https://keepa.com/#!product/1-{test_asin}'
        })
        
        print(f"\n🌐 发送请求...")
        print(f"   代理: {proxy}")
        print(f"   User-Agent: {headers.get('User-Agent', 'N/A')[:50]}...")
        
        # 发送请求
        response = requests.get(
            endpoint,
            headers=headers,
            proxies=proxy,
            timeout=30,
            verify=False
        )
        
        print(f"\n📊 响应信息:")
        print(f"   状态码: {response.status_code}")
        print(f"   内容类型: {response.headers.get('content-type', 'N/A')}")
        print(f"   内容长度: {len(response.text)} 字符")
        
        # 保存完整响应到文件
        with open(f"debug_api_response_{test_asin}.html", 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"   完整响应已保存到: debug_api_response_{test_asin}.html")
        
        # 分析内容
        print(f"\n🔍 内容分析:")
        content = response.text
        content_lower = content.lower()
        
        # 检查关键词
        keywords = ['price', 'highest', 'lowest', 'keepa', 'amazon', 'product', 'asin']
        for keyword in keywords:
            count = content_lower.count(keyword)
            print(f"   '{keyword}': {count} 次")
        
        # 查找价格模式
        print(f"\n💰 价格模式搜索:")
        price_patterns = [
            (r'highest["\']?\s*:\s*[\'"]*(\d+\.?\d*)', "highest"),
            (r'lowest["\']?\s*:\s*[\'"]*(\d+\.?\d*)', "lowest"),
            (r'\$(\d+\.?\d*)', "dollar"),
            (r'price["\']?\s*:\s*[\'"]*(\d+\.?\d*)', "price"),
            (r'(\d+\.\d{2})', "decimal")
        ]
        
        for pattern, name in price_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                print(f"   {name}: {matches[:5]}...")  # 只显示前5个匹配
            else:
                print(f"   {name}: 无匹配")
        
        # 检查是否是HTML页面
        if '<html' in content_lower:
            print(f"\n📄 HTML页面分析:")
            title_match = re.search(r'<title>(.*?)</title>', content, re.IGNORECASE | re.DOTALL)
            if title_match:
                print(f"   标题: {title_match.group(1).strip()[:100]}...")
            
            # 查找表格或数据结构
            if '<table' in content_lower:
                print(f"   包含表格: 是")
            if 'json' in content_lower:
                print(f"   包含JSON: 是")
            if 'keepa' in content_lower:
                print(f"   包含Keepa: 是")
        
        # 尝试使用检查器的解析方法
        print(f"\n🧪 使用检查器解析:")
        result = checker.parse_api_text_response(content, test_asin)
        if result:
            print(f"   解析成功:")
            print(f"     最高价: ${result.get('historical_highest_price', 'N/A')}")
            print(f"     最低价: ${result.get('historical_lowest_price', 'N/A')}")
        else:
            print(f"   解析失败: 无法提取价格数据")
        
        # 显示内容片段
        print(f"\n📝 内容片段 (前500字符):")
        print(f"   {content[:500]}...")
        
        if len(content) > 500:
            print(f"\n📝 内容片段 (后500字符):")
            print(f"   ...{content[-500:]}")
        
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

if __name__ == "__main__":
    debug_api_response()
