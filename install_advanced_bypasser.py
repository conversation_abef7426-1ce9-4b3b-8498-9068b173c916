#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级Keepa绕过器安装脚本
自动安装所有必要的依赖
"""

import subprocess
import sys
import os

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_package(package_name):
    """安装Python包"""
    print(f"📦 安装 {package_name}...")
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install {package_name}")
    
    if success:
        print(f"✅ {package_name} 安装成功")
        return True
    else:
        print(f"❌ {package_name} 安装失败: {stderr}")
        return False

def check_chrome_installation():
    """检查Chrome浏览器是否已安装"""
    print("🔍 检查Chrome浏览器...")
    
    # Windows路径
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
    ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✅ 找到Chrome: {path}")
            return True
    
    # 尝试命令行检查
    success, _, _ = run_command("chrome --version")
    if success:
        print("✅ Chrome已安装（通过命令行检测）")
        return True
    
    success, _, _ = run_command("google-chrome --version")
    if success:
        print("✅ Chrome已安装（通过命令行检测）")
        return True
    
    print("⚠️ 未检测到Chrome浏览器")
    print("📥 请从以下地址下载安装Chrome:")
    print("   https://www.google.com/chrome/")
    return False

def main():
    """主安装流程"""
    print("🚀 高级Keepa绕过器 - 依赖安装器")
    print("=" * 50)
    
    # 升级pip
    print("📈 升级pip...")
    run_command(f"{sys.executable} -m pip install --upgrade pip")
    
    # 必要的Python包
    required_packages = [
        "undetected-chromedriver>=3.5.0",
        "selenium>=4.15.0",
        "requests>=2.31.0",
        "websocket-client>=1.6.0",
    ]
    
    print("\n📦 安装Python依赖包...")
    failed_packages = []
    
    for package in required_packages:
        if not install_package(package):
            failed_packages.append(package)
    
    # 检查Chrome
    print("\n🌐 检查浏览器...")
    chrome_ok = check_chrome_installation()
    
    # 测试导入
    print("\n🧪 测试依赖导入...")
    test_imports = [
        ("undetected_chromedriver", "undetected-chromedriver"),
        ("selenium", "selenium"),
        ("requests", "requests"),
        ("websocket", "websocket-client"),
    ]
    
    import_failures = []
    for module_name, package_name in test_imports:
        try:
            __import__(module_name)
            print(f"✅ {package_name} 导入成功")
        except ImportError as e:
            print(f"❌ {package_name} 导入失败: {e}")
            import_failures.append(package_name)
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 安装总结")
    print("=" * 50)
    
    if not failed_packages and not import_failures and chrome_ok:
        print("🎉 所有依赖安装成功!")
        print("✅ 可以运行高级绕过器了")
        print("\n🚀 运行命令:")
        print("   python advanced_keepa_bypasser.py")
    else:
        print("⚠️ 安装过程中遇到问题:")
        
        if failed_packages:
            print(f"❌ 包安装失败: {', '.join(failed_packages)}")
        
        if import_failures:
            print(f"❌ 导入失败: {', '.join(import_failures)}")
        
        if not chrome_ok:
            print("❌ Chrome浏览器未安装")
        
        print("\n🔧 解决建议:")
        print("1. 确保网络连接正常")
        print("2. 尝试使用管理员权限运行")
        print("3. 手动安装Chrome浏览器")
        print("4. 检查Python版本（建议3.8+）")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程异常: {e}")
        import traceback
        traceback.print_exc()
