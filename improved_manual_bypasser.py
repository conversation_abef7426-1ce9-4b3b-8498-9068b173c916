#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的手动Keepa绕过器
修复JavaScript和WebSocket问题
"""

import time
import random
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def create_stealth_chrome_driver():
    """创建隐蔽的Chrome驱动器"""
    print("🔧 创建隐蔽Chrome驱动器...")
    
    options = Options()
    
    # 基础反检测配置
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_argument('--disable-extensions')
    # 注意：不禁用JavaScript，因为我们需要它来运行WebSocket
    
    # 窗口和显示设置
    options.add_argument('--window-size=1366,768')
    
    # 用户代理
    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    options.add_argument(f'--user-agent={user_agent}')
    
    # 禁用自动化标识
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    # 设置首选项（只禁用图片以加速）
    prefs = {
        "profile.default_content_setting_values": {
            "images": 2,  # 禁用图片
            "notifications": 2,  # 禁用通知
        }
    }
    options.add_experimental_option("prefs", prefs)
    
    try:
        driver = webdriver.Chrome(options=options)
        
        # 设置超时
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(10)
        
        # 注入反检测脚本
        inject_stealth_scripts(driver)
        
        print("✅ Chrome驱动器创建成功")
        return driver
        
    except Exception as e:
        print(f"❌ 创建驱动器失败: {str(e)}")
        return None

def inject_stealth_scripts(driver):
    """注入反检测脚本"""
    print("🛡️ 注入反检测脚本...")
    
    stealth_scripts = [
        # 移除webdriver属性
        "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})",
        
        # 修改plugins
        """
        Object.defineProperty(navigator, 'plugins', {
            get: () => [
                {name: 'Chrome PDF Plugin', description: 'Portable Document Format'},
                {name: 'Chrome PDF Viewer', description: 'PDF Viewer'},
                {name: 'Native Client', description: 'Native Client'}
            ]
        });
        """,
        
        # 修改语言
        "Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})",
        
        # 修改平台
        "Object.defineProperty(navigator, 'platform', {get: () => 'Win32'})",
    ]
    
    for script in stealth_scripts:
        try:
            driver.execute_script(script)
        except Exception as e:
            print(f"⚠️ 脚本注入警告: {str(e)}")

def setup_websocket_listener(driver):
    """设置WebSocket监听器"""
    print("📡 设置WebSocket监听器...")
    
    websocket_script = """
    // 清理之前的监听器
    window.keepaMessages = [];
    window.keepaConnections = [];
    window.keepaDebug = [];
    
    console.log('设置WebSocket监听器...');
    
    // 拦截WebSocket构造函数
    const originalWebSocket = window.WebSocket;
    window.WebSocket = function(url, protocols) {
        console.log('创建WebSocket连接:', url);
        window.keepaDebug.push('WebSocket创建: ' + url);
        
        const ws = new originalWebSocket(url, protocols);
        
        window.keepaConnections.push({
            url: url,
            readyState: ws.readyState,
            timestamp: Date.now()
        });
        
        ws.addEventListener('open', function(event) {
            console.log('WebSocket已连接:', url);
            window.keepaDebug.push('WebSocket连接成功: ' + url);
        });
        
        ws.addEventListener('message', function(event) {
            try {
                console.log('收到WebSocket消息，大小:', event.data.length);
                window.keepaDebug.push('收到消息，大小: ' + event.data.length);
                
                window.keepaMessages.push({
                    data: event.data,
                    timestamp: Date.now(),
                    url: url,
                    size: event.data.length
                });
            } catch (e) {
                console.log('WebSocket消息捕获错误:', e);
                window.keepaDebug.push('消息捕获错误: ' + e.message);
            }
        });
        
        ws.addEventListener('error', function(event) {
            console.log('WebSocket错误:', event);
            window.keepaDebug.push('WebSocket错误');
        });
        
        ws.addEventListener('close', function(event) {
            console.log('WebSocket已关闭:', event.code, event.reason);
            window.keepaDebug.push('WebSocket关闭: ' + event.code);
        });
        
        return ws;
    };
    
    // 保持原型链
    Object.setPrototypeOf(window.WebSocket, originalWebSocket);
    window.WebSocket.prototype = originalWebSocket.prototype;
    
    console.log('WebSocket监听器设置完成');
    window.keepaDebug.push('监听器设置完成');
    """
    
    try:
        driver.execute_script(websocket_script)
        print("✅ WebSocket监听器设置成功")
        return True
    except Exception as e:
        print(f"❌ 设置WebSocket监听器失败: {str(e)}")
        return False

def wait_for_page_load(driver, timeout=15):
    """等待页面完全加载"""
    print("⏳ 等待页面加载...")
    
    try:
        # 等待页面加载完成
        WebDriverWait(driver, timeout).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        
        # 额外等待JavaScript执行
        time.sleep(3)
        
        print("✅ 页面加载完成")
        return True
        
    except Exception as e:
        print(f"⚠️ 页面加载超时: {str(e)}")
        return False

def check_websocket_activity(driver):
    """检查WebSocket活动"""
    try:
        debug_info = driver.execute_script("return window.keepaDebug || [];")
        connections = driver.execute_script("return window.keepaConnections || [];")
        messages = driver.execute_script("return window.keepaMessages || [];")
        
        print(f"🔍 调试信息: {len(debug_info)} 条")
        for info in debug_info[-5:]:  # 显示最后5条
            print(f"   📝 {info}")
        
        print(f"🔗 WebSocket连接: {len(connections)} 个")
        for conn in connections:
            print(f"   📡 {conn['url']} (状态: {conn['readyState']})")
        
        print(f"📨 WebSocket消息: {len(messages)} 条")
        for i, msg in enumerate(messages[-3:]):  # 显示最后3条
            print(f"   📦 消息 #{i+1}: {msg['size']} bytes from {msg['url']}")
        
        return len(messages)
        
    except Exception as e:
        print(f"❌ 检查WebSocket活动失败: {str(e)}")
        return 0

def test_keepa_bypass(asin):
    """测试Keepa绕过"""
    print(f"\n🎯 测试ASIN: {asin}")
    print("-" * 50)
    
    driver = create_stealth_chrome_driver()
    if not driver:
        return None
    
    try:
        # 设置WebSocket监听
        if not setup_websocket_listener(driver):
            return None
        
        # 预热：访问主页
        print("🔥 预热会话 - 访问Keepa主页...")
        driver.get("https://keepa.com")
        
        if not wait_for_page_load(driver):
            print("❌ 主页加载失败")
            return None
        
        # 检查页面标题
        page_title = driver.title
        print(f"📄 页面标题: {page_title}")
        
        # 检查初始WebSocket活动
        print("\n📊 预热阶段WebSocket检查:")
        check_websocket_activity(driver)
        
        # 访问产品页面
        product_url = f"https://keepa.com/#!product/1-{asin}"
        print(f"\n🛒 访问产品页面: {product_url}")
        
        driver.get(product_url)
        
        if not wait_for_page_load(driver):
            print("❌ 产品页面加载失败")
            return None
        
        # 等待WebSocket数据
        print("⏳ 等待WebSocket数据加载...")
        time.sleep(10)
        
        # 检查WebSocket活动
        print("\n📊 产品页面WebSocket检查:")
        message_count = check_websocket_activity(driver)
        
        if message_count == 0:
            print("❌ 未检测到WebSocket消息")
            
            # 尝试手动触发
            print("🔄 尝试手动触发数据加载...")
            try:
                # 滚动页面
                driver.execute_script("window.scrollTo(0, 500);")
                time.sleep(2)
                driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(3)
                
                # 再次检查
                message_count = check_websocket_activity(driver)
            except:
                pass
        
        if message_count > 0:
            print(f"✅ 检测到 {message_count} 条WebSocket消息，开始解析...")
            
            # 收集并解析消息
            messages = driver.execute_script("return window.keepaMessages || [];")
            
            for i, msg in enumerate(messages):
                try:
                    data = msg['data']
                    if isinstance(data, str) and len(data) > 100:
                        print(f"🔍 分析消息 #{i+1} ({len(data)} bytes)...")
                        
                        # 检查是否包含产品数据
                        if 'basicProducts' in data:
                            print(f"✅ 找到产品数据!")
                            
                            try:
                                json_data = json.loads(data)
                                if 'basicProducts' in json_data:
                                    products = json_data['basicProducts']
                                    for product in products:
                                        if product.get('asin') == asin:
                                            title = product.get('title', '未知')
                                            csv_data = product.get('csv', [])
                                            
                                            if csv_data and len(csv_data) >= 2:
                                                # 提取价格
                                                prices = []
                                                for j in range(0, len(csv_data), 2):
                                                    if j + 1 < len(csv_data):
                                                        price = csv_data[j + 1]
                                                        if 100 <= price <= 200000:  # $1-$2000
                                                            prices.append(price)
                                                
                                                if prices:
                                                    highest = max(prices) / 100.0
                                                    lowest = min(prices) / 100.0
                                                    
                                                    print(f"🎉 成功解析 {asin}:")
                                                    print(f"   📝 标题: {title[:50]}...")
                                                    print(f"   📊 最高价: ${highest:.2f}")
                                                    print(f"   📊 最低价: ${lowest:.2f}")
                                                    print(f"   📈 数据点: {len(prices)}")
                                                    
                                                    return {
                                                        'asin': asin,
                                                        'title': title,
                                                        'highest_price': highest,
                                                        'lowest_price': lowest,
                                                        'data_points': len(prices),
                                                        'success': True
                                                    }
                            except json.JSONDecodeError as e:
                                print(f"⚠️ JSON解析失败: {str(e)}")
                                continue
                        
                        # 检查错误状态
                        elif '"status":401' in data or '"status": 401' in data:
                            print(f"❌ 检测到401错误 - 需要更强的绕过技术")
                        elif '"status":200' in data or '"status": 200' in data:
                            print(f"✅ 检测到200状态 - 请求成功但可能无产品数据")
                            
                except Exception as e:
                    print(f"⚠️ 解析消息异常: {str(e)}")
                    continue
        
        print(f"❌ 未能解析 {asin} 的有效数据")
        return {'asin': asin, 'success': False, 'error': '未找到有效数据'}
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return {'asin': asin, 'success': False, 'error': str(e)}
        
    finally:
        try:
            driver.quit()
        except:
            pass

def main():
    """主测试函数"""
    print("🚀 改进的手动Keepa绕过测试")
    print("🛠️ 修复JavaScript和WebSocket问题")
    print("=" * 60)
    
    # 测试ASIN
    test_asins = [
        "B07TZDH6G4",  # 已知有数据的ASIN
    ]
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n[{i}/{len(test_asins)}] 开始测试...")
        
        start_time = time.time()
        result = test_keepa_bypass(asin)
        elapsed_time = time.time() - start_time
        
        if result and result.get('success'):
            print(f"✅ 测试成功!")
            print(f"⏱️ 处理时间: {elapsed_time:.1f}s")
        else:
            error = result.get('error', '未知错误') if result else '测试失败'
            print(f"❌ 测试失败: {error}")
            print(f"⏱️ 处理时间: {elapsed_time:.1f}s")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
