#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终集成测试 - 测试完整的历史价格功能
"""

import sys
import os
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def test_batch_processing():
    """测试批量处理功能"""
    print("🔍 测试批量处理功能...")
    
    # 创建测试ASIN文件
    test_asins = [
        "B07QNYTRG2",  # 已知有效的ASIN
        "B08N5WRWNW",  # 另一个测试ASIN
        "B0BSHF7LLL",  # 第三个测试ASIN
    ]
    
    # 写入测试文件
    test_file = "test_asins.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        for asin in test_asins:
            f.write(f"{asin}\n")
    
    print(f"📝 创建测试文件: {test_file}")
    print(f"📊 包含 {len(test_asins)} 个ASIN")
    
    # 创建分析器实例
    analyzer = KeepaHistoricalPriceChecker(
        threads=3,  # 使用3个线程
        verbose=True
    )
    
    print("\n🚀 开始批量处理...")
    start_time = time.time()
    
    try:
        # 处理ASIN列表
        results = []
        
        for i, asin in enumerate(test_asins, 1):
            print(f"\n📱 处理 {i}/{len(test_asins)}: {asin}")
            
            result = analyzer.reverse_engineer_keepa(asin)
            
            if result:
                results.append(result)
                print(f"✅ 成功: 最高${result.get('historical_highest_price', 0):.2f} 最低${result.get('historical_lowest_price', 0):.2f}")
                if result.get('current_price'):
                    print(f"   当前价格: ${result.get('current_price'):.2f}")
                print(f"   数据源: {result.get('source', 'unknown')}")
            else:
                print(f"❌ 失败: 无法获取价格数据")
            
            # 添加延迟避免请求过快
            time.sleep(2)
    
    except KeyboardInterrupt:
        print("\n⚠️  用户中断处理")
    except Exception as e:
        print(f"\n❌ 处理异常: {str(e)}")
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n📊 处理完成:")
    print(f"   总耗时: {processing_time:.1f} 秒")
    print(f"   成功数量: {len(results)}/{len(test_asins)}")
    print(f"   成功率: {len(results)/len(test_asins)*100:.1f}%")
    
    # 保存结果
    if results:
        print(f"\n💾 保存结果...")
        
        result_file = "test_results.txt"
        with open(result_file, 'w', encoding='utf-8') as f:
            f.write("Keepa历史价格分析结果\n")
            f.write("=" * 50 + "\n\n")
            
            for result in results:
                f.write(f"ASIN: {result.get('asin', 'Unknown')}\n")
                f.write(f"标题: {result.get('title', 'Unknown')}\n")
                f.write(f"历史最高价: ${result.get('historical_highest_price', 0):.2f}\n")
                f.write(f"历史最低价: ${result.get('historical_lowest_price', 0):.2f}\n")
                if result.get('current_price'):
                    f.write(f"当前价格: ${result.get('current_price'):.2f}\n")
                f.write(f"数据源: {result.get('source', 'unknown')}\n")
                f.write("-" * 30 + "\n\n")
        
        print(f"📄 结果已保存到: {result_file}")
    
    # 清理测试文件
    try:
        os.remove(test_file)
        print(f"🗑️  清理测试文件: {test_file}")
    except:
        pass
    
    return len(results) > 0

def test_single_asin():
    """测试单个ASIN处理"""
    print("🔍 测试单个ASIN处理...")
    
    analyzer = KeepaHistoricalPriceChecker(verbose=True)
    
    test_asin = "B07QNYTRG2"
    print(f"📱 测试ASIN: {test_asin}")
    
    start_time = time.time()
    result = analyzer.reverse_engineer_keepa(test_asin)
    end_time = time.time()
    
    if result:
        print(f"✅ 处理成功 (耗时: {end_time - start_time:.1f}秒)")
        print(f"   ASIN: {result.get('asin')}")
        print(f"   标题: {result.get('title', 'Unknown')[:50]}...")
        print(f"   历史最高价: ${result.get('historical_highest_price', 0):.2f}")
        print(f"   历史最低价: ${result.get('historical_lowest_price', 0):.2f}")
        if result.get('current_price'):
            print(f"   当前价格: ${result.get('current_price'):.2f}")
        print(f"   数据源: {result.get('source', 'unknown')}")
        return True
    else:
        print(f"❌ 处理失败 (耗时: {end_time - start_time:.1f}秒)")
        return False

def main():
    """主测试函数"""
    print("🚀 开始最终集成测试...")
    print("=" * 60)
    
    # 测试单个ASIN
    print("\n1️⃣ 单个ASIN测试")
    single_success = test_single_asin()
    
    # 测试批量处理
    print("\n2️⃣ 批量处理测试")
    batch_success = test_batch_processing()
    
    print("\n" + "=" * 60)
    print("📋 最终测试结果:")
    print(f"   单个ASIN测试: {'✅ 通过' if single_success else '❌ 失败'}")
    print(f"   批量处理测试: {'✅ 通过' if batch_success else '❌ 失败'}")
    
    if single_success and batch_success:
        print("\n🎉 所有测试通过！WebSocket集成完全成功！")
        print("💡 现在可以正常使用历史价格8.py脚本了")
        return True
    else:
        print("\n⚠️  部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
