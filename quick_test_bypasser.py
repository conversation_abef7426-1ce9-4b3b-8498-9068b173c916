#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试版本的Keepa绕过器
用于验证核心功能
"""

import time
import random
import json
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC

def create_stealth_driver():
    """创建隐蔽的Chrome驱动器"""
    print("🔧 创建隐蔽Chrome驱动器...")
    
    options = uc.ChromeOptions()
    
    # 基础隐蔽配置
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_argument('--disable-extensions')
    options.add_argument('--disable-plugins')
    options.add_argument('--disable-images')
    
    # 设置窗口大小
    options.add_argument('--window-size=1366,768')
    
    # 设置用户代理
    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    options.add_argument(f'--user-agent={user_agent}')
    
    try:
        driver = uc.Chrome(options=options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(10)
        
        # 注入反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ Chrome驱动器创建成功")
        return driver
        
    except Exception as e:
        print(f"❌ 创建驱动器失败: {str(e)}")
        return None

def setup_websocket_listener(driver):
    """设置WebSocket监听器"""
    print("📡 设置WebSocket监听器...")
    
    websocket_script = """
    window.keepaMessages = [];
    
    const originalWebSocket = window.WebSocket;
    window.WebSocket = function(url, protocols) {
        const ws = new originalWebSocket(url, protocols);
        
        ws.addEventListener('message', function(event) {
            try {
                window.keepaMessages.push({
                    data: event.data,
                    timestamp: Date.now()
                });
            } catch (e) {
                console.log('WebSocket capture error:', e);
            }
        });
        
        return ws;
    };
    """
    
    try:
        driver.execute_script(websocket_script)
        print("✅ WebSocket监听器设置成功")
        return True
    except Exception as e:
        print(f"❌ 设置WebSocket监听器失败: {str(e)}")
        return False

def simulate_human_behavior(driver):
    """模拟人类行为"""
    print("🤖 模拟人类行为...")
    
    try:
        # 随机鼠标移动
        driver.execute_script("""
            function randomMove() {
                const event = new MouseEvent('mousemove', {
                    clientX: Math.random() * window.innerWidth,
                    clientY: Math.random() * window.innerHeight
                });
                document.dispatchEvent(event);
            }
            for(let i = 0; i < 3; i++) {
                setTimeout(randomMove, i * 100);
            }
        """)
        
        # 随机滚动
        scroll_amount = random.randint(100, 300)
        driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
        
        time.sleep(random.uniform(1, 2))
        print("✅ 人类行为模拟完成")
        
    except Exception as e:
        print(f"⚠️ 行为模拟异常: {str(e)}")

def test_single_asin(asin):
    """测试单个ASIN"""
    print(f"\n🎯 测试ASIN: {asin}")
    print("-" * 50)
    
    driver = create_stealth_driver()
    if not driver:
        return None
    
    try:
        # 设置WebSocket监听
        setup_websocket_listener(driver)
        
        # 访问Keepa主页进行预热
        print("🔥 预热会话...")
        driver.get("https://keepa.com")
        time.sleep(3)
        
        # 模拟行为
        simulate_human_behavior(driver)
        
        # 访问产品页面
        product_url = f"https://keepa.com/#!product/1-{asin}"
        print(f"🛒 访问产品页面: {product_url}")
        
        driver.get(product_url)
        time.sleep(10)  # 等待页面加载
        
        # 再次模拟行为
        simulate_human_behavior(driver)
        time.sleep(5)
        
        # 收集WebSocket消息
        print("📨 收集WebSocket消息...")
        messages = driver.execute_script("return window.keepaMessages || [];")
        
        print(f"📊 收集到 {len(messages)} 条消息")
        
        # 简单解析
        for i, msg in enumerate(messages):
            try:
                data = msg['data']
                if isinstance(data, str) and 'basicProducts' in data:
                    print(f"✅ 找到产品数据消息 #{i+1}")
                    
                    # 尝试解析JSON
                    try:
                        json_data = json.loads(data)
                        if 'basicProducts' in json_data:
                            products = json_data['basicProducts']
                            for product in products:
                                if product.get('asin') == asin:
                                    title = product.get('title', '未知')
                                    csv_data = product.get('csv', [])
                                    
                                    if csv_data:
                                        # 提取价格
                                        prices = []
                                        for j in range(0, len(csv_data), 2):
                                            if j + 1 < len(csv_data):
                                                price = csv_data[j + 1]
                                                if 100 <= price <= 200000:  # 过滤异常价格
                                                    prices.append(price)
                                        
                                        if prices:
                                            highest = max(prices) / 100.0
                                            lowest = min(prices) / 100.0
                                            
                                            result = {
                                                'asin': asin,
                                                'title': title,
                                                'highest_price': highest,
                                                'lowest_price': lowest,
                                                'success': True
                                            }
                                            
                                            print(f"✅ 成功解析 {asin}:")
                                            print(f"   📝 标题: {title[:50]}...")
                                            print(f"   📊 最高价: ${highest}")
                                            print(f"   📊 最低价: ${lowest}")
                                            
                                            return result
                    except json.JSONDecodeError:
                        continue
                        
            except Exception as e:
                print(f"⚠️ 解析消息异常: {str(e)}")
                continue
        
        print(f"❌ 未能解析 {asin} 的数据")
        return {'asin': asin, 'success': False, 'error': '未找到有效数据'}
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return {'asin': asin, 'success': False, 'error': str(e)}
        
    finally:
        try:
            driver.quit()
        except:
            pass

def main():
    """主测试函数"""
    print("🚀 快速Keepa绕过测试")
    print("🎯 使用undetected-chromedriver")
    print("=" * 60)
    
    # 测试ASIN
    test_asins = [
        "B07TZDH6G4",  # 已知有数据的ASIN
        "B07SPTL99F",  # 测试ASIN
    ]
    
    results = []
    success_count = 0
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n[{i}/{len(test_asins)}] 开始测试...")
        
        start_time = time.time()
        result = test_single_asin(asin)
        elapsed_time = time.time() - start_time
        
        if result and result.get('success'):
            success_count += 1
            print(f"⏱️ 处理时间: {elapsed_time:.1f}s")
        else:
            error = result.get('error', '未知错误') if result else '测试失败'
            print(f"❌ 失败: {error}")
            print(f"⏱️ 处理时间: {elapsed_time:.1f}s")
        
        results.append(result)
        
        # 测试间隔
        if i < len(test_asins):
            print("⏳ 等待10秒后继续...")
            time.sleep(10)
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    
    for result in results:
        if result and result.get('success'):
            print(f"✅ {result['asin']}: 成功")
        else:
            asin = result['asin'] if result else '未知'
            error = result.get('error', '未知') if result else '测试失败'
            print(f"❌ {asin}: {error}")
    
    success_rate = (success_count / len(test_asins) * 100) if test_asins else 0
    print(f"\n📊 成功率: {success_count}/{len(test_asins)} ({success_rate:.1f}%)")
    
    if success_rate > 0:
        print("🎉 绕过功能有效!")
    else:
        print("❌ 需要进一步优化")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
