#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的历史价格8.py脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入是否成功"""
    try:
        from 历史价格8 import KeepaHistoricalPriceChecker
        print("✅ 成功导入 KeepaHistoricalPriceChecker")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        return False

def test_proxy_loading():
    """测试代理加载功能"""
    try:
        from 历史价格8 import KeepaHistoricalPriceChecker
        checker = KeepaHistoricalPriceChecker(verbose=True)
        
        print(f"✅ 成功创建检查器实例")
        print(f"✅ 加载了 {len(checker.proxies)} 个代理")
        print(f"✅ 请求统计初始化: 总请求={checker.request_count}, 成功={checker.success_count}, 失败={checker.error_count}")
        
        return True
    except Exception as e:
        print(f"❌ 代理加载测试失败: {str(e)}")
        return False

def test_http_request():
    """测试HTTP请求功能"""
    try:
        from 历史价格8 import KeepaHistoricalPriceChecker
        checker = KeepaHistoricalPriceChecker(verbose=True)
        
        # 测试一个简单的HTTP请求
        test_url = "https://httpbin.org/get"
        response = checker.make_request_with_retry(test_url, max_retries=1)
        
        if response and response.status_code == 200:
            print("✅ HTTP请求功能正常")
            return True
        else:
            print("❌ HTTP请求失败")
            return False
    except Exception as e:
        print(f"❌ HTTP请求测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试修改后的历史价格8.py脚本...")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_import),
        ("代理加载测试", test_proxy_loading),
        ("HTTP请求测试", test_http_request),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行 {test_name}...")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！脚本修改成功！")
        print("\n✨ 主要改进:")
        print("  - 移除了Selenium依赖，改用HTTP请求")
        print("  - 添加了代理支持和轮换")
        print("  - 增加了请求监控和分析功能")
        print("  - 保持了原有的UI和多线程功能")
    else:
        print("⚠️  部分测试失败，请检查代码")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
