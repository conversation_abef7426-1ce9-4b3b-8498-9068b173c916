#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keepa逆向工程最终解决方案测试
"""

import sys
import os
from 历史价格8 import KeepaHistoricalPriceChe<PERSON>

def test_final_keepa_solution():
    """测试最终的Keepa解决方案"""
    print("🎯 Keepa逆向工程最终解决方案测试")
    print("=" * 60)
    
    try:
        # 创建检查器
        checker = KeepaHistoricalPriceChecker(marketplace="US", verbose=True)
        checker.load_proxies()
        
        test_asins = ["B07QNYTRG2", "B07SPTL99F"]
        
        for asin in test_asins:
            print(f"\n{'🔍' * 20} 测试 {asin} {'🔍' * 20}")
            
            # 方法1: 完整的逆向工程流程
            print(f"🚀 方法1: 完整逆向工程流程")
            result = checker.reverse_engineer_keepa(asin)
            
            if result and (result.get('historical_highest_price') or result.get('historical_lowest_price')):
                print(f"✅ 逆向工程成功!")
                print(f"   最高价: ${result.get('historical_highest_price', 'N/A')}")
                print(f"   最低价: ${result.get('historical_lowest_price', 'N/A')}")
                print(f"   断货状态: {result.get('out_of_stock_within_month', 'N/A')}")
                continue
            else:
                print(f"❌ 逆向工程未获取到价格数据")
            
            # 方法2: 直接WebSocket通信
            print(f"\n🔌 方法2: 改进的WebSocket通信")
            main_page_data = checker.get_keepa_main_page(asin)
            if main_page_data:
                ws_result = checker.simulate_keepa_websocket(asin, main_page_data)
                if ws_result and (ws_result.get('historical_highest_price') or ws_result.get('historical_lowest_price')):
                    print(f"✅ WebSocket成功!")
                    print(f"   最高价: ${ws_result.get('historical_highest_price', 'N/A')}")
                    print(f"   最低价: ${ws_result.get('historical_lowest_price', 'N/A')}")
                    continue
                else:
                    print(f"❌ WebSocket未获取到价格数据")
            
            # 方法3: 常规HTTP请求（作为对比）
            print(f"\n🌐 方法3: 常规HTTP请求（对比）")
            http_result = checker.get_keepa_info(asin)
            if http_result and (http_result.get('historical_highest_price') or http_result.get('historical_lowest_price')):
                print(f"✅ HTTP请求成功!")
                print(f"   最高价: ${http_result.get('historical_highest_price', 'N/A')}")
                print(f"   最低价: ${http_result.get('historical_lowest_price', 'N/A')}")
            else:
                print(f"❌ HTTP请求未获取到价格数据")
            
            print(f"\n📊 {asin} 测试总结:")
            print(f"   逆向工程: {'✅' if result else '❌'}")
            print(f"   WebSocket: {'✅' if 'ws_result' in locals() and ws_result else '❌'}")
            print(f"   HTTP请求: {'✅' if 'http_result' in locals() and http_result else '❌'}")
        
        # 显示统计信息
        print(f"\n📈 请求统计:")
        print(f"   总请求数: {checker.request_count}")
        print(f"   成功请求: {checker.success_count}")
        print(f"   失败请求: {checker.error_count}")
        print(f"   成功率: {(checker.success_count / max(checker.request_count, 1)) * 100:.1f}%")
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

def analyze_keepa_protocol():
    """分析Keepa通信协议"""
    print(f"\n🔬 Keepa通信协议分析")
    print("=" * 40)
    
    try:
        import websocket
        import json
        import threading
        import time
        
        messages = []
        
        def on_message(ws, message):
            messages.append(message)
            if isinstance(message, bytes):
                print(f"📦 二进制消息: {len(message)} 字节")
                try:
                    # 尝试不同的解压方法
                    import gzip
                    try:
                        decompressed = gzip.decompress(message)
                        print(f"   GZIP解压: {decompressed[:100]}...")
                    except:
                        print(f"   GZIP解压失败")
                    
                    # 尝试其他解压方法
                    try:
                        import zlib
                        decompressed = zlib.decompress(message)
                        print(f"   ZLIB解压: {decompressed[:100]}...")
                    except:
                        print(f"   ZLIB解压失败")
                        
                except Exception as e:
                    print(f"   解压失败: {str(e)}")
            else:
                print(f"📨 文本消息: {message}")
                try:
                    data = json.loads(message)
                    print(f"   解析结果: {data}")
                except:
                    print(f"   JSON解析失败")
        
        def on_error(ws, error):
            print(f"❌ 错误: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print(f"🔌 连接关闭: {close_status_code}")
        
        def on_open(ws):
            print(f"✅ 连接建立")
            
            # 发送各种测试消息
            test_messages = [
                {"type": "ping"},
                {"cmd": "ping"},
                {"action": "ping"},
                {"type": "init"},
                {"type": "auth", "guest": 1},
                {"type": "product", "domain": 1, "asin": "B07QNYTRG2"},
            ]
            
            for i, msg in enumerate(test_messages):
                try:
                    ws.send(json.dumps(msg))
                    print(f"📤 发送消息 {i+1}: {msg}")
                    time.sleep(2)  # 等待响应
                except Exception as e:
                    print(f"❌ 发送失败: {str(e)}")
        
        # 连接WebSocket
        ws_url = "wss://push.keepa.com/apps/cloud/?app=keepaWebsite&version=3.0"
        ws = websocket.WebSocketApp(ws_url,
                                  on_open=on_open,
                                  on_message=on_message,
                                  on_error=on_error,
                                  on_close=on_close)
        
        # 运行15秒
        ws_thread = threading.Thread(target=ws.run_forever)
        ws_thread.daemon = True
        ws_thread.start()
        
        time.sleep(15)
        ws.close()
        
        print(f"\n📊 协议分析结果:")
        print(f"   收到消息数: {len(messages)}")
        print(f"   二进制消息: {sum(1 for m in messages if isinstance(m, bytes))}")
        print(f"   文本消息: {sum(1 for m in messages if not isinstance(m, bytes))}")
        
    except ImportError:
        print(f"❌ 需要安装websocket-client: pip install websocket-client")
    except Exception as e:
        print(f"❌ 协议分析异常: {str(e)}")

def provide_solution_summary():
    """提供解决方案总结"""
    print(f"\n📋 Keepa逆向工程解决方案总结")
    print("=" * 50)
    
    print(f"""
🎯 问题分析:
   ✅ Keepa是JavaScript单页应用，数据通过WebSocket动态加载
   ✅ HTTP请求只能获取HTML框架，无法获取产品数据
   ✅ 需要WebSocket通信获取实际价格信息

🔧 实现的解决方案:
   1. 📡 API端点检测 - 分析JavaScript文件寻找API端点
   2. 🌐 直接API调用 - 尝试各种可能的API端点
   3. 🔌 WebSocket通信 - 模拟浏览器的WebSocket连接
   4. 🔑 认证处理 - 处理访客模式和认证流程
   5. 📦 数据解析 - 从各种响应格式中提取价格数据

🚀 关键突破:
   ✅ 成功建立WebSocket连接
   ✅ 收到Keepa服务器认证响应 (status: 108)
   ✅ 识别访客模式限制
   ✅ 实现认证流程处理

💡 下一步优化:
   1. 🔐 实现完整的认证机制
   2. 📦 处理二进制数据解压缩
   3. 🔄 优化消息格式和协议
   4. ⚡ 提高数据提取成功率
   5. 🛡️  增强反检测能力

📊 当前状态:
   ✅ 基础框架完成
   ✅ WebSocket通信建立
   🔄 数据提取优化中
   🔄 认证机制完善中
""")

if __name__ == "__main__":
    print("🚀 开始Keepa逆向工程最终测试")
    
    # 测试最终解决方案
    test_final_keepa_solution()
    
    # 分析通信协议
    analyze_keepa_protocol()
    
    # 提供解决方案总结
    provide_solution_summary()
    
    print(f"\n🌍 最终测试完成!")
    print(f"请查看生成的调试文件获取更多信息")
