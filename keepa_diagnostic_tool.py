#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keepa诊断工具
深入分析为什么WebSocket连接失败
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

def create_diagnostic_driver():
    """创建诊断用的Chrome驱动器"""
    print("🔧 创建诊断Chrome驱动器...")
    
    options = Options()
    
    # 最小化反检测配置，专注于诊断
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--window-size=1366,768')
    
    # 启用详细日志
    options.add_argument('--enable-logging')
    options.add_argument('--log-level=0')
    
    try:
        driver = webdriver.Chrome(options=options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(10)
        
        print("✅ 诊断驱动器创建成功")
        return driver
        
    except Exception as e:
        print(f"❌ 创建驱动器失败: {str(e)}")
        return None

def setup_comprehensive_monitoring(driver):
    """设置全面的监控"""
    print("📊 设置全面监控...")
    
    monitoring_script = """
    // 全面监控脚本
    window.keepaDiagnostic = {
        websockets: [],
        networkRequests: [],
        errors: [],
        console: [],
        pageEvents: []
    };
    
    // 监控WebSocket
    const originalWebSocket = window.WebSocket;
    window.WebSocket = function(url, protocols) {
        console.log('🔗 WebSocket创建尝试:', url);
        window.keepaDiagnostic.websockets.push({
            action: 'create_attempt',
            url: url,
            timestamp: Date.now()
        });
        
        try {
            const ws = new originalWebSocket(url, protocols);
            
            window.keepaDiagnostic.websockets.push({
                action: 'create_success',
                url: url,
                readyState: ws.readyState,
                timestamp: Date.now()
            });
            
            ws.addEventListener('open', function(event) {
                console.log('✅ WebSocket连接成功:', url);
                window.keepaDiagnostic.websockets.push({
                    action: 'open',
                    url: url,
                    timestamp: Date.now()
                });
            });
            
            ws.addEventListener('message', function(event) {
                console.log('📨 WebSocket消息:', event.data.length, 'bytes');
                window.keepaDiagnostic.websockets.push({
                    action: 'message',
                    url: url,
                    size: event.data.length,
                    timestamp: Date.now()
                });
            });
            
            ws.addEventListener('error', function(event) {
                console.log('❌ WebSocket错误:', event);
                window.keepaDiagnostic.websockets.push({
                    action: 'error',
                    url: url,
                    error: event.toString(),
                    timestamp: Date.now()
                });
            });
            
            ws.addEventListener('close', function(event) {
                console.log('🔒 WebSocket关闭:', event.code, event.reason);
                window.keepaDiagnostic.websockets.push({
                    action: 'close',
                    url: url,
                    code: event.code,
                    reason: event.reason,
                    timestamp: Date.now()
                });
            });
            
            return ws;
            
        } catch (error) {
            console.log('❌ WebSocket创建失败:', error);
            window.keepaDiagnostic.websockets.push({
                action: 'create_error',
                url: url,
                error: error.toString(),
                timestamp: Date.now()
            });
            throw error;
        }
    };
    
    // 监控网络请求
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        console.log('🌐 Fetch请求:', url);
        window.keepaDiagnostic.networkRequests.push({
            type: 'fetch',
            url: url,
            timestamp: Date.now()
        });
        return originalFetch.apply(this, args);
    };
    
    // 监控XMLHttpRequest
    const originalXHR = window.XMLHttpRequest;
    window.XMLHttpRequest = function() {
        const xhr = new originalXHR();
        const originalOpen = xhr.open;
        xhr.open = function(method, url) {
            console.log('🌐 XHR请求:', method, url);
            window.keepaDiagnostic.networkRequests.push({
                type: 'xhr',
                method: method,
                url: url,
                timestamp: Date.now()
            });
            return originalOpen.apply(this, arguments);
        };
        return xhr;
    };
    
    // 监控控制台错误
    const originalConsoleError = console.error;
    console.error = function(...args) {
        window.keepaDiagnostic.errors.push({
            type: 'console_error',
            message: args.join(' '),
            timestamp: Date.now()
        });
        return originalConsoleError.apply(this, args);
    };
    
    // 监控页面事件
    document.addEventListener('DOMContentLoaded', function() {
        window.keepaDiagnostic.pageEvents.push({
            event: 'DOMContentLoaded',
            timestamp: Date.now()
        });
    });
    
    window.addEventListener('load', function() {
        window.keepaDiagnostic.pageEvents.push({
            event: 'load',
            timestamp: Date.now()
        });
    });
    
    console.log('📊 全面监控已设置');
    """
    
    try:
        driver.execute_script(monitoring_script)
        print("✅ 全面监控设置成功")
        return True
    except Exception as e:
        print(f"❌ 设置监控失败: {str(e)}")
        return False

def analyze_page_content(driver):
    """分析页面内容"""
    print("🔍 分析页面内容...")
    
    try:
        # 检查页面标题
        title = driver.title
        print(f"📄 页面标题: {title}")
        
        # 检查页面URL
        current_url = driver.current_url
        print(f"🔗 当前URL: {current_url}")
        
        # 检查页面源码中的关键信息
        page_source = driver.page_source
        
        # 检查是否有Cloudflare
        if 'cloudflare' in page_source.lower():
            print("🛡️ 检测到Cloudflare保护")
        
        # 检查是否有错误信息
        error_indicators = ['error', 'blocked', 'denied', 'forbidden', '401', '403']
        for indicator in error_indicators:
            if indicator in page_source.lower():
                print(f"⚠️ 检测到可能的错误指示: {indicator}")
        
        # 检查是否有WebSocket相关的脚本
        if 'websocket' in page_source.lower():
            print("📡 页面包含WebSocket相关代码")
        else:
            print("❌ 页面不包含WebSocket相关代码")
        
        # 检查是否有Keepa特定的脚本
        if 'keepa' in page_source.lower():
            print("✅ 页面包含Keepa相关代码")
        else:
            print("❌ 页面不包含Keepa相关代码")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析页面内容失败: {str(e)}")
        return False

def get_diagnostic_report(driver):
    """获取诊断报告"""
    print("📋 生成诊断报告...")
    
    try:
        diagnostic_data = driver.execute_script("return window.keepaDiagnostic || {};")
        
        print("\n" + "="*60)
        print("📊 KEEPA诊断报告")
        print("="*60)
        
        # WebSocket分析
        websockets = diagnostic_data.get('websockets', [])
        print(f"\n🔗 WebSocket活动 ({len(websockets)} 个事件):")
        if websockets:
            for i, ws in enumerate(websockets):
                action = ws.get('action', '未知')
                url = ws.get('url', '未知')
                timestamp = ws.get('timestamp', 0)
                print(f"   {i+1}. {action}: {url}")
                if 'error' in ws:
                    print(f"      ❌ 错误: {ws['error']}")
        else:
            print("   ❌ 没有WebSocket活动")
        
        # 网络请求分析
        requests = diagnostic_data.get('networkRequests', [])
        print(f"\n🌐 网络请求 ({len(requests)} 个):")
        for i, req in enumerate(requests[-5:]):  # 显示最后5个
            req_type = req.get('type', '未知')
            url = req.get('url', '未知')
            print(f"   {i+1}. {req_type}: {url}")
        
        # 错误分析
        errors = diagnostic_data.get('errors', [])
        print(f"\n❌ 错误信息 ({len(errors)} 个):")
        for i, error in enumerate(errors):
            message = error.get('message', '未知')
            print(f"   {i+1}. {message}")
        
        # 页面事件分析
        events = diagnostic_data.get('pageEvents', [])
        print(f"\n📅 页面事件 ({len(events)} 个):")
        for event in events:
            event_name = event.get('event', '未知')
            print(f"   ✅ {event_name}")
        
        return diagnostic_data
        
    except Exception as e:
        print(f"❌ 获取诊断报告失败: {str(e)}")
        return {}

def diagnose_keepa_connection():
    """诊断Keepa连接问题"""
    print("🚀 Keepa连接诊断工具")
    print("🎯 分析WebSocket连接失败的原因")
    print("=" * 60)
    
    driver = create_diagnostic_driver()
    if not driver:
        return
    
    try:
        # 设置监控
        if not setup_comprehensive_monitoring(driver):
            return
        
        # 第一步：访问主页
        print("\n📍 第一步：访问Keepa主页")
        driver.get("https://keepa.com")
        time.sleep(5)
        
        analyze_page_content(driver)
        
        print("\n📊 主页诊断报告:")
        get_diagnostic_report(driver)
        
        # 第二步：访问产品页面
        print("\n📍 第二步：访问产品页面")
        asin = "B07TZDH6G4"
        product_url = f"https://keepa.com/#!product/1-{asin}"
        
        driver.get(product_url)
        time.sleep(10)  # 等待更长时间
        
        analyze_page_content(driver)
        
        print("\n📊 产品页面诊断报告:")
        final_report = get_diagnostic_report(driver)
        
        # 总结分析
        print("\n" + "="*60)
        print("🎯 诊断总结")
        print("="*60)
        
        websockets = final_report.get('websockets', [])
        if not websockets:
            print("❌ 主要问题：没有WebSocket连接尝试")
            print("💡 可能原因：")
            print("   1. Keepa检测到自动化并阻止了WebSocket初始化")
            print("   2. 页面JavaScript被阻止或修改")
            print("   3. 需要更强的反检测技术")
            print("   4. 需要用户交互才能触发WebSocket连接")
        else:
            create_attempts = [ws for ws in websockets if ws.get('action') == 'create_attempt']
            successful_connections = [ws for ws in websockets if ws.get('action') == 'open']
            errors = [ws for ws in websockets if ws.get('action') in ['error', 'create_error']]
            
            print(f"📊 WebSocket统计:")
            print(f"   连接尝试: {len(create_attempts)}")
            print(f"   成功连接: {len(successful_connections)}")
            print(f"   连接错误: {len(errors)}")
            
            if errors:
                print("❌ 主要问题：WebSocket连接错误")
                for error in errors:
                    print(f"   错误: {error.get('error', '未知')}")
            elif not successful_connections:
                print("❌ 主要问题：WebSocket连接被阻止")
            else:
                print("✅ WebSocket连接正常，问题可能在数据解析")
        
    except Exception as e:
        print(f"❌ 诊断过程异常: {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        try:
            driver.quit()
        except:
            pass

if __name__ == "__main__":
    try:
        diagnose_keepa_connection()
    except KeyboardInterrupt:
        print("\n⏹️ 诊断被中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
