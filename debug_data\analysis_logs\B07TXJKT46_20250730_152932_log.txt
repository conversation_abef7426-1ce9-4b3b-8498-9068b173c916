调试日志 - ASIN: B07TXJKT46
时间: 20250730_152932
============================================================

[15:29:19] [B07TXJKT46] 开始WebSocket数据获取
[15:29:21] [B07TXJKT46] 访问页面: https://keepa.com/#!product/1-B07TXJKT46
[15:29:22] [B07TXJKT46] WebSocket连接建立
[15:29:23] [B07TXJKT46] 页面加载完成，等待WebSocket数据
[15:29:23] [B07TXJKT46] 收到未压缩WebSocket消息: 81 字符
[15:29:24] [B07TXJKT46] 收到未压缩WebSocket消息: 366 字符
[15:29:24] [B07TXJKT46] 收到未压缩WebSocket消息: 120 字符
[15:29:24] [B07TXJKT46] 收到未压缩WebSocket消息: 140 字符
[15:29:31] [B07TXJKT46] 页面内容长度: 71937 字符
[15:29:31] [B07TXJKT46] 页面内容已保存: debug_data\html_pages\B07TXJKT46_20250730_152931.html
[15:29:31] [B07TXJKT46] WebSocket数据已保存: debug_data\websocket_data\B07TXJKT46_20250730_152931.json
[15:29:31] [B07TXJKT46] 收到 4 条WebSocket消息
[15:29:31] [B07TXJKT46] 开始解析 4 条WebSocket消息
[15:29:31] [B07TXJKT46] 解析消息 1: <class 'dict'>
[15:29:31] [B07TXJKT46] 消息不包含basicProducts，包含键: ['status', 'actionUrl', 'guest', 'n']
[15:29:31] [B07TXJKT46] 解析消息 2: <class 'dict'>
[15:29:31] [B07TXJKT46] 消息不包含basicProducts，包含键: ['exchange', 'id', 'json', 'lastUpdate', 'startTimeStamp', 'status', 'timeStamp', 'version']
[15:29:31] [B07TXJKT46] 解析消息 3: <class 'dict'>
[15:29:31] [B07TXJKT46] 消息不包含basicProducts，包含键: ['id', 'lastUpdate', 'startTimeStamp', 'status', 'timeStamp', 'version']
[15:29:31] [B07TXJKT46] 解析消息 4: <class 'dict'>
[15:29:31] [B07TXJKT46] 消息不包含basicProducts，包含键: ['checkHuman', 'id', 'lastUpdate', 'startTimeStamp', 'status', 'timeStamp', 'version']
[15:29:31] [B07TXJKT46] 所有WebSocket消息解析完成，未找到有效数据
