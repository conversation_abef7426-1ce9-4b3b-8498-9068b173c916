#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的主文件
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def test_single_asin_with_main_method():
    """测试主文件的方法是否能正常工作"""
    print("🔍 测试主文件的单个ASIN处理方法...")
    
    # 创建分析器实例，启用详细输出
    analyzer = KeepaHistoricalPriceChecker(verbose=True)
    
    # 使用已知有效的ASIN
    test_asin = "B0BQZ9K2N2"  # 这个ASIN之前测试成功过
    
    print(f"📱 测试ASIN: {test_asin}")
    print("=" * 50)
    
    try:
        # 使用主文件中的方法
        result = analyzer.get_keepa_info_with_browser(test_asin, max_retries=1)
        
        if result:
            print("✅ 主文件方法测试成功:")
            print(f"   ASIN: {test_asin}")
            print(f"   最高价: ${result.get('historical_highest_price', 0):.2f}")
            print(f"   最低价: ${result.get('historical_lowest_price', 0):.2f}")
            print(f"   断货状态: {result.get('out_of_stock_within_month', '未知')}")
            return True
        else:
            print("❌ 主文件方法测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_websocket_method():
    """测试WebSocket方法是否能正常工作"""
    print("\n🔍 测试WebSocket方法...")
    
    # 创建分析器实例，启用详细输出
    analyzer = KeepaHistoricalPriceChecker(verbose=True)
    
    # 使用已知有效的ASIN
    test_asin = "B0BQZ9K2N2"
    
    print(f"📱 测试ASIN: {test_asin}")
    print("=" * 50)
    
    try:
        # 直接使用WebSocket方法
        result = analyzer.reverse_engineer_keepa(test_asin)
        
        if result:
            print("✅ WebSocket方法测试成功:")
            print(f"   ASIN: {result.get('asin')}")
            print(f"   数据源: {result.get('source', 'unknown')}")
            print(f"   最高价: ${result.get('historical_highest_price', 0):.2f}")
            print(f"   最低价: ${result.get('historical_lowest_price', 0):.2f}")
            if result.get('current_price'):
                print(f"   当前价格: ${result.get('current_price'):.2f}")
            return True
        else:
            print("❌ WebSocket方法测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始测试修复后的代码...")
    print("=" * 60)
    
    # 测试主文件方法
    main_result = test_single_asin_with_main_method()
    
    # 测试WebSocket方法
    websocket_result = test_websocket_method()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"   主文件方法: {'✅ 成功' if main_result else '❌ 失败'}")
    print(f"   WebSocket方法: {'✅ 成功' if websocket_result else '❌ 失败'}")
    
    if main_result or websocket_result:
        print("\n🎉 至少有一个方法正常工作！")
        if main_result:
            print("💡 主文件的多线程处理应该能正常工作了")
        if websocket_result:
            print("💡 WebSocket方法依然可用作为备选方案")
        return True
    else:
        print("\n⚠️  两个方法都失败了，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
