#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多线程是否导致WebSocket方法失败
"""

import sys
import os
import threading
import time
from concurrent.futures import ThreadPoolExecutor

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def test_single_thread():
    """测试单线程处理"""
    print("🔍 测试单线程处理...")
    
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    
    # 使用之前测试成功的ASIN
    test_asins = ["B07QNYTRG2", "B004VMM718", "B08WWHDSX8"]
    
    success_count = 0
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n📱 单线程测试ASIN {i}: {asin}")
        print("-" * 40)
        
        try:
            result = analyzer.get_keepa_info_with_browser(asin, max_retries=1)
            
            if result and (result.get('historical_highest_price') is not None or 
                          result.get('historical_lowest_price') is not None):
                print(f"✅ 成功获取数据")
                success_count += 1
            else:
                print(f"❌ 无法获取数据")
                
        except Exception as e:
            print(f"❌ 异常: {str(e)}")
    
    print(f"\n📋 单线程结果: {success_count}/{len(test_asins)} 成功")
    return success_count

def process_asin_multithread(asin):
    """多线程处理单个ASIN"""
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    
    try:
        result = analyzer.get_keepa_info_with_browser(asin, max_retries=1)
        
        if result and (result.get('historical_highest_price') is not None or 
                      result.get('historical_lowest_price') is not None):
            return f"✅ {asin}: 成功"
        else:
            return f"❌ {asin}: 失败"
            
    except Exception as e:
        return f"❌ {asin}: 异常 - {str(e)}"

def test_multi_thread():
    """测试多线程处理"""
    print("\n🔍 测试多线程处理...")
    
    # 使用相同的ASIN
    test_asins = ["B07QNYTRG2", "B004VMM718", "B08WWHDSX8"]
    
    print(f"使用 {len(test_asins)} 个线程同时处理...")
    
    # 使用ThreadPoolExecutor模拟主程序的多线程处理
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(process_asin_multithread, asin) for asin in test_asins]
        
        results = []
        for future in futures:
            try:
                result = future.result(timeout=30)
                results.append(result)
                print(result)
            except Exception as e:
                results.append(f"❌ 线程异常: {str(e)}")
                print(f"❌ 线程异常: {str(e)}")
    
    success_count = sum(1 for r in results if "成功" in r)
    print(f"\n📋 多线程结果: {success_count}/{len(test_asins)} 成功")
    return success_count

def test_sequential_vs_concurrent():
    """测试顺序处理 vs 并发处理"""
    print("\n🔍 测试顺序处理 vs 并发处理...")
    
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    test_asins = ["B07QNYTRG2", "B004VMM718"]
    
    # 顺序处理
    print("\n📱 顺序处理:")
    sequential_success = 0
    for asin in test_asins:
        try:
            result = analyzer.reverse_engineer_keepa(asin)
            if result and analyzer.is_valid_websocket_result(result):
                print(f"✅ {asin}: 成功")
                sequential_success += 1
            else:
                print(f"❌ {asin}: 失败")
        except Exception as e:
            print(f"❌ {asin}: 异常 - {str(e)}")
    
    # 并发处理（快速连续）
    print("\n📱 快速连续处理:")
    concurrent_success = 0
    for asin in test_asins:
        try:
            # 不等待，快速连续处理
            result = analyzer.reverse_engineer_keepa(asin)
            if result and analyzer.is_valid_websocket_result(result):
                print(f"✅ {asin}: 成功")
                concurrent_success += 1
            else:
                print(f"❌ {asin}: 失败")
        except Exception as e:
            print(f"❌ {asin}: 异常 - {str(e)}")
    
    print(f"\n📋 对比结果:")
    print(f"   顺序处理: {sequential_success}/{len(test_asins)} 成功")
    print(f"   快速连续: {concurrent_success}/{len(test_asins)} 成功")
    
    return sequential_success, concurrent_success

def main():
    """主函数"""
    print("🚀 诊断多线程问题...")
    print("=" * 60)
    
    # 测试单线程
    single_success = test_single_thread()
    
    # 测试多线程
    multi_success = test_multi_thread()
    
    # 测试顺序 vs 并发
    seq_success, conc_success = test_sequential_vs_concurrent()
    
    print("\n" + "=" * 60)
    print("📋 最终诊断结果:")
    print(f"   单线程处理: {single_success > 0}")
    print(f"   多线程处理: {multi_success > 0}")
    print(f"   顺序处理: {seq_success > 0}")
    print(f"   快速连续: {conc_success > 0}")
    
    if single_success > 0 and multi_success == 0:
        print("\n🎯 问题确认: 多线程导致WebSocket方法失败")
        print("💡 解决方案:")
        print("   1. 修改主程序使用单线程处理")
        print("   2. 添加线程同步机制")
        print("   3. 在WebSocket调用之间添加延迟")
        return False
    elif single_success > 0 and multi_success > 0:
        print("\n✅ 多线程工作正常，可能是其他问题")
        return True
    else:
        print("\n⚠️  所有方法都失败，可能是网络或其他问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
