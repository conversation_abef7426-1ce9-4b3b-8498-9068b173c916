#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keepa网络请求分析器
使用playwright自动化浏览器，捕获并分析真实的API请求
"""

import asyncio
import json
import re
import time
from playwright.async_api import async_playwright
from urllib.parse import urlparse, parse_qs

class KeepaNetworkAnalyzer:
    def __init__(self):
        self.requests = []
        self.responses = []
        self.price_data_found = []
        
    async def analyze_keepa_requests(self, asin):
        """分析Keepa的网络请求"""
        print(f"🔍 开始分析ASIN: {asin} 的网络请求")
        print("=" * 60)
        
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(
                headless=False,  # 显示浏览器窗口，方便观察
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080}
            )
            
            page = await context.new_page()
            
            # 监听网络请求
            page.on('request', self.on_request)
            page.on('response', self.on_response)
            
            try:
                # 访问Keepa产品页面
                keepa_url = f"https://keepa.com/#!product/1-{asin}"
                print(f"📱 访问页面: {keepa_url}")
                
                await page.goto(keepa_url, wait_until='networkidle', timeout=30000)
                
                # 等待页面完全加载
                print("⏳ 等待页面加载...")
                await asyncio.sleep(5)
                
                # 尝试等待价格图表加载
                try:
                    await page.wait_for_selector('.graph', timeout=10000)
                    print("📊 价格图表已加载")
                except:
                    print("⚠️  未检测到价格图表，继续分析...")
                
                # 等待更长时间，让WebSocket连接建立并获取数据
                print("⏳ 等待WebSocket连接和数据加载...")
                await asyncio.sleep(10)

                # 尝试执行JavaScript来获取页面数据
                try:
                    # 检查是否有价格数据
                    price_data = await page.evaluate("""
                        () => {
                            // 尝试获取页面中的价格信息
                            const priceElements = document.querySelectorAll('[class*="price"], [class*="Price"], .graph, .chart');
                            const results = [];

                            priceElements.forEach(el => {
                                if (el.textContent && el.textContent.includes('$')) {
                                    results.push({
                                        element: el.className,
                                        text: el.textContent.trim()
                                    });
                                }
                            });

                            // 检查全局变量
                            const globals = {};
                            if (window.keepaApp) globals.keepaApp = 'exists';
                            if (window.productData) globals.productData = window.productData;
                            if (window.priceHistory) globals.priceHistory = window.priceHistory;

                            return {
                                priceElements: results,
                                globals: globals,
                                url: window.location.href
                            };
                        }
                    """)

                    if price_data:
                        print(f"📊 页面数据: {price_data}")

                except Exception as e:
                    print(f"⚠️  JavaScript执行失败: {str(e)}")

                # 再等待一些时间确保所有AJAX请求完成
                await asyncio.sleep(5)
                
                # 分析捕获的请求
                await self.analyze_captured_data(asin)
                
            except Exception as e:
                print(f"❌ 页面加载失败: {str(e)}")
            
            finally:
                await browser.close()
    
    async def on_request(self, request):
        """请求监听器"""
        url = request.url
        method = request.method
        headers = await request.all_headers()
        
        # 过滤相关请求
        if self.is_relevant_request(url):
            request_data = {
                'url': url,
                'method': method,
                'headers': headers,
                'post_data': request.post_data,
                'timestamp': time.time()
            }
            self.requests.append(request_data)
            print(f"📤 捕获请求: {method} {url}")
    
    async def on_response(self, response):
        """响应监听器"""
        url = response.url
        status = response.status
        headers = await response.all_headers()
        
        if self.is_relevant_request(url):
            try:
                # 尝试获取响应内容
                content = await response.text()
                
                response_data = {
                    'url': url,
                    'status': status,
                    'headers': headers,
                    'content': content,
                    'timestamp': time.time()
                }
                self.responses.append(response_data)
                
                # 检查是否包含价格数据
                if self.contains_price_data(content):
                    self.price_data_found.append(response_data)
                    print(f"💰 发现价格数据: {url}")
                
            except Exception as e:
                print(f"⚠️  无法读取响应内容: {url} - {str(e)}")
    
    def is_relevant_request(self, url):
        """判断是否是相关请求"""
        # 高优先级模式 - 这些肯定是相关的
        high_priority_patterns = [
            r'push\.keepa\.com',  # WebSocket服务器
            r'keepa\.com.*query',  # 查询API
            r'keepa\.com.*product',  # 产品API
            r'keepa\.com.*price',  # 价格API
            r'keepa\.com.*data',  # 数据API
            r'keepa\.com.*graph',  # 图表API
            r'keepa\.com.*history',  # 历史数据API
            r'keepa\.com.*asin',  # ASIN相关API
        ]

        # 一般相关模式
        relevant_patterns = [
            r'keepa\.com',
            r'api',
            r'ajax',
            r'xhr',
            r'websocket',
            r'wss://',
        ]

        # 排除静态资源，但保留可能包含数据的JS文件
        exclude_patterns = [
            r'\.(css|png|jpg|jpeg|gif|ico|woff|woff2|ttf|svg)$',
            r'google',
            r'facebook',
            r'twitter',
            r'analytics',
            r'cloudflare.*\.js',  # 排除cloudflare的JS
            r'turnstile',  # 排除验证码相关
        ]

        url_lower = url.lower()

        # 高优先级模式直接返回true
        for pattern in high_priority_patterns:
            if re.search(pattern, url_lower):
                return True

        # 检查排除模式
        for pattern in exclude_patterns:
            if re.search(pattern, url_lower):
                return False

        # 检查一般相关模式
        for pattern in relevant_patterns:
            if re.search(pattern, url_lower):
                return True

        return False
    
    def contains_price_data(self, content):
        """检查内容是否包含价格数据"""
        if not content:
            return False
        
        content_lower = content.lower()
        
        # 价格数据指标
        price_indicators = [
            r'\$\d+\.\d{2}',  # 美元价格格式
            r'"price"',
            r'"highest"',
            r'"lowest"',
            r'"current"',
            r'pricehistory',
            r'amazon',
            r'asin',
            r'\d+\.\d{2}.*\d+\.\d{2}'  # 数字价格模式
        ]
        
        for pattern in price_indicators:
            if re.search(pattern, content_lower):
                return True
        
        return False
    
    async def analyze_captured_data(self, asin):
        """分析捕获的数据"""
        print(f"\n📊 数据分析结果:")
        print(f"   总请求数: {len(self.requests)}")
        print(f"   总响应数: {len(self.responses)}")
        print(f"   包含价格数据的响应: {len(self.price_data_found)}")
        
        if self.price_data_found:
            print(f"\n💰 发现的价格数据源:")
            for i, response in enumerate(self.price_data_found, 1):
                print(f"\n--- 价格数据源 {i} ---")
                print(f"URL: {response['url']}")
                print(f"状态码: {response['status']}")
                print(f"内容长度: {len(response['content'])} 字符")
                
                # 尝试解析JSON
                try:
                    json_data = json.loads(response['content'])
                    print(f"数据类型: JSON")
                    print(f"JSON键: {list(json_data.keys()) if isinstance(json_data, dict) else 'Array'}")
                except:
                    print(f"数据类型: 文本/HTML")
                
                # 保存详细数据
                filename = f"keepa_price_data_{asin}_{i}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(response, f, indent=2, ensure_ascii=False)
                print(f"详细数据已保存到: {filename}")
        
        # 生成HTTP请求重现代码
        await self.generate_http_code(asin)
    
    async def generate_http_code(self, asin):
        """生成HTTP请求重现代码"""
        if not self.price_data_found:
            print(f"\n❌ 未找到价格数据，无法生成HTTP代码")
            return
        
        print(f"\n🔧 生成HTTP请求重现代码:")
        
        code_lines = [
            "import requests",
            "import json",
            "",
            "def get_keepa_price_data(asin):",
            "    \"\"\"获取Keepa价格数据\"\"\"",
        ]
        
        for i, response in enumerate(self.price_data_found):
            # 找到对应的请求
            matching_request = None
            for req in self.requests:
                if req['url'] == response['url']:
                    matching_request = req
                    break
            
            if matching_request:
                code_lines.extend([
                    f"    # 方法 {i+1}: {response['url']}",
                    f"    url = '{matching_request['url']}'",
                    f"    method = '{matching_request['method']}'",
                    f"    headers = {json.dumps(matching_request['headers'], indent=8)}",
                ])
                
                if matching_request['post_data']:
                    code_lines.append(f"    data = {repr(matching_request['post_data'])}")
                    code_lines.append(f"    response = requests.{matching_request['method'].lower()}(url, headers=headers, data=data)")
                else:
                    code_lines.append(f"    response = requests.{matching_request['method'].lower()}(url, headers=headers)")
                
                code_lines.extend([
                    f"    if response.status_code == 200:",
                    f"        return response.json() if 'json' in response.headers.get('content-type', '') else response.text",
                    f"    return None",
                    ""
                ])
        
        # 保存生成的代码
        code_filename = f"keepa_http_code_{asin}.py"
        with open(code_filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(code_lines))
        
        print(f"HTTP重现代码已保存到: {code_filename}")

async def main():
    """主函数"""
    # 测试ASIN
    test_asin = "B07QNYTRG2"
    
    analyzer = KeepaNetworkAnalyzer()
    await analyzer.analyze_keepa_requests(test_asin)

if __name__ == "__main__":
    asyncio.run(main())
