#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试清洁输出的Keepa解决方案
"""

import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from 历史价格8 import KeepaHistoricalPriceChecker

def test_clean_output():
    """测试清洁输出"""
    print("🚀 测试清洁输出的Keepa解决方案")
    print("=" * 50)
    
    # 测试ASIN列表
    test_asins = [
        "B07QNYTRG2",
        "B07SPTL99F", 
        "B07TXJKT46"
    ]
    
    print(f"📋 测试配置:")
    print(f"   ASIN数量: {len(test_asins)}")
    print(f"   线程数: 10")
    print(f"   预期输出: 只显示成功的价格信息")
    
    # 创建检查器（使用10个线程）
    checker = KeepaHistoricalPriceChecker(threads=10, marketplace="US", verbose=False)
    checker.load_proxies()
    
    print(f"\n🔄 开始测试...")
    start_time = time.time()
    
    results = []
    
    # 使用线程池并发处理
    with ThreadPoolExecutor(max_workers=10) as executor:
        # 提交所有任务
        future_to_asin = {
            executor.submit(checker.reverse_engineer_keepa, asin): asin 
            for asin in test_asins
        }
        
        # 收集结果
        for future in as_completed(future_to_asin):
            asin = future_to_asin[future]
            try:
                result = future.result()
                if result:
                    results.append({
                        'asin': asin,
                        'success': True,
                        'highest_price': result.get('historical_highest_price'),
                        'lowest_price': result.get('historical_lowest_price')
                    })
                else:
                    results.append({
                        'asin': asin,
                        'success': False
                    })
                    
            except Exception as e:
                print(f"❌ {asin}: 处理异常 - {str(e)}")
                results.append({
                    'asin': asin,
                    'success': False,
                    'error': str(e)
                })
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 统计结果
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"\n📊 测试结果:")
    print(f"   总耗时: {total_time:.1f}秒")
    print(f"   成功数量: {len(successful)}/{len(test_asins)}")
    print(f"   成功率: {(len(successful)/len(test_asins))*100:.1f}%")
    
    if failed:
        print(f"   失败数量: {len(failed)}")
    
    print(f"\n✅ 输出测试完成!")
    
    if len(successful) == len(test_asins):
        print("🎉 完美！输出简洁，只显示成功的价格信息！")
        print("📈 现在可以高效处理您的303个ASIN了！")
    else:
        print("⚠️  部分ASIN处理失败，但输出已经简化")
    
    return results

if __name__ == "__main__":
    test_clean_output()
