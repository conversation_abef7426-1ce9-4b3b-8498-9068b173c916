#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的主程序
验证图像API集成是否成功
"""

import sys
import os

# 导入修改后的主程序
from 历史价格8 import KeepaHistoricalPriceChecker

def test_modified_main():
    """测试修改后的主程序"""
    print("🚀 测试修改后的主程序")
    print("🔄 验证图像API集成")
    print("=" * 50)
    
    try:
        # 创建检查器实例
        checker = KeepaHistoricalPriceChecker(verbose=True)
        
        # 测试ASIN
        test_asins = [
            "B07TZDH6G4",  # 主要测试ASIN
        ]
        
        success_count = 0
        
        for i, asin in enumerate(test_asins, 1):
            print(f"\n[{i}/{len(test_asins)}] 测试ASIN: {asin}")
            print("-" * 30)
            
            try:
                # 使用修改后的reverse_engineer_keepa方法
                result = checker.reverse_engineer_keepa(asin)
                
                if result and result.get('success'):
                    print(f"✅ 成功!")
                    print(f"📊 最高价: ${result.get('historical_highest_price', 0):.2f}")
                    print(f"📊 最低价: ${result.get('historical_lowest_price', 0):.2f}")
                    print(f"📊 当前价: ${result.get('current_price', 0):.2f}")
                    print(f"📊 来源: {result.get('source', '未知')}")
                    success_count += 1
                else:
                    print(f"❌ 失败")
                    if result:
                        print(f"   结果: {result}")
                
            except Exception as e:
                print(f"❌ 异常: {str(e)}")
                import traceback
                traceback.print_exc()
        
        # 总结
        success_rate = (success_count / len(test_asins) * 100) if test_asins else 0
        print(f"\n📊 总体成功率: {success_count}/{len(test_asins)} ({success_rate:.1f}%)")
        
        if success_rate > 0:
            print("\n🎉 主程序修改成功!")
            print("💡 图像API方法已成功集成")
            print("💡 可以替代原有的WebSocket方法")
        else:
            print("\n❌ 主程序修改失败")
            print("💡 需要进一步调试")
        
        # 测试主程序的get_keepa_info_with_browser方法
        print(f"\n🔍 测试主程序接口方法...")
        try:
            result = checker.get_keepa_info_with_browser(test_asins[0])
            if result:
                print(f"✅ 主程序接口测试成功")
                print(f"📊 结果: {result}")
            else:
                print(f"❌ 主程序接口测试失败")
        except Exception as e:
            print(f"❌ 主程序接口异常: {str(e)}")
        
    except Exception as e:
        print(f"❌ 程序初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        test_modified_main()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
