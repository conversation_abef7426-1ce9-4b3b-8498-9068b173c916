#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单个ASIN的处理，保存详细的调试信息
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_single_asin(asin="B07QNYTRG2"):
    """测试单个ASIN的处理"""
    try:
        from 历史价格8 import KeepaHistoricalPriceChecker
        
        print(f"🧪 开始测试ASIN: {asin}")
        print("=" * 50)
        
        # 创建检查器实例，启用详细模式
        checker = KeepaHistoricalPriceChecker(verbose=True, marketplace="US")
        
        print(f"✅ 创建检查器成功")
        print(f"📡 加载了 {len(checker.proxies)} 个代理")
        print(f"🌍 市场设置: {checker.marketplace}")
        
        # 测试单个ASIN
        print(f"\n🔍 开始处理ASIN: {asin}")
        result = checker.get_keepa_info_with_browser(asin, max_retries=1)
        
        print(f"\n📊 处理结果:")
        print(f"  历史最高价: {result.get('historical_highest_price', '未知')}")
        print(f"  历史最低价: {result.get('historical_lowest_price', '未知')}")
        print(f"  断货状态: {result.get('out_of_stock_within_month', '未知')}")
        
        # 显示统计信息
        checker.print_request_statistics()
        
        print(f"\n💾 调试文件已保存到当前目录")
        print(f"   - debug_{asin}_*.html (完整HTML响应)")
        print(f"   - analysis_{asin}_*.txt (分析报告)")
        print(f"   - structure_analysis_{asin}_*.txt (HTML结构分析)")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("🚀 单个ASIN测试工具")
    print("这个工具会测试单个ASIN并保存所有调试信息")
    print("=" * 60)
    
    # 测试几个不同的ASIN
    test_asins = [
        "B07QNYTRG2",  # 用户提到的第一个ASIN
        "B07SPTL99F",  # 用户提到的第二个ASIN
    ]
    
    results = {}
    
    for asin in test_asins:
        print(f"\n{'='*20} 测试 {asin} {'='*20}")
        result = test_single_asin(asin)
        results[asin] = result
        
        if result:
            has_price = (result.get('historical_highest_price') is not None or 
                        result.get('historical_lowest_price') is not None)
            print(f"✅ ASIN {asin}: {'有价格数据' if has_price else '无价格数据'}")
        else:
            print(f"❌ ASIN {asin}: 处理失败")
        
        print("-" * 60)
    
    # 总结
    print(f"\n📋 测试总结:")
    successful = sum(1 for r in results.values() if r is not None)
    with_price = sum(1 for r in results.values() 
                    if r and (r.get('historical_highest_price') is not None or 
                             r.get('historical_lowest_price') is not None))
    
    print(f"  测试ASIN数: {len(test_asins)}")
    print(f"  成功处理: {successful}")
    print(f"  有价格数据: {with_price}")
    
    if with_price == 0:
        print(f"\n⚠️  所有ASIN都没有价格数据，可能的原因:")
        print(f"  1. Keepa页面结构发生变化")
        print(f"  2. 需要登录或验证")
        print(f"  3. 代理被封禁")
        print(f"  4. URL构建错误")
        print(f"  5. 页面解析逻辑需要更新")
        print(f"\n💡 请检查保存的调试文件来分析具体原因")
    
    return results

if __name__ == "__main__":
    results = main()
    
    # 等待用户查看结果
    input("\n按回车键退出...")
