#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keepa 401验证破解器
完整的反爬虫绕过解决方案
"""

import sys
import os
import json
import time
import random
import asyncio
import base64
import hashlib
from urllib.parse import urlparse, parse_qs

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

class Keepa401Bypasser(KeepaHistoricalPriceChecker):
    """Keepa 401验证破解器 - 完整的反爬虫绕过解决方案"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.retry_count = 3
        self.delay_range = (5, 15)  # 增加延迟时间
        self.session_cookies = {}
        self.turnstile_token = None
        self.websocket_url = None
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
    
    async def bypass_cloudflare_turnstile(self, page):
        """绕过Cloudflare Turnstile验证"""
        try:
            # 等待Turnstile加载
            await asyncio.sleep(3)

            # 查找Turnstile iframe
            turnstile_frame = None
            frames = page.frames
            for frame in frames:
                if 'challenges.cloudflare.com' in frame.url:
                    turnstile_frame = frame
                    break

            if turnstile_frame:
                if self.verbose:
                    print("🔍 检测到Cloudflare Turnstile，尝试绕过...")

                # 模拟人类行为
                await page.mouse.move(random.randint(100, 800), random.randint(100, 600))
                await asyncio.sleep(random.uniform(1, 3))

                # 尝试点击验证框
                try:
                    checkbox = await turnstile_frame.wait_for_selector('input[type="checkbox"]', timeout=5000)
                    if checkbox:
                        await checkbox.click()
                        await asyncio.sleep(random.uniform(2, 5))
                except:
                    pass

                # 等待验证完成
                for i in range(10):
                    try:
                        # 检查是否有成功令牌
                        token_element = await page.query_selector('[name="cf-turnstile-response"]')
                        if token_element:
                            token = await token_element.get_attribute('value')
                            if token and len(token) > 10:
                                self.turnstile_token = token
                                if self.verbose:
                                    print("✅ Turnstile验证成功")
                                return True
                    except:
                        pass
                    await asyncio.sleep(1)

            return True  # 即使没有Turnstile也继续

        except Exception as e:
            if self.verbose:
                print(f"⚠️ Turnstile绕过异常: {str(e)}")
            return True  # 继续尝试

    async def get_keepa_websocket_data_with_bypass(self, asin):
        """带401绕过的WebSocket数据获取"""
        for attempt in range(self.retry_count):
            try:
                if self.verbose:
                    print(f"[尝试 {attempt + 1}/{self.retry_count}] 破解401验证获取 {asin} 的数据...")

                result = await self._advanced_websocket_attempt(asin, attempt)

                if result:
                    if self.verbose:
                        print(f"✅ {asin} 401绕过成功，数据获取完成")
                    return result
                else:
                    if attempt < self.retry_count - 1:
                        delay = random.uniform(*self.delay_range) * (attempt + 1)
                        if self.verbose:
                            print(f"⏳ {asin} 第{attempt + 1}次尝试失败，等待 {delay:.1f}s 后重试...")
                        await asyncio.sleep(delay)

            except Exception as e:
                if self.verbose:
                    print(f"❌ {asin} 第{attempt + 1}次尝试异常: {str(e)}")
                if attempt < self.retry_count - 1:
                    delay = random.uniform(*self.delay_range) * (attempt + 1)
                    await asyncio.sleep(delay)

        if self.verbose:
            print(f"❌ {asin} 所有401绕过尝试均失败")
        return None
    
    async def _advanced_websocket_attempt(self, asin, attempt):
        """高级WebSocket尝试 - 包含401绕过"""
        from playwright.async_api import async_playwright
        import gzip
        import json

        # 存储WebSocket消息
        websocket_messages = []
        websocket_connected = False
        
        async with async_playwright() as p:
            # 高级反检测浏览器配置
            browser_args = [
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',  # 加快加载速度
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-field-trial-config',
                '--disable-back-forward-cache',
                '--disable-ipc-flooding-protection',
                '--enable-features=NetworkService,NetworkServiceLogging',
                '--force-color-profile=srgb',
                '--metrics-recording-only',
                '--use-mock-keychain'
            ]
            
            # 代理配置
            proxy_config = None
            if hasattr(self, 'current_proxy') and self.current_proxy:
                proxy_parts = self.current_proxy.split(':')
                if len(proxy_parts) >= 4:
                    proxy_config = {
                        'server': f"socks5://{proxy_parts[1]}:{proxy_parts[2]}",
                        'username': proxy_parts[3] if len(proxy_parts) > 3 else None,
                        'password': proxy_parts[4] if len(proxy_parts) > 4 else None
                    }

            browser = await p.chromium.launch(
                headless=False,  # 使用有头模式，更难检测
                args=browser_args,
                slow_mo=random.randint(50, 200)  # 随机减慢操作速度
            )

            # 创建更真实的浏览器上下文
            context = await browser.new_context(
                user_agent=random.choice(self.user_agents),
                proxy=proxy_config,
                viewport={
                    'width': random.randint(1200, 1920),
                    'height': random.randint(800, 1080)
                },
                locale=random.choice(['en-US', 'en-GB', 'zh-CN']),
                timezone_id=random.choice(['America/New_York', 'Europe/London', 'Asia/Shanghai']),
                geolocation={'latitude': 40.7128, 'longitude': -74.0060},
                permissions=['geolocation'],
                color_scheme=random.choice(['light', 'dark']),
                reduced_motion=random.choice(['reduce', 'no-preference']),
                forced_colors=random.choice(['active', 'none']),
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Upgrade-Insecure-Requests': '1',
                    'DNT': '1'
                }
            )

            page = await context.new_page()

            # 注入反检测脚本
            await page.add_init_script("""
                // 移除webdriver属性
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                // 伪造Chrome运行时
                window.chrome = {
                    runtime: {},
                    loadTimes: function() {},
                    csi: function() {},
                    app: {}
                };

                // 伪造插件
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });

                // 伪造语言
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });

                // 移除自动化标识
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            """)

            # 高级WebSocket监听
            def on_websocket(websocket):
                nonlocal websocket_connected
                websocket_connected = True
                if self.verbose:
                    print(f"[{asin}] WebSocket连接建立: {websocket.url}")

                def on_frame_received(payload):
                    self.handle_advanced_websocket_message(payload, websocket_messages, asin)

                def on_close():
                    if self.verbose:
                        print(f"[{asin}] WebSocket连接关闭")

                websocket.on('framereceived', on_frame_received)
                websocket.on('close', on_close)

            page.on('websocket', on_websocket)

            try:
                # 第一步：访问主页建立会话
                if self.verbose:
                    print(f"[{asin}] 第一步：访问Keepa主页建立会话...")

                await page.goto('https://keepa.com/', wait_until='networkidle', timeout=30000)
                await asyncio.sleep(random.uniform(2, 4))

                # 绕过Cloudflare Turnstile
                await self.bypass_cloudflare_turnstile(page)

                # 模拟真实用户行为
                await self.simulate_human_behavior(page)

                # 第二步：访问产品页面
                keepa_url = f"https://keepa.com/#!product/1-{asin}"
                if self.verbose:
                    print(f"[{asin}] 第二步：访问产品页面: {keepa_url}")

                await page.goto(keepa_url, wait_until='networkidle', timeout=30000)

                # 等待页面完全加载
                await asyncio.sleep(random.uniform(3, 6))

                # 检查是否有401错误
                page_content = await page.content()
                if '401' in page_content or 'Unauthorized' in page_content:
                    if self.verbose:
                        print(f"[{asin}] 检测到401错误，尝试刷新...")
                    await page.reload(wait_until='networkidle')
                    await asyncio.sleep(random.uniform(2, 4))

                # 等待WebSocket连接和数据
                wait_time = 15 + attempt * 5  # 增加等待时间
                if self.verbose:
                    print(f"[{asin}] 等待WebSocket数据 {wait_time}s...")

                for i in range(wait_time):
                    await asyncio.sleep(1)
                    if len(websocket_messages) >= 4:  # 通常需要至少4条消息
                        break

                # 检查WebSocket连接状态
                if not websocket_connected:
                    if self.verbose:
                        print(f"[{asin}] WebSocket未连接，尝试触发连接...")
                    # 尝试滚动页面触发WebSocket
                    await page.mouse.wheel(0, random.randint(100, 500))
                    await asyncio.sleep(3)

                if self.verbose:
                    print(f"[{asin}] 收到 {len(websocket_messages)} 条WebSocket消息")

                # 解析WebSocket消息
                result = self.parse_websocket_messages_advanced(websocket_messages, asin)

                return result

            finally:
                await browser.close()
    
    async def simulate_human_behavior(self, page):
        """模拟真实用户行为"""
        try:
            # 随机鼠标移动
            for _ in range(random.randint(2, 5)):
                x = random.randint(100, 800)
                y = random.randint(100, 600)
                await page.mouse.move(x, y)
                await asyncio.sleep(random.uniform(0.5, 1.5))

            # 随机滚动
            for _ in range(random.randint(1, 3)):
                await page.mouse.wheel(0, random.randint(-200, 200))
                await asyncio.sleep(random.uniform(0.5, 1.0))

            # 随机点击空白区域
            await page.mouse.click(random.randint(100, 300), random.randint(100, 300))
            await asyncio.sleep(random.uniform(0.5, 1.0))

        except Exception as e:
            if self.verbose:
                print(f"模拟人类行为异常: {str(e)}")

    def handle_advanced_websocket_message(self, payload, message_list, asin):
        """高级WebSocket消息处理"""
        try:
            if isinstance(payload, bytes):
                try:
                    # 尝试多种解压缩方法
                    import gzip
                    import zlib

                    # 方法1: gzip解压
                    try:
                        decompressed = gzip.decompress(payload)
                        json_data = json.loads(decompressed.decode('utf-8'))
                        message_list.append(json_data)
                        if self.verbose:
                            print(f"[{asin}] 收到gzip压缩WebSocket消息: {len(str(json_data))} 字符")
                        return
                    except:
                        pass

                    # 方法2: zlib解压
                    try:
                        decompressed = zlib.decompress(payload)
                        json_data = json.loads(decompressed.decode('utf-8'))
                        message_list.append(json_data)
                        if self.verbose:
                            print(f"[{asin}] 收到zlib压缩WebSocket消息: {len(str(json_data))} 字符")
                        return
                    except:
                        pass

                    # 方法3: 直接解析
                    try:
                        json_data = json.loads(payload.decode('utf-8'))
                        message_list.append(json_data)
                        if self.verbose:
                            print(f"[{asin}] 收到未压缩WebSocket消息: {len(str(json_data))} 字符")
                        return
                    except:
                        pass

                    # 如果都失败，保存原始数据
                    if self.verbose:
                        print(f"[{asin}] 无法解析bytes WebSocket消息: {len(payload)} bytes")
                    message_list.append({'_raw_bytes': len(payload), '_error': 'parse_failed'})

                except Exception as e:
                    if self.verbose:
                        print(f"[{asin}] bytes消息处理异常: {str(e)}")

            elif isinstance(payload, str):
                try:
                    json_data = json.loads(payload)
                    message_list.append(json_data)
                    if self.verbose:
                        print(f"[{asin}] 收到字符串WebSocket消息: {len(payload)} 字符")
                except:
                    if self.verbose:
                        print(f"[{asin}] 无法解析字符串WebSocket消息: {len(payload)} 字符")
                    message_list.append({'_raw_string': payload[:100], '_error': 'parse_failed'})

        except Exception as e:
            if self.verbose:
                print(f"[{asin}] 处理WebSocket消息异常: {str(e)}")

    def handle_websocket_message_improved(self, payload, message_list, asin):
        """改进的WebSocket消息处理"""
        try:
            if isinstance(payload, bytes):
                try:
                    # 尝试解压缩
                    import gzip
                    decompressed = gzip.decompress(payload)
                    json_data = json.loads(decompressed.decode('utf-8'))
                    message_list.append(json_data)
                    if self.verbose:
                        print(f"[{asin}] 收到压缩WebSocket消息: {len(str(json_data))} 字符")
                except:
                    try:
                        # 直接解析
                        json_data = json.loads(payload.decode('utf-8'))
                        message_list.append(json_data)
                        if self.verbose:
                            print(f"[{asin}] 收到未压缩WebSocket消息: {len(str(json_data))} 字符")
                    except:
                        if self.verbose:
                            print(f"[{asin}] 无法解析bytes WebSocket消息: {len(payload)} bytes")
                        # 保存原始数据用于调试
                        message_list.append({'_raw_bytes': len(payload), '_error': 'parse_failed'})
            elif isinstance(payload, str):
                try:
                    json_data = json.loads(payload)
                    message_list.append(json_data)
                    if self.verbose:
                        print(f"[{asin}] 收到字符串WebSocket消息: {len(payload)} 字符")
                except:
                    if self.verbose:
                        print(f"[{asin}] 无法解析字符串WebSocket消息: {len(payload)} 字符")
                    message_list.append({'_raw_string': payload[:100], '_error': 'parse_failed'})
        except Exception as e:
            if self.verbose:
                print(f"[{asin}] 处理WebSocket消息异常: {str(e)}")
    
    def parse_websocket_messages_improved(self, messages, asin):
        """改进的WebSocket消息解析"""
        if self.verbose:
            print(f"[{asin}] 开始解析 {len(messages)} 条WebSocket消息")
        
        # 检查是否有错误状态
        for i, message in enumerate(messages):
            if isinstance(message, dict):
                # 检查状态码
                if message.get('status') == 401:
                    if self.verbose:
                        print(f"[{asin}] 检测到401未授权状态")
                    return None
                
                # 检查JSON状态
                json_str = message.get('json', '')
                if json_str and '"status":-2' in json_str:
                    if self.verbose:
                        print(f"[{asin}] 检测到状态-2（请求被拒绝）")
                    return None
                
                # 检查人机验证
                if message.get('checkHuman'):
                    if self.verbose:
                        print(f"[{asin}] 检测到人机验证要求")
                    return None
                
                # 查找产品数据
                if 'basicProducts' in message:
                    if self.verbose:
                        print(f"[{asin}] 找到basicProducts，包含 {len(message['basicProducts'])} 个产品")
                    products = message['basicProducts']

                    for j, product in enumerate(products):
                        product_asin = product.get('asin')
                        if self.verbose:
                            print(f"[{asin}] 产品 {j+1}: ASIN={product_asin}")
                        
                        if product_asin == asin:
                            if self.verbose:
                                print(f"[{asin}] 找到匹配的ASIN，开始解析产品数据")
                            result = self.parse_keepa_product_data(product)
                            if result:
                                if self.verbose:
                                    print(f"[{asin}] 产品数据解析成功")
                                return result
                            else:
                                if self.verbose:
                                    print(f"[{asin}] 产品数据解析失败")

        if self.verbose:
            print(f"[{asin}] 所有WebSocket消息解析完成，未找到有效数据")
        return None

    def parse_websocket_messages_advanced(self, messages, asin):
        """高级WebSocket消息解析 - 包含401检测和绕过"""
        if not messages:
            if self.verbose:
                print(f"[{asin}] 没有收到WebSocket消息")
            return None

        if self.verbose:
            print(f"[{asin}] 开始高级解析 {len(messages)} 条WebSocket消息")

        # 统计消息类型
        status_401_count = 0
        status_200_count = 0
        data_messages = []
        error_messages = []

        for i, message in enumerate(messages):
            if isinstance(message, dict):
                # 检查状态码
                status = message.get('status')
                if status == 401:
                    status_401_count += 1
                    error_messages.append(f"消息{i}: 401未授权")
                elif status == 200:
                    status_200_count += 1
                    data_messages.append(message)

                # 检查JSON状态
                json_str = message.get('json', '')
                if json_str:
                    if '"status":-2' in json_str:
                        error_messages.append(f"消息{i}: 状态-2（请求被拒绝）")
                    elif '"basicProducts"' in json_str:
                        data_messages.append(message)

                # 检查人机验证
                if message.get('checkHuman'):
                    error_messages.append(f"消息{i}: 人机验证要求")

        if self.verbose:
            print(f"[{asin}] 消息统计: 401错误={status_401_count}, 200成功={status_200_count}, 数据消息={len(data_messages)}")
            if error_messages:
                print(f"[{asin}] 错误详情: {'; '.join(error_messages)}")

        # 如果有数据消息，尝试解析
        if data_messages:
            for message in data_messages:
                try:
                    result = self.parse_websocket_messages_improved([message], asin)
                    if result:
                        if self.verbose:
                            print(f"[{asin}] ✅ 成功解析数据消息")
                        return result
                except Exception as e:
                    if self.verbose:
                        print(f"[{asin}] 解析数据消息异常: {str(e)}")

        # 如果主要是401错误，返回特殊标识
        if status_401_count > status_200_count:
            if self.verbose:
                print(f"[{asin}] ❌ 主要是401错误，绕过失败")
            return {'_bypass_failed': True, '_401_count': status_401_count}

        # 尝试使用改进方法解析所有消息
        try:
            result = self.parse_websocket_messages_improved(messages, asin)
            if result:
                if self.verbose:
                    print(f"[{asin}] ✅ 改进方法解析成功")
                return result
        except Exception as e:
            if self.verbose:
                print(f"[{asin}] 改进解析异常: {str(e)}")

        if self.verbose:
            print(f"[{asin}] ❌ 所有解析方法均失败")
        return None
    
    def reverse_engineer_keepa_improved(self, asin):
        """改进的Keepa逆向工程方法"""
        try:
            import concurrent.futures
            import asyncio

            def run_websocket_in_thread():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        return loop.run_until_complete(self.get_keepa_websocket_data_with_bypass(asin))
                    finally:
                        loop.close()
                except Exception as e:
                    if self.verbose:
                        print(f"[{asin}] WebSocket线程执行失败: {str(e)}")
                    return None

            return run_websocket_in_thread()

        except Exception as e:
            if self.verbose:
                print(f"[{asin}] 改进方法执行失败: {str(e)}")
            return None

def test_improved_method():
    """测试改进的方法"""
    print("🚀 测试改进的WebSocket处理方法...")
    print("=" * 60)
    
    # 创建401绕过分析器
    analyzer = Keepa401Bypasser(verbose=True)
    
    # 测试之前失败的ASIN
    test_asins = [
        "B07SPTL99F",  # 之前失败的ASIN
        "B07TXJKT46",  # 之前失败的ASIN
        "B07TZDH6G4",  # 之前成功的ASIN（对比）
    ]
    
    results = []
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n🔍 测试ASIN {i}/{len(test_asins)}: {asin}")
        print("-" * 40)
        
        try:
            result = analyzer.reverse_engineer_keepa_improved(asin)
            
            if result and analyzer.is_valid_websocket_result(result):
                highest = result.get('historical_highest_price', 0)
                lowest = result.get('historical_lowest_price', 0)
                current = result.get('current_price', 0)
                
                print(f"✅ 成功获取数据:")
                print(f"   标题: {result.get('title', 'Unknown')[:50]}...")
                print(f"   历史最高价: ${highest:.2f}")
                print(f"   历史最低价: ${lowest:.2f}")
                if current:
                    print(f"   当前价格: ${current:.2f}")
                
                results.append({'asin': asin, 'success': True, 'result': result})
            else:
                print(f"❌ 仍然返回未知")
                results.append({'asin': asin, 'success': False})
                
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
            results.append({'asin': asin, 'success': False, 'error': str(e)})
    
    # 分析结果
    print("\n" + "=" * 60)
    print("📊 改进方法测试结果:")
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"   ✅ 成功: {len(successful)}/{len(results)}")
    print(f"   ❌ 失败: {len(failed)}/{len(results)}")
    
    if successful:
        print(f"\n🎯 成功的ASIN:")
        for result in successful:
            print(f"   • {result['asin']}")
    
    if failed:
        print(f"\n❌ 仍然失败的ASIN:")
        for result in failed:
            print(f"   • {result['asin']}")
    
    return results

def main():
    """主函数"""
    print("🔧 改进WebSocket处理器测试...")
    results = test_improved_method()
    
    print("\n" + "=" * 60)
    print("📋 总结:")
    
    successful_count = len([r for r in results if r['success']])
    total_count = len(results)
    
    if successful_count > 0:
        print(f"✅ 改进方法有效 ({successful_count}/{total_count} 成功)")
        print("💡 建议将改进的方法集成到主程序中")
    else:
        print("❌ 改进方法需要进一步优化")
        print("💡 可能需要更复杂的反爬虫对策")

if __name__ == "__main__":
    main()
