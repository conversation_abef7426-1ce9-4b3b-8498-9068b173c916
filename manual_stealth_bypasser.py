#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动配置的隐蔽Keepa绕过器
使用标准Selenium + 手动反检测配置
"""

import time
import random
import json
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def create_stealth_chrome_driver():
    """创建隐蔽的Chrome驱动器"""
    print("🔧 创建隐蔽Chrome驱动器...")
    
    options = Options()
    
    # 基础反检测配置
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_argument('--disable-extensions')
    options.add_argument('--disable-plugins')
    options.add_argument('--disable-images')
    options.add_argument('--disable-javascript')  # 先禁用，后面启用
    
    # 窗口和显示设置
    options.add_argument('--window-size=1366,768')
    options.add_argument('--start-maximized')
    
    # 用户代理
    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    options.add_argument(f'--user-agent={user_agent}')
    
    # 语言设置
    options.add_argument('--lang=en-US')
    
    # 禁用自动化标识
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    # 设置首选项
    prefs = {
        "profile.default_content_setting_values": {
            "images": 2,  # 禁用图片
            "plugins": 2,  # 禁用插件
            "popups": 2,   # 禁用弹窗
            "geolocation": 2,  # 禁用地理位置
            "notifications": 2,  # 禁用通知
            "media_stream": 2,  # 禁用媒体流
        },
        "profile.managed_default_content_settings": {
            "images": 2
        }
    }
    options.add_experimental_option("prefs", prefs)
    
    try:
        # 尝试使用系统Chrome
        driver = webdriver.Chrome(options=options)
        
        # 设置超时
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(10)
        
        # 注入反检测脚本
        inject_stealth_scripts(driver)
        
        print("✅ Chrome驱动器创建成功")
        return driver
        
    except Exception as e:
        print(f"❌ 创建驱动器失败: {str(e)}")
        print("💡 请确保Chrome浏览器已安装并且ChromeDriver在PATH中")
        return None

def inject_stealth_scripts(driver):
    """注入反检测脚本"""
    print("🛡️ 注入反检测脚本...")
    
    stealth_scripts = [
        # 移除webdriver属性
        "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})",
        
        # 修改plugins
        """
        Object.defineProperty(navigator, 'plugins', {
            get: () => [
                {name: 'Chrome PDF Plugin', description: 'Portable Document Format'},
                {name: 'Chrome PDF Viewer', description: 'PDF Viewer'},
                {name: 'Native Client', description: 'Native Client'}
            ]
        });
        """,
        
        # 修改语言
        "Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})",
        
        # 修改平台
        "Object.defineProperty(navigator, 'platform', {get: () => 'Win32'})",
        
        # 修改权限查询
        """
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
            Promise.resolve({ state: Notification.permission }) :
            originalQuery(parameters)
        );
        """,
        
        # 启用JavaScript（之前被禁用）
        "// JavaScript now enabled"
    ]
    
    for script in stealth_scripts:
        try:
            driver.execute_script(script)
        except Exception as e:
            print(f"⚠️ 脚本注入警告: {str(e)}")

def setup_websocket_listener(driver):
    """设置WebSocket监听器"""
    print("📡 设置WebSocket监听器...")
    
    websocket_script = """
    window.keepaMessages = [];
    window.keepaConnections = [];
    
    // 拦截WebSocket构造函数
    const originalWebSocket = window.WebSocket;
    window.WebSocket = function(url, protocols) {
        console.log('WebSocket连接:', url);
        const ws = new originalWebSocket(url, protocols);
        
        window.keepaConnections.push({
            url: url,
            readyState: ws.readyState,
            timestamp: Date.now()
        });
        
        ws.addEventListener('open', function(event) {
            console.log('WebSocket已连接:', url);
        });
        
        ws.addEventListener('message', function(event) {
            try {
                console.log('收到WebSocket消息:', event.data.length, 'bytes');
                window.keepaMessages.push({
                    data: event.data,
                    timestamp: Date.now(),
                    url: url
                });
            } catch (e) {
                console.log('WebSocket消息捕获错误:', e);
            }
        });
        
        ws.addEventListener('error', function(event) {
            console.log('WebSocket错误:', event);
        });
        
        ws.addEventListener('close', function(event) {
            console.log('WebSocket已关闭:', event.code, event.reason);
        });
        
        return ws;
    };
    
    // 复制原始WebSocket的属性
    Object.setPrototypeOf(window.WebSocket, originalWebSocket);
    window.WebSocket.prototype = originalWebSocket.prototype;
    """
    
    try:
        driver.execute_script(websocket_script)
        print("✅ WebSocket监听器设置成功")
        return True
    except Exception as e:
        print(f"❌ 设置WebSocket监听器失败: {str(e)}")
        return False

def simulate_human_behavior(driver):
    """模拟人类行为"""
    print("🤖 模拟人类行为...")
    
    try:
        # 随机鼠标移动和点击
        driver.execute_script("""
            function simulateHumanBehavior() {
                // 随机鼠标移动
                for(let i = 0; i < 3; i++) {
                    setTimeout(() => {
                        const event = new MouseEvent('mousemove', {
                            clientX: Math.random() * window.innerWidth,
                            clientY: Math.random() * window.innerHeight,
                            bubbles: true
                        });
                        document.dispatchEvent(event);
                    }, i * 200);
                }
                
                // 随机滚动
                setTimeout(() => {
                    window.scrollBy(0, Math.random() * 300);
                }, 600);
            }
            
            simulateHumanBehavior();
        """)
        
        time.sleep(random.uniform(1, 2))
        print("✅ 人类行为模拟完成")
        
    except Exception as e:
        print(f"⚠️ 行为模拟异常: {str(e)}")

def test_keepa_bypass(asin):
    """测试Keepa绕过"""
    print(f"\n🎯 测试ASIN: {asin}")
    print("-" * 50)
    
    driver = create_stealth_chrome_driver()
    if not driver:
        return None
    
    try:
        # 设置WebSocket监听
        if not setup_websocket_listener(driver):
            return None
        
        # 预热：访问主页
        print("🔥 预热会话 - 访问Keepa主页...")
        driver.get("https://keepa.com")
        time.sleep(3)
        
        # 检查页面是否正常加载
        page_title = driver.title
        print(f"📄 页面标题: {page_title}")
        
        if "keepa" not in page_title.lower():
            print("⚠️ 页面可能被阻止")
        
        # 模拟行为
        simulate_human_behavior(driver)
        
        # 访问产品页面
        product_url = f"https://keepa.com/#!product/1-{asin}"
        print(f"🛒 访问产品页面...")
        
        driver.get(product_url)
        time.sleep(8)  # 等待页面和WebSocket数据加载
        
        # 再次模拟行为
        simulate_human_behavior(driver)
        time.sleep(3)
        
        # 检查WebSocket连接
        connections = driver.execute_script("return window.keepaConnections || [];")
        print(f"🔗 WebSocket连接数: {len(connections)}")
        
        for conn in connections:
            print(f"   📡 {conn['url']}")
        
        # 收集WebSocket消息
        print("📨 收集WebSocket消息...")
        messages = driver.execute_script("return window.keepaMessages || [];")
        
        print(f"📊 收集到 {len(messages)} 条消息")
        
        # 分析消息
        success_messages = 0
        error_messages = 0
        
        for i, msg in enumerate(messages):
            try:
                data = msg['data']
                if isinstance(data, str):
                    if 'status' in data:
                        if '"status":200' in data or '"status": 200' in data:
                            success_messages += 1
                        elif '"status":401' in data or '"status": 401' in data:
                            error_messages += 1
                    
                    if 'basicProducts' in data:
                        print(f"✅ 找到产品数据消息 #{i+1}")
                        
                        # 尝试解析
                        try:
                            json_data = json.loads(data)
                            if 'basicProducts' in json_data:
                                products = json_data['basicProducts']
                                for product in products:
                                    if product.get('asin') == asin:
                                        title = product.get('title', '未知')
                                        csv_data = product.get('csv', [])
                                        
                                        if csv_data and len(csv_data) >= 2:
                                            # 提取价格
                                            prices = []
                                            for j in range(0, len(csv_data), 2):
                                                if j + 1 < len(csv_data):
                                                    price = csv_data[j + 1]
                                                    if 100 <= price <= 200000:  # $1-$2000
                                                        prices.append(price)
                                            
                                            if prices:
                                                highest = max(prices) / 100.0
                                                lowest = min(prices) / 100.0
                                                
                                                print(f"✅ 成功解析 {asin}:")
                                                print(f"   📝 标题: {title[:50]}...")
                                                print(f"   📊 最高价: ${highest:.2f}")
                                                print(f"   📊 最低价: ${lowest:.2f}")
                                                print(f"   📈 价格数据点: {len(prices)}")
                                                
                                                return {
                                                    'asin': asin,
                                                    'title': title,
                                                    'highest_price': highest,
                                                    'lowest_price': lowest,
                                                    'data_points': len(prices),
                                                    'success': True
                                                }
                        except json.JSONDecodeError:
                            continue
                            
            except Exception as e:
                print(f"⚠️ 解析消息 #{i+1} 异常: {str(e)}")
                continue
        
        print(f"📊 消息分析: {success_messages} 成功, {error_messages} 错误")
        
        if error_messages > 0:
            print("❌ 检测到401错误，需要更强的绕过技术")
        
        print(f"❌ 未能解析 {asin} 的有效数据")
        return {'asin': asin, 'success': False, 'error': '未找到有效数据'}
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return {'asin': asin, 'success': False, 'error': str(e)}
        
    finally:
        try:
            driver.quit()
        except:
            pass

def main():
    """主测试函数"""
    print("🚀 手动隐蔽Keepa绕过测试")
    print("🛠️ 使用标准Selenium + 手动反检测")
    print("=" * 60)
    
    # 测试ASIN
    test_asins = [
        "B07TZDH6G4",  # 已知有数据的ASIN
    ]
    
    results = []
    success_count = 0
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n[{i}/{len(test_asins)}] 开始测试...")
        
        start_time = time.time()
        result = test_keepa_bypass(asin)
        elapsed_time = time.time() - start_time
        
        if result and result.get('success'):
            success_count += 1
            print(f"⏱️ 处理时间: {elapsed_time:.1f}s")
        else:
            error = result.get('error', '未知错误') if result else '测试失败'
            print(f"❌ 失败: {error}")
            print(f"⏱️ 处理时间: {elapsed_time:.1f}s")
        
        results.append(result)
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    
    for result in results:
        if result and result.get('success'):
            print(f"✅ {result['asin']}: 成功")
        else:
            asin = result['asin'] if result else '未知'
            error = result.get('error', '未知') if result else '测试失败'
            print(f"❌ {asin}: {error}")
    
    success_rate = (success_count / len(test_asins) * 100) if test_asins else 0
    print(f"\n📊 成功率: {success_count}/{len(test_asins)} ({success_rate:.1f}%)")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
