#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分类系统
验证黄金购买框价格提取和分类保存功能
"""

import sys
import time
import os
import json
from 历史价格8 import KeepaHistoricalPriceChecker

def test_classification_system():
    """测试分类系统功能"""
    print("🚀 Keepa分类系统测试")
    print("🎯 验证黄金购买框价格提取和分类保存")
    print("📊 按价格变动时间和缺货状态分类")
    print("=" * 60)
    
    # 创建价格检查器实例
    checker = KeepaHistoricalPriceChecker(verbose=True)
    
    # 测试ASIN列表
    test_asins = [
        "B07TZDH6G4",  # 我们知道有数据的ASIN
        "B07SPTL99F",  # 另一个测试ASIN
        "B07FZ8S74R",  # 第三个测试ASIN
    ]
    
    print(f"\n🔍 测试 {len(test_asins)} 个ASIN的分类功能")
    print("-" * 40)
    
    results = []
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n[{i}/{len(test_asins)}] 处理 ASIN: {asin}")
        print("-" * 30)
        
        start_time = time.time()
        
        # 获取价格数据（包含分类）
        result = checker.get_keepa_image_api_data_optimized(asin)
        
        elapsed_time = time.time() - start_time
        
        if result and result.get('success'):
            print(f"✅ 成功! (耗时: {elapsed_time:.1f}s)")
            
            # 显示基本信息
            print(f"📊 最高价: ${result.get('historical_highest_price', 0):.2f}")
            print(f"📊 当前价: ${result.get('current_price', 0):.2f}")
            print(f"📊 来源: {result.get('source', 'unknown')}")
            
            # 显示分类信息
            if 'category' in result:
                print(f"🏷️ 分类: {result['category']}")
            
            # 显示价格变动信息
            if 'last_price_change' in result and result['last_price_change']:
                change_info = result['last_price_change']
                print(f"📅 价格变动: {change_info.get('value', 0)} {change_info.get('type', 'unknown')}前")
                print(f"📅 天数: {change_info.get('days_ago', 0):.1f} 天前")
            
            # 显示黄金购买框信息
            if 'buybox_data' in result and result['buybox_data']:
                buybox = result['buybox_data']
                print(f"🏆 黄金购买框最高价: ${buybox.get('highest_price', 0):.2f}")
                print(f"🏆 缺货状态: {'是' if buybox.get('out_of_stock') else '否'}")
            
            results.append(result)
        else:
            print(f"❌ 失败 (耗时: {elapsed_time:.1f}s)")
        
        # 避免请求过快
        if i < len(test_asins):
            print("⏳ 等待 3 秒...")
            time.sleep(3)
    
    # 显示分类统计
    print(f"\n" + "=" * 60)
    print("📊 分类统计结果")
    print("-" * 60)
    
    show_classification_summary()
    
    return results

def show_classification_summary():
    """显示分类汇总"""
    try:
        categories_dir = "keepa_categories"
        
        if not os.path.exists(categories_dir):
            print("❌ 分类目录不存在")
            return
        
        category_files = [f for f in os.listdir(categories_dir) if f.endswith('.json')]
        
        if not category_files:
            print("❌ 没有找到分类文件")
            return
        
        print(f"📁 找到 {len(category_files)} 个分类文件:")
        
        total_products = 0
        
        for category_file in sorted(category_files):
            category_name = category_file.replace('.json', '')
            file_path = os.path.join(categories_dir, category_file)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                count = len(data)
                total_products += count
                
                print(f"\n🏷️ {category_name}: {count} 个产品")
                
                # 显示该分类中的产品详情
                for item in data:
                    asin = item.get('asin', 'unknown')
                    price = item.get('buybox_highest_price', 0)
                    out_of_stock = item.get('out_of_stock', False)
                    timestamp = item.get('timestamp', 'unknown')
                    
                    stock_status = "缺货" if out_of_stock else "有货"
                    print(f"   • {asin}: ${price:.2f} ({stock_status}) - {timestamp[:19]}")
                    
            except Exception as e:
                print(f"   ❌ 读取 {category_file} 失败: {str(e)}")
        
        print(f"\n📊 总计: {total_products} 个产品已分类")
        
    except Exception as e:
        print(f"❌ 分类汇总失败: {str(e)}")

def test_category_structure():
    """测试分类结构"""
    print(f"\n" + "=" * 60)
    print("🏗️ 分类结构测试")
    print("-" * 60)
    
    # 预期的分类
    expected_categories = [
        "15天内_有货",
        "15天内_缺货", 
        "15天外_有货",
        "15天外_缺货",
        "未知时间_有货",
        "未知时间_缺货"
    ]
    
    print("📋 预期分类结构:")
    for category in expected_categories:
        print(f"   • {category}")
    
    print(f"\n💡 分类说明:")
    print(f"   🕐 15天内/15天外: 基于'上次价格变动'时间")
    print(f"   📦 有货/缺货: 基于当前库存状态")
    print(f"   💾 每个分类保存为独立的JSON文件")
    print(f"   🔄 相同ASIN会更新而不是重复添加")

if __name__ == "__main__":
    try:
        test_classification_system()
        test_category_structure()
    except KeyboardInterrupt:
        print("\n⏹️ 测试中断")
    except Exception as e:
        print(f"\n❌ 异常: {str(e)}")
        import traceback
        traceback.print_exc()
