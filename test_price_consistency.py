#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试价格一致性
验证所有ASIN都使用黄金购买框价格，而不是估算的历史价格
"""

import sys
import time
from 历史价格8 import KeepaHistoricalPriceChecker

def test_price_consistency():
    """测试价格一致性"""
    print("🚀 价格一致性测试")
    print("🎯 验证所有ASIN都使用黄金购买框价格")
    print("💡 目标：消除100多美金的估算价格，只显示真实价格")
    print("=" * 60)
    
    # 创建价格检查器实例
    checker = KeepaHistoricalPriceChecker(verbose=True)
    
    # 测试ASIN列表（包括之前显示不同价格格式的ASIN）
    test_asins = [
        "B009ONDIHG",  # 之前显示黄金购买框价格 $29.99
        "B0BLV9CPYL",  # 之前显示历史最高价 $157.63
        "B06XG2DGYV",  # 之前显示历史最高价 $179.42
        "B00005Q5GM",  # 之前显示历史最高价 $169.29
    ]
    
    print(f"\n🔍 测试 {len(test_asins)} 个ASIN的价格一致性")
    print("-" * 40)
    
    results = []
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n[{i}/{len(test_asins)}] 测试ASIN: {asin}")
        print("-" * 30)
        
        start_time = time.time()
        
        # 调用主程序的处理方法
        result = checker.get_keepa_info_with_browser(asin, max_retries=1)
        
        elapsed_time = time.time() - start_time
        
        if result and result.get('historical_highest_price'):
            print(f"✅ 成功获取价格 (耗时: {elapsed_time:.1f}s)")
            
            # 分析价格范围
            highest = result.get('historical_highest_price', 0)
            lowest = result.get('historical_lowest_price', 0)
            
            print(f"📊 价格范围: ${lowest:.2f} - ${highest:.2f}")
            
            # 判断价格类型
            if highest > 100:
                print(f"⚠️ 高价格警告: ${highest:.2f} (可能是估算价格)")
                price_type = "估算价格"
            else:
                print(f"✅ 合理价格: ${highest:.2f} (可能是真实价格)")
                price_type = "真实价格"
            
            results.append({
                'asin': asin,
                'highest_price': highest,
                'lowest_price': lowest,
                'price_type': price_type,
                'elapsed_time': elapsed_time
            })
        else:
            print(f"❌ 获取价格失败 (耗时: {elapsed_time:.1f}s)")
        
        # 避免请求过快
        if i < len(test_asins):
            print("⏳ 等待 3 秒...")
            time.sleep(3)
    
    # 分析结果
    print(f"\n" + "=" * 60)
    print("📊 价格一致性分析")
    print("-" * 60)
    
    if results:
        real_price_count = sum(1 for r in results if r['price_type'] == '真实价格')
        estimated_price_count = sum(1 for r in results if r['price_type'] == '估算价格')
        
        print(f"✅ 真实价格: {real_price_count} 个ASIN")
        print(f"⚠️ 估算价格: {estimated_price_count} 个ASIN")
        
        if estimated_price_count == 0:
            print(f"\n🎉 完美！所有ASIN都使用了真实价格！")
        else:
            print(f"\n⚠️ 仍有 {estimated_price_count} 个ASIN使用估算价格")
            print(f"💡 这些ASIN可能需要进一步优化")
        
        # 显示详细结果
        print(f"\n📋 详细结果:")
        for result in results:
            asin = result['asin']
            highest = result['highest_price']
            price_type = result['price_type']
            elapsed_time = result['elapsed_time']
            
            status_icon = "✅" if price_type == "真实价格" else "⚠️"
            print(f"   {status_icon} {asin}: ${highest:.2f} ({price_type}) - {elapsed_time:.1f}s")
    else:
        print(f"❌ 没有成功获取任何价格数据")

def explain_price_difference():
    """解释价格差异"""
    print(f"\n" + "=" * 60)
    print("💡 价格差异说明")
    print("-" * 60)
    
    print("🏆 黄金购买框价格 (真实价格):")
    print("   • 来源: Keepa浏览器真实数据")
    print("   • 含义: 当前Amazon实际售价")
    print("   • 范围: 通常几十美金")
    print("   • 准确性: 高")
    
    print("\n📊 历史最高价 (估算价格):")
    print("   • 来源: HTML解析或图像分析")
    print("   • 含义: 历史上的最高价格")
    print("   • 范围: 可能100多美金")
    print("   • 准确性: 较低，可能包含异常高价")
    
    print("\n🎯 为什么历史最高价更高？")
    print("   • 促销前的原价")
    print("   • 缺货时的高价")
    print("   • 第三方卖家定价")
    print("   • 算法估算误差")
    
    print("\n✅ 解决方案:")
    print("   • 优先使用黄金购买框价格")
    print("   • 避免使用估算的历史价格")
    print("   • 提供真实的市场价格")

if __name__ == "__main__":
    try:
        test_price_consistency()
        explain_price_difference()
    except KeyboardInterrupt:
        print("\n⏹️ 测试中断")
    except Exception as e:
        print(f"\n❌ 异常: {str(e)}")
        import traceback
        traceback.print_exc()
