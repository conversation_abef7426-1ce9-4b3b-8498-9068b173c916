#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序流程
"""

import sys
import os
import requests

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalP<PERSON><PERSON>he<PERSON>

def test_main_flow():
    """测试主程序流程"""
    print("🔍 测试主程序流程...")
    
    # 创建分析器实例
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    
    # 使用失败的ASIN
    test_asin = "B07ZNW4TLG"
    
    print(f"📱 测试ASIN: {test_asin}")
    print("=" * 50)
    
    try:
        # 模拟主程序的HTTP请求
        url = f"https://keepa.com/#!product/1-{test_asin}"
        
        print(f"🌐 请求URL: {url}")

        # 发送HTTP请求（不使用代理，简化测试）
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"📊 响应状态: {response.status_code}")
        print(f"📏 响应长度: {len(response.text)} 字符")
        
        # 检测是否是JavaScript应用
        is_js_app = analyzer.detect_javascript_app(response.text)
        
        if is_js_app:
            print("✅ 识别为JavaScript应用，应该调用WebSocket方法")
            
            # 调用WebSocket方法
            result = analyzer.reverse_engineer_keepa(test_asin)
            
            if result:
                print("✅ WebSocket方法成功")
                return True
            else:
                print("❌ WebSocket方法失败，会调用传统方法")
                
                # 这时会调用try_extract_from_js_app
                js_data = analyzer.try_extract_from_js_app(response.text, test_asin)
                
                if js_data:
                    print("✅ 传统方法成功")
                else:
                    print("❌ 传统方法也失败")
        else:
            print("❌ 未识别为JavaScript应用，不会调用WebSocket方法")
            print("💡 这可能是主程序问题的原因")
        
        return False
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 主程序流程测试")
    print("=" * 60)
    
    success = test_main_flow()
    
    print("\n" + "=" * 60)
    print(f"📋 测试结果: {'✅ 找到问题' if not success else '❓ 需要进一步调试'}")

if __name__ == "__main__":
    main()
