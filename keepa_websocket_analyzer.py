#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keepa WebSocket分析器
专门监听WebSocket连接和数据传输
"""

import asyncio
import json
import time
import base64
from playwright.async_api import async_playwright

class KeepaWebSocketAnalyzer:
    def __init__(self):
        self.websocket_messages = []
        self.ajax_requests = []
        self.price_data = []
        
    async def analyze_keepa_websocket(self, asin):
        """分析Keepa的WebSocket通信"""
        print(f"🔍 开始分析ASIN: {asin} 的WebSocket通信")
        print("=" * 60)
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(
                headless=False,
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080}
            )
            
            page = await context.new_page()
            
            # 监听WebSocket连接
            page.on('websocket', self.on_websocket)
            
            # 监听网络请求（专注于AJAX）
            page.on('request', self.on_ajax_request)
            page.on('response', self.on_ajax_response)
            
            try:
                # 访问Keepa产品页面
                keepa_url = f"https://keepa.com/#!product/1-{asin}"
                print(f"📱 访问页面: {keepa_url}")
                
                await page.goto(keepa_url, wait_until='networkidle', timeout=30000)
                
                # 等待页面完全加载
                print("⏳ 等待页面和WebSocket连接...")
                await asyncio.sleep(8)
                
                # 尝试获取页面中的实际数据
                page_data = await page.evaluate("""
                    () => {
                        const result = {
                            url: window.location.href,
                            hasKeepaApp: !!window.keepaApp,
                            hasConnection: !!window.connection,
                            connectionState: window.connection ? window.connection.readyState : null,
                            priceElements: [],
                            globalVars: {}
                        };
                        
                        // 查找价格元素
                        const priceSelectors = [
                            '[class*="price"]',
                            '[class*="Price"]', 
                            '.graph',
                            '.chart',
                            '[data-price]',
                            '.productTableDescriptionPriceAlt'
                        ];
                        
                        priceSelectors.forEach(selector => {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                if (el.textContent && (el.textContent.includes('$') || el.textContent.includes('€'))) {
                                    result.priceElements.push({
                                        selector: selector,
                                        className: el.className,
                                        text: el.textContent.trim().substring(0, 200)
                                    });
                                }
                            });
                        });
                        
                        // 检查全局变量
                        ['keepaApp', 'productData', 'priceHistory', 'connection', 'preInitWS'].forEach(varName => {
                            if (window[varName]) {
                                result.globalVars[varName] = typeof window[varName];
                            }
                        });
                        
                        return result;
                    }
                """)
                
                print(f"📊 页面数据分析:")
                print(f"   URL: {page_data['url']}")
                print(f"   Keepa App: {page_data['hasKeepaApp']}")
                print(f"   WebSocket连接: {page_data['hasConnection']}")
                print(f"   连接状态: {page_data['connectionState']}")
                print(f"   价格元素数量: {len(page_data['priceElements'])}")
                print(f"   全局变量: {page_data['globalVars']}")
                
                if page_data['priceElements']:
                    print(f"\n💰 发现的价格元素:")
                    for i, element in enumerate(page_data['priceElements'][:3], 1):
                        print(f"   {i}. {element['className']}: {element['text'][:100]}")
                
                # 等待更多WebSocket消息
                print("\n⏳ 等待WebSocket消息...")
                await asyncio.sleep(10)
                
                # 分析收集的数据
                await self.analyze_collected_data(asin)
                
            except Exception as e:
                print(f"❌ 分析失败: {str(e)}")
            
            finally:
                await browser.close()
    
    async def on_websocket(self, websocket):
        """WebSocket连接监听器"""
        print(f"🔌 WebSocket连接: {websocket.url}")
        
        # 监听WebSocket消息
        websocket.on('framereceived', lambda payload: self.on_websocket_message(payload, 'received'))
        websocket.on('framesent', lambda payload: self.on_websocket_message(payload, 'sent'))
        websocket.on('close', lambda: print("🔌 WebSocket连接关闭"))
    
    def on_websocket_message(self, payload, direction):
        """WebSocket消息处理"""
        try:
            message_data = {
                'direction': direction,
                'timestamp': time.time(),
                'payload': payload,
                'size': len(payload) if payload else 0
            }
            
            # 尝试解析消息内容
            if isinstance(payload, str):
                message_data['type'] = 'text'
                message_data['content'] = payload
                print(f"📨 WebSocket {direction}: {payload[:200]}...")
            elif isinstance(payload, bytes):
                message_data['type'] = 'binary'
                message_data['content'] = base64.b64encode(payload).decode('utf-8')
                print(f"📨 WebSocket {direction}: Binary data ({len(payload)} bytes)")
                
                # 尝试解析二进制数据
                try:
                    # 可能是压缩的JSON数据
                    import gzip
                    decompressed = gzip.decompress(payload)
                    json_data = json.loads(decompressed.decode('utf-8'))
                    message_data['parsed_content'] = json_data
                    print(f"   解析为JSON: {str(json_data)[:200]}...")
                except:
                    try:
                        # 尝试直接解析为JSON
                        json_data = json.loads(payload.decode('utf-8'))
                        message_data['parsed_content'] = json_data
                        print(f"   解析为JSON: {str(json_data)[:200]}...")
                    except:
                        print(f"   无法解析二进制数据")
            
            self.websocket_messages.append(message_data)
            
            # 检查是否包含价格数据
            if self.contains_price_info(message_data):
                self.price_data.append(message_data)
                print(f"💰 发现价格数据在WebSocket消息中!")
                
        except Exception as e:
            print(f"⚠️  WebSocket消息处理失败: {str(e)}")
    
    async def on_ajax_request(self, request):
        """AJAX请求监听器"""
        url = request.url
        if self.is_ajax_request(url):
            print(f"📤 AJAX请求: {request.method} {url}")
            self.ajax_requests.append({
                'url': url,
                'method': request.method,
                'headers': await request.all_headers(),
                'post_data': request.post_data,
                'timestamp': time.time(),
                'type': 'request'
            })
    
    async def on_ajax_response(self, response):
        """AJAX响应监听器"""
        url = response.url
        if self.is_ajax_request(url):
            try:
                content = await response.text()
                response_data = {
                    'url': url,
                    'status': response.status,
                    'headers': await response.all_headers(),
                    'content': content,
                    'timestamp': time.time(),
                    'type': 'response'
                }
                
                self.ajax_requests.append(response_data)
                print(f"📥 AJAX响应: {response.status} {url} ({len(content)} chars)")
                
                # 检查是否包含价格数据
                if self.contains_price_info({'content': content}):
                    self.price_data.append(response_data)
                    print(f"💰 发现价格数据在AJAX响应中!")
                    
            except Exception as e:
                print(f"⚠️  AJAX响应处理失败: {str(e)}")
    
    def is_ajax_request(self, url):
        """判断是否是AJAX请求"""
        ajax_patterns = [
            'api',
            'query',
            'data',
            'ajax',
            'json',
            'product',
            'price',
            'history'
        ]
        
        url_lower = url.lower()
        return any(pattern in url_lower for pattern in ajax_patterns)
    
    def contains_price_info(self, data):
        """检查数据是否包含价格信息"""
        content = ""
        if 'content' in data:
            content = str(data['content']).lower()
        elif 'parsed_content' in data:
            content = str(data['parsed_content']).lower()
        elif 'payload' in data:
            content = str(data['payload']).lower()
        
        price_indicators = [
            'price',
            'cost',
            'amount',
            '$',
            '€',
            'usd',
            'eur',
            'highest',
            'lowest',
            'current',
            'history'
        ]
        
        return any(indicator in content for indicator in price_indicators)
    
    async def analyze_collected_data(self, asin):
        """分析收集的数据"""
        print(f"\n📊 数据收集结果:")
        print(f"   WebSocket消息: {len(self.websocket_messages)}")
        print(f"   AJAX请求: {len(self.ajax_requests)}")
        print(f"   包含价格数据: {len(self.price_data)}")
        
        # 保存详细数据
        if self.websocket_messages:
            filename = f"websocket_messages_{asin}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.websocket_messages, f, indent=2, ensure_ascii=False, default=str)
            print(f"WebSocket消息已保存到: {filename}")
        
        if self.ajax_requests:
            filename = f"ajax_requests_{asin}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.ajax_requests, f, indent=2, ensure_ascii=False, default=str)
            print(f"AJAX请求已保存到: {filename}")
        
        if self.price_data:
            filename = f"price_data_{asin}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.price_data, f, indent=2, ensure_ascii=False, default=str)
            print(f"价格数据已保存到: {filename}")
            
            print(f"\n💰 价格数据摘要:")
            for i, data in enumerate(self.price_data[:3], 1):
                print(f"   {i}. 类型: {data.get('type', 'unknown')}")
                if 'url' in data:
                    print(f"      URL: {data['url']}")
                if 'parsed_content' in data:
                    print(f"      内容: {str(data['parsed_content'])[:200]}...")

async def main():
    """主函数"""
    # 测试ASIN
    test_asin = "B07QNYTRG2"
    
    analyzer = KeepaWebSocketAnalyzer()
    await analyzer.analyze_keepa_websocket(test_asin)

if __name__ == "__main__":
    asyncio.run(main())
