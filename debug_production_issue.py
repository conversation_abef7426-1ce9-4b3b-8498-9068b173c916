#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试生产环境问题
"""

import sys
import os
import threading
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def test_production_scenario():
    """模拟生产环境场景"""
    print("🔍 模拟生产环境测试...")
    
    # 创建分析器实例，关闭详细输出（模拟生产环境）
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    
    # 使用用户输出中失败的ASIN
    test_asin = "B07ZNW4TLG"
    
    print(f"📱 测试ASIN: {test_asin}")
    print("=" * 50)
    
    # 添加调试输出来跟踪方法调用
    original_reverse_engineer = analyzer.reverse_engineer_keepa
    
    def debug_reverse_engineer(asin):
        print(f"🔍 [调试] reverse_engineer_keepa被调用: {asin}")
        try:
            result = original_reverse_engineer(asin)
            if result:
                print(f"✅ [调试] reverse_engineer_keepa成功: {result.get('source', 'unknown')}")
            else:
                print(f"❌ [调试] reverse_engineer_keepa失败")
            return result
        except Exception as e:
            print(f"❌ [调试] reverse_engineer_keepa异常: {str(e)}")
            raise
    
    # 替换方法以添加调试
    analyzer.reverse_engineer_keepa = debug_reverse_engineer
    
    try:
        # 直接调用reverse_engineer_keepa方法
        print("🔍 直接调用reverse_engineer_keepa...")
        result = analyzer.reverse_engineer_keepa(test_asin)
        
        if result:
            print("✅ 直接调用成功")
            return True
        else:
            print("❌ 直接调用失败")
            
        print("\n" + "=" * 50)
        
        # 现在模拟完整的处理流程
        print("🔍 模拟完整处理流程...")
        
        # 创建一个模拟的HTTP响应
        class MockResponse:
            def __init__(self):
                self.text = """
                <!DOCTYPE html>
                <html>
                <head><title>Keepa</title></head>
                <body>
                    <script src="keepa.js"></script>
                    <script src="libs.js"></script>
                    <div id="app"></div>
                    <script>
                        window.keepaData = {};
                    </script>
                </body>
                </html>
                """
                self.status_code = 200
        
        mock_response = MockResponse()
        
        # 检查是否会被识别为JavaScript应用
        is_js_app = analyzer.detect_javascript_app(mock_response.text)
        print(f"🔍 是否识别为JS应用: {is_js_app}")
        
        if is_js_app:
            print("🔍 调用reverse_engineer_keepa...")
            result = analyzer.reverse_engineer_keepa(test_asin)
            
            if result:
                print("✅ 完整流程成功")
                return True
            else:
                print("❌ 完整流程失败")
                
                # 这时应该会调用try_extract_from_js_app
                print("🔍 调用try_extract_from_js_app...")
                js_data = analyzer.try_extract_from_js_app(mock_response.text, test_asin)
                
                if js_data:
                    print("✅ try_extract_from_js_app成功")
                else:
                    print("❌ try_extract_from_js_app失败")
        
        return False
        
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 生产环境问题调试")
    print("=" * 60)
    
    success = test_production_scenario()
    
    print("\n" + "=" * 60)
    print(f"📋 调试结果: {'✅ 找到问题' if not success else '❓ 需要进一步调试'}")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
