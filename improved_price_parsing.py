#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的价格解析方法
解决价格异常高的问题
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def test_multiple_asins():
    """测试多个ASIN，分析价格解析问题"""
    print("🔍 测试多个ASIN的价格解析...")
    print("=" * 60)
    
    # 创建分析器
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    
    # 测试ASIN列表
    test_asins = [
        "B07TZDH6G4",  # 用户提供的ASIN
        "B07QNYTRG2",  # 已知有效的ASIN
        "B08KGW5XGN",  # 之前测试成功的ASIN
        "B0CN819XD8",  # 之前测试成功的ASIN
        "B004VMM718"   # 文件中的ASIN
    ]
    
    results = []
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n📱 测试ASIN {i}/{len(test_asins)}: {asin}")
        print("-" * 40)
        
        try:
            result = analyzer.reverse_engineer_keepa(asin)
            
            if result and analyzer.is_valid_websocket_result(result):
                highest = result.get('historical_highest_price', 0)
                lowest = result.get('historical_lowest_price', 0)
                current = result.get('current_price', 0)
                
                print(f"✅ 获取到数据:")
                print(f"   标题: {result.get('title', 'Unknown')[:50]}...")
                print(f"   历史最高价: ${highest:.2f}")
                print(f"   历史最低价: ${lowest:.2f}")
                if current:
                    print(f"   当前价格: ${current:.2f}")
                else:
                    print(f"   当前价格: 无")
                
                # 价格合理性检查
                price_issues = []
                if highest > 1000:
                    price_issues.append(f"最高价异常高 (${highest:.2f})")
                if lowest > highest:
                    price_issues.append("最低价高于最高价")
                if current > highest * 2:
                    price_issues.append(f"当前价格异常高 (${current:.2f})")
                
                if price_issues:
                    print(f"⚠️  价格异常: {', '.join(price_issues)}")
                else:
                    print(f"✅ 价格数据合理")
                
                results.append({
                    'asin': asin,
                    'success': True,
                    'highest': highest,
                    'lowest': lowest,
                    'current': current,
                    'issues': price_issues
                })
            else:
                print(f"❌ 未获取到数据")
                results.append({
                    'asin': asin,
                    'success': False
                })
                
        except Exception as e:
            print(f"❌ 异常: {str(e)}")
            results.append({
                'asin': asin,
                'success': False,
                'error': str(e)
            })
    
    # 分析结果
    print("\n" + "=" * 60)
    print("📊 测试结果分析:")
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"   ✅ 成功: {len(successful)}/{len(results)}")
    print(f"   ❌ 失败: {len(failed)}/{len(results)}")
    
    if successful:
        print(f"\n🎯 成功的ASIN:")
        for result in successful:
            issues_str = f" (⚠️ {len(result.get('issues', []))} 个价格异常)" if result.get('issues') else ""
            print(f"   • {result['asin']}: ${result['lowest']:.2f} - ${result['highest']:.2f}{issues_str}")
    
    if failed:
        print(f"\n❌ 失败的ASIN:")
        for result in failed:
            error_str = f" - {result.get('error', 'Unknown error')}" if result.get('error') else ""
            print(f"   • {result['asin']}{error_str}")
    
    # 价格异常分析
    price_issues = [r for r in successful if r.get('issues')]
    if price_issues:
        print(f"\n⚠️  价格异常分析:")
        print(f"   发现 {len(price_issues)} 个ASIN有价格异常")
        print(f"   这可能是因为:")
        print(f"   1. 价格数据包含了错误的价格类型（如列表价格）")
        print(f"   2. 价格解析时没有过滤极端值")
        print(f"   3. 需要改进价格数据的筛选逻辑")
    
    return results

def analyze_price_parsing_issue():
    """分析价格解析问题"""
    print("\n🔧 价格解析问题分析:")
    print("=" * 60)
    
    print("💡 可能的问题和解决方案:")
    print("1. 极端价格过滤:")
    print("   - 当前可能包含了列表价格或错误价格")
    print("   - 需要添加价格范围过滤 (如 $1-$500)")
    print("   - 过滤明显异常的价格点")
    
    print("\n2. 价格类型选择:")
    print("   - 优先使用Amazon官方价格")
    print("   - 其次使用第三方FBA价格")
    print("   - 避免使用列表价格或促销价格")
    
    print("\n3. 数据验证:")
    print("   - 确保最低价 <= 最高价")
    print("   - 当前价格应在合理范围内")
    print("   - 添加价格变化趋势验证")

def main():
    """主函数"""
    print("🚀 改进价格解析测试...")
    
    results = test_multiple_asins()
    analyze_price_parsing_issue()
    
    print("\n" + "=" * 60)
    print("📋 总结:")
    
    successful_count = len([r for r in results if r['success']])
    total_count = len(results)
    
    if successful_count > 0:
        print(f"✅ WebSocket方法工作正常 ({successful_count}/{total_count} 成功)")
        print("💡 主要问题是价格解析需要改进，而不是数据获取失败")
        print("🔧 建议优化价格过滤和验证逻辑")
    else:
        print("❌ WebSocket方法需要调试")
        print("💡 可能需要检查网络连接或代理设置")

if __name__ == "__main__":
    main()
