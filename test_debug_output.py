#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试调试输出
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPrice<PERSON>he<PERSON>

def test_debug_output():
    """测试调试输出"""
    print("🔍 测试调试输出...")
    
    # 创建分析器实例
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    
    # 测试一个失败的ASIN
    test_asin = "B07ZNW4TLG"
    
    print(f"📱 测试ASIN: {test_asin}")
    print("=" * 50)
    
    try:
        result = analyzer.reverse_engineer_keepa(test_asin)
        
        if result:
            print(f"✅ 意外成功: {result}")
        else:
            print(f"❌ 预期失败")
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")

def main():
    """主函数"""
    print("🚀 调试输出测试")
    print("=" * 50)
    
    test_debug_output()
    
    print("\n" + "=" * 50)
    print("📋 测试完成")

if __name__ == "__main__":
    main()
