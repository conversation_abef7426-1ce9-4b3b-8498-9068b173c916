#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keepa逆向工程测试脚本
测试新的逆向工程功能
"""

import sys
import os
from 历史价格8 import KeepaHistoricalPriceChecker

def test_reverse_engineering():
    """测试逆向工程功能"""
    print("🔧 Keepa逆向工程测试工具")
    print("这个工具会测试新的逆向工程功能")
    print("=" * 60)
    
    # 测试ASIN列表
    test_asins = ["B07QNYTRG2", "B07SPTL99F"]
    
    for asin in test_asins:
        print(f"\n{'=' * 20} 测试 {asin} {'=' * 20}")
        print(f"🧪 开始逆向工程测试ASIN: {asin}")
        print("=" * 50)
        
        try:
            # 创建检查器
            checker = KeepaHistoricalPriceChecker(
                threads=1,
                marketplace="US",
                verbose=True
            )
            
            print("✅ 创建检查器成功")
            
            # 加载代理
            if checker.load_proxies():
                print(f"📡 加载了 {len(checker.proxies)}个代理")
            else:
                print("⚠️  未加载代理，将使用无代理模式")
            
            # 市场已在构造函数中设置
            print(f"🌍 市场设置: {checker.marketplace}")
            
            print(f"\n🔍 开始处理ASIN: {asin}")
            
            # 直接调用逆向工程方法
            result = checker.reverse_engineer_keepa(asin)
            
            if result:
                print(f"🎉 逆向工程成功!")
                print(f"📊 结果:")
                print(f"  历史最高价: {result.get('historical_highest_price')}")
                print(f"  历史最低价: {result.get('historical_lowest_price')}")
                print(f"  断货状态: {result.get('out_of_stock_within_month')}")
            else:
                print(f"❌ 逆向工程失败")
                
                # 如果逆向工程失败，尝试常规方法
                print(f"🔄 尝试常规方法...")
                regular_result = checker.get_keepa_info(asin)
                
                if regular_result:
                    print(f"✅ 常规方法成功")
                    print(f"📊 结果:")
                    print(f"  历史最高价: {regular_result.get('historical_highest_price')}")
                    print(f"  历史最低价: {regular_result.get('historical_lowest_price')}")
                    print(f"  断货状态: {regular_result.get('out_of_stock_within_month')}")
                else:
                    print(f"❌ 常规方法也失败")
            
            # 显示统计信息
            print(f"\n=== 请求统计 ===")
            print(f"总请求数: {checker.request_count}")
            print(f"成功请求: {checker.success_count}")
            print(f"失败请求: {checker.error_count}")
            print(f"成功率: {(checker.success_count / max(checker.request_count, 1)) * 100:.1f}%")
            print(f"可用代理数: {len(checker.proxies) - len(checker.invalid_proxies)}")
            
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("=" * 20)
    
    print(f"\n💾 调试文件已保存到当前目录")
    print(f"   - debug_*.html (完整HTML响应)")
    print(f"   - analysis_*.txt (分析报告)")
    print(f"   - structure_analysis_*.txt (HTML结构分析)")

def test_specific_methods():
    """测试特定的逆向工程方法"""
    print(f"\n🔬 测试特定逆向工程方法")
    print("=" * 40)
    
    try:
        checker = KeepaHistoricalPriceChecker(marketplace="US", verbose=True)
        checker.load_proxies()
        
        asin = "B07QNYTRG2"
        
        # 测试1: 获取主页面数据
        print(f"📄 测试1: 获取主页面数据")
        main_page_data = checker.get_keepa_main_page(asin)
        if main_page_data:
            print(f"✅ 获取主页面成功")
            print(f"   WebSocket服务器: {main_page_data.get('ws_server', '未找到')}")
            print(f"   版本: {main_page_data.get('version', '未找到')}")
            print(f"   HTML长度: {len(main_page_data.get('html', ''))}")
        else:
            print(f"❌ 获取主页面失败")
        
        # 测试2: 分析JavaScript文件
        print(f"\n📜 测试2: 分析JavaScript文件")
        api_endpoints = checker.analyze_keepa_js()
        print(f"✅ 发现 {len(api_endpoints)} 个API端点:")
        for i, endpoint in enumerate(api_endpoints[:5], 1):  # 只显示前5个
            print(f"   {i}. {endpoint}")
        
        # 测试3: 尝试API调用
        print(f"\n🌐 测试3: 尝试API调用")
        for endpoint in api_endpoints[:3]:  # 只测试前3个
            result = checker.try_keepa_api(endpoint, asin)
            if result:
                print(f"✅ API成功: {endpoint}")
                break
            else:
                print(f"❌ API失败: {endpoint}")
        
    except Exception as e:
        print(f"❌ 特定方法测试异常: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始Keepa逆向工程测试")
    
    # 基本逆向工程测试
    test_reverse_engineering()
    
    # 特定方法测试
    test_specific_methods()
    
    print(f"\n🌍 测试完成!")
    print(f"请查看生成的调试文件来分析结果")
