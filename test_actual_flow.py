#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实际的处理流程
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def test_actual_processing_flow():
    """测试实际的处理流程"""
    print("🔍 测试实际处理流程...")
    
    # 创建分析器实例，启用详细输出
    analyzer = KeepaHistoricalPriceChecker(verbose=True)
    
    # 使用一个失败的ASIN来模拟实际情况
    test_asin = "B07ZNW4TLG"  # 这是用户输出中失败的ASIN
    
    print(f"📱 测试ASIN: {test_asin}")
    print("=" * 50)
    
    try:
        # 直接调用reverse_engineer_keepa方法
        print("🔍 直接调用reverse_engineer_keepa方法...")
        result = analyzer.reverse_engineer_keepa(test_asin)
        
        if result:
            print("✅ 直接调用成功:")
            print(f"   ASIN: {result.get('asin')}")
            print(f"   数据源: {result.get('source', 'unknown')}")
            print(f"   最高价: ${result.get('historical_highest_price', 0):.2f}")
            print(f"   最低价: ${result.get('historical_lowest_price', 0):.2f}")
        else:
            print("❌ 直接调用失败")
        
        print("\n" + "=" * 50)
        
        # 现在测试完整的处理流程
        print("🔍 测试完整处理流程...")
        
        # 模拟从文件处理的流程
        # 这会调用process_asin_batch方法
        
        # 创建临时ASIN文件
        temp_file = "temp_test.txt"
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(f"{test_asin}\n")
        
        print(f"📝 创建临时文件: {temp_file}")
        
        # 使用单线程处理
        analyzer.thread_count.set(1)
        
        # 调用处理方法
        print("🚀 开始批量处理...")
        
        # 这里我们需要找到实际调用的方法
        # 让我检查process_asin_batch或类似的方法
        
        # 清理临时文件
        try:
            os.remove(temp_file)
        except:
            pass
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始实际流程测试...")
    print("=" * 60)
    
    test_actual_processing_flow()
    
    print("\n" + "=" * 60)
    print("📋 测试完成")

if __name__ == "__main__":
    main()
