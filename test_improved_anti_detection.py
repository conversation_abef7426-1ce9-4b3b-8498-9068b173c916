#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的反检测机制
验证新的User-Agent和反检测措施是否有效
"""

import sys
import os
import time
import random

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoryChecker

def test_improved_anti_detection():
    """测试改进的反检测机制"""
    print("🔧 测试改进的反检测机制")
    print("=" * 50)
    
    # 创建检查器实例
    checker = KeepaHistoryChecker(
        marketplace='US',
        threads=3,  # 使用较少的线程
        verbose=True
    )
    
    # 测试ASIN列表 - 选择之前成功和失败的混合
    test_asins = [
        'B009ONDIHG',  # 之前成功的
        'B0BLV9CPYL',  # 之前成功的
        'B06XG2DGYV',  # 之前失败的
        'B003UNNNXA',  # 之前失败的
    ]
    
    print(f"📋 测试 {len(test_asins)} 个ASIN:")
    for asin in test_asins:
        print(f"   - {asin}")
    print()
    
    success_count = 0
    total_count = len(test_asins)
    
    for i, asin in enumerate(test_asins, 1):
        print(f"🔍 [{i}/{total_count}] 测试 {asin}...")
        
        try:
            # 添加随机延迟
            delay = random.uniform(3, 6)
            print(f"   ⏱️ 等待 {delay:.1f} 秒...")
            time.sleep(delay)
            
            # 获取价格信息
            result = checker.reverse_engineer_keepa(asin)
            
            if result and result.get('success') and result.get('source') == 'keepa_browser_real_stats':
                success_count += 1
                highest_price = result.get('historical_highest_price', 0)
                current_price = result.get('current_price', 0)
                confidence = result.get('confidence', '未知')
                
                print(f"   ✅ 成功获取真实价格:")
                print(f"      🏆 最高价: ${highest_price:.2f}")
                print(f"      💰 当前价: ${current_price:.2f}")
                print(f"      🎯 置信度: {confidence}")
                
                # 检查分类信息
                if 'category' in result:
                    print(f"      📂 分类: {result['category']}")
                
            else:
                print(f"   ❌ 无法获取真实数据")
                
        except Exception as e:
            print(f"   💥 处理异常: {str(e)}")
        
        print()
    
    # 计算成功率
    success_rate = (success_count / total_count) * 100
    
    print("=" * 50)
    print("📊 测试结果统计:")
    print(f"✅ 成功获取真实价格: {success_count}/{total_count} ({success_rate:.1f}%)")
    print(f"❌ 无法获取真实数据: {total_count - success_count}/{total_count} ({100 - success_rate:.1f}%)")
    
    # 评估改进效果
    if success_rate >= 75:
        print("🎉 反检测机制效果优秀！")
    elif success_rate >= 50:
        print("👍 反检测机制效果良好")
    elif success_rate >= 25:
        print("⚠️ 反检测机制需要进一步优化")
    else:
        print("🚨 反检测机制效果不佳，需要重新设计")
    
    print("\n🔧 改进措施:")
    print("1. ✅ 更新User-Agent到2025年最新版本")
    print("2. ✅ 增加浏览器反检测参数")
    print("3. ✅ 添加随机延迟 (2-5秒)")
    print("4. ✅ 限制线程数量 (最多5个)")
    print("5. ✅ 轮换多种现代浏览器标识")
    
    return success_rate

if __name__ == "__main__":
    try:
        success_rate = test_improved_anti_detection()
        
        print(f"\n🎯 最终成功率: {success_rate:.1f}%")
        
        if success_rate > 0:
            print("✅ 改进的反检测机制正在工作！")
        else:
            print("❌ 需要进一步调整反检测策略")
            
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中出现错误: {str(e)}")
