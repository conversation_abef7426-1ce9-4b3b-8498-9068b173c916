#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级Keepa 401验证绕过器 - 使用最新的反检测技术
基于2025年最新的Cloudflare绕过研究
"""

import sys
import os
import json
import time
import random
import asyncio
import requests
from typing import Dict, Optional, List
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
import websocket
import threading
import gzip
import zlib

class AdvancedKeepaBypass:
    """高级Keepa绕过器 - 集成多种最新技术"""
    
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.session_cache = {}  # 会话缓存
        self.success_fingerprints = []  # 成功的指纹配置
        self.driver = None
        self.websocket_messages = []
        
        # 住宅代理配置（如果有的话）
        self.proxy_list = self.load_residential_proxies()
        
        if self.verbose:
            print("🚀 初始化高级Keepa绕过器...")
            print(f"📡 加载了 {len(self.proxy_list)} 个住宅代理")
    
    def load_residential_proxies(self) -> List[Dict]:
        """加载住宅代理列表"""
        try:
            # 这里可以加载您的住宅代理
            # 格式: [{"host": "ip", "port": port, "username": "user", "password": "pass"}]
            return []
        except:
            return []
    
    def get_random_fingerprint(self) -> Dict:
        """生成随机的浏览器指纹"""
        screen_resolutions = [
            (1920, 1080), (1366, 768), (1536, 864), (1440, 900),
            (1280, 720), (1600, 900), (2560, 1440), (1920, 1200)
        ]
        
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
        ]
        
        width, height = random.choice(screen_resolutions)
        
        return {
            "user_agent": random.choice(user_agents),
            "screen_width": width,
            "screen_height": height,
            "viewport_width": width - random.randint(0, 100),
            "viewport_height": height - random.randint(100, 200),
            "timezone": random.choice(["America/New_York", "America/Los_Angeles", "Europe/London", "Asia/Tokyo"]),
            "language": random.choice(["en-US", "en-GB", "zh-CN", "ja-JP"]),
        }
    
    def create_undetected_driver(self, fingerprint: Dict) -> uc.Chrome:
        """创建undetected-chromedriver实例"""
        if self.verbose:
            print("🔧 创建undetected-chromedriver...")
        
        options = uc.ChromeOptions()
        
        # 基础反检测配置
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-images')  # 加速加载
        options.add_argument('--disable-javascript')  # 先禁用JS，后面手动启用
        
        # 设置窗口大小
        options.add_argument(f'--window-size={fingerprint["viewport_width"]},{fingerprint["viewport_height"]}')
        
        # 设置用户代理
        options.add_argument(f'--user-agent={fingerprint["user_agent"]}')
        
        # 语言设置
        options.add_argument(f'--lang={fingerprint["language"]}')
        
        # 时区设置
        options.add_argument(f'--timezone={fingerprint["timezone"]}')
        
        # 代理设置（如果有住宅代理）
        if self.proxy_list:
            proxy = random.choice(self.proxy_list)
            proxy_str = f"{proxy['host']}:{proxy['port']}"
            options.add_argument(f'--proxy-server={proxy_str}')
            if self.verbose:
                print(f"🌐 使用住宅代理: {proxy_str}")
        
        # 创建驱动器
        try:
            driver = uc.Chrome(options=options, version_main=None)
            
            # 设置页面加载超时
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(10)
            
            # 执行额外的反检测脚本
            self.inject_stealth_scripts(driver)
            
            return driver
            
        except Exception as e:
            if self.verbose:
                print(f"❌ 创建驱动器失败: {str(e)}")
            return None
    
    def inject_stealth_scripts(self, driver):
        """注入反检测脚本"""
        stealth_scripts = [
            # 移除webdriver属性
            "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})",
            
            # 修改plugins
            """
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5]
            });
            """,
            
            # 修改语言
            f"Object.defineProperty(navigator, 'languages', {{get: () => ['en-US', 'en']}})",
            
            # 修改平台
            "Object.defineProperty(navigator, 'platform', {get: () => 'Win32'})",
            
            # 修改权限
            """
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
            );
            """
        ]
        
        for script in stealth_scripts:
            try:
                driver.execute_cdp_cmd('Runtime.evaluate', {'expression': script})
            except:
                pass
    
    def simulate_human_behavior(self, driver):
        """模拟真实用户行为"""
        if self.verbose:
            print("🤖 模拟人类行为...")
        
        # 随机鼠标移动
        try:
            driver.execute_script("""
                function simulateMouseMove() {
                    const event = new MouseEvent('mousemove', {
                        clientX: Math.random() * window.innerWidth,
                        clientY: Math.random() * window.innerHeight,
                        bubbles: true
                    });
                    document.dispatchEvent(event);
                }
                
                // 执行多次随机鼠标移动
                for(let i = 0; i < 5; i++) {
                    setTimeout(simulateMouseMove, i * 200);
                }
            """)
            
            # 随机滚动
            scroll_amount = random.randint(100, 500)
            driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
            time.sleep(random.uniform(0.5, 1.5))
            
            # 随机点击（安全区域）
            driver.execute_script("""
                const safeArea = document.body;
                if (safeArea) {
                    const rect = safeArea.getBoundingClientRect();
                    const x = rect.left + Math.random() * rect.width;
                    const y = rect.top + Math.random() * rect.height;
                    
                    const clickEvent = new MouseEvent('click', {
                        clientX: x,
                        clientY: y,
                        bubbles: true
                    });
                    safeArea.dispatchEvent(clickEvent);
                }
            """)
            
        except Exception as e:
            if self.verbose:
                print(f"⚠️ 行为模拟异常: {str(e)}")
    
    def warmup_session(self, driver) -> bool:
        """预热会话 - 模拟真实用户浏览路径"""
        if self.verbose:
            print("🔥 开始会话预热...")
        
        try:
            # 第一步：访问主页
            if self.verbose:
                print("📄 访问Keepa主页...")
            driver.get("https://keepa.com")
            time.sleep(random.uniform(2, 4))
            
            # 检查是否有Cloudflare挑战
            if self.handle_cloudflare_challenge(driver):
                if self.verbose:
                    print("✅ Cloudflare挑战已处理")
            
            # 模拟浏览行为
            self.simulate_human_behavior(driver)
            time.sleep(random.uniform(1, 3))
            
            # 第二步：访问一个产品分类页面
            if self.verbose:
                print("📂 浏览产品分类...")
            try:
                driver.get("https://keepa.com/#!category/1-16225007011")
                time.sleep(random.uniform(2, 4))
                self.simulate_human_behavior(driver)
            except:
                pass
            
            # 第三步：搜索功能测试
            if self.verbose:
                print("🔍 测试搜索功能...")
            try:
                driver.get("https://keepa.com/#!search/1-digital%20camera")
                time.sleep(random.uniform(2, 4))
                self.simulate_human_behavior(driver)
            except:
                pass
            
            if self.verbose:
                print("✅ 会话预热完成")
            return True
            
        except Exception as e:
            if self.verbose:
                print(f"❌ 会话预热失败: {str(e)}")
            return False
    
    def handle_cloudflare_challenge(self, driver) -> bool:
        """处理Cloudflare挑战"""
        try:
            # 检查是否有Cloudflare挑战页面
            challenge_indicators = [
                "Checking your browser",
                "Please wait",
                "DDoS protection",
                "Cloudflare",
                "challenges.cloudflare.com"
            ]
            
            page_source = driver.page_source.lower()
            has_challenge = any(indicator.lower() in page_source for indicator in challenge_indicators)
            
            if has_challenge:
                if self.verbose:
                    print("🛡️ 检测到Cloudflare挑战，等待解决...")
                
                # 等待挑战解决（最多60秒）
                for i in range(60):
                    time.sleep(1)
                    current_url = driver.current_url
                    if "keepa.com" in current_url and "challenge" not in current_url.lower():
                        if self.verbose:
                            print(f"✅ Cloudflare挑战已解决 ({i+1}s)")
                        return True
                
                if self.verbose:
                    print("⏰ Cloudflare挑战解决超时")
                return False
            
            return True
            
        except Exception as e:
            if self.verbose:
                print(f"❌ 处理Cloudflare挑战异常: {str(e)}")
            return False
    
    def extract_session_data(self, driver) -> Dict:
        """提取会话数据用于重用"""
        try:
            cookies = driver.get_cookies()
            user_agent = driver.execute_script("return navigator.userAgent;")
            
            session_data = {
                "cookies": cookies,
                "user_agent": user_agent,
                "timestamp": time.time()
            }
            
            return session_data
            
        except Exception as e:
            if self.verbose:
                print(f"❌ 提取会话数据失败: {str(e)}")
            return {}

    def setup_websocket_listener(self, driver):
        """设置WebSocket监听器"""
        self.websocket_messages = []

        # 注入WebSocket监听脚本
        websocket_script = """
        window.keepaWebSocketMessages = [];

        // 拦截WebSocket
        const originalWebSocket = window.WebSocket;
        window.WebSocket = function(url, protocols) {
            const ws = new originalWebSocket(url, protocols);

            ws.addEventListener('message', function(event) {
                try {
                    let data = event.data;
                    if (typeof data === 'string') {
                        window.keepaWebSocketMessages.push({
                            type: 'text',
                            data: data,
                            timestamp: Date.now()
                        });
                    } else if (data instanceof Blob) {
                        const reader = new FileReader();
                        reader.onload = function() {
                            window.keepaWebSocketMessages.push({
                                type: 'blob',
                                data: reader.result,
                                timestamp: Date.now()
                            });
                        };
                        reader.readAsArrayBuffer(data);
                    }
                } catch (e) {
                    console.log('WebSocket message capture error:', e);
                }
            });

            return ws;
        };
        """

        try:
            driver.execute_script(websocket_script)
            if self.verbose:
                print("📡 WebSocket监听器已设置")
        except Exception as e:
            if self.verbose:
                print(f"❌ 设置WebSocket监听器失败: {str(e)}")

    def collect_websocket_messages(self, driver) -> List[Dict]:
        """收集WebSocket消息"""
        try:
            messages = driver.execute_script("return window.keepaWebSocketMessages || [];")

            processed_messages = []
            for msg in messages:
                try:
                    if msg['type'] == 'text':
                        # 尝试解析JSON
                        try:
                            json_data = json.loads(msg['data'])
                            processed_messages.append(json_data)
                        except:
                            processed_messages.append({'raw_text': msg['data']})
                    elif msg['type'] == 'blob':
                        # 处理二进制数据
                        processed_messages.append({'raw_blob': msg['data']})
                except Exception as e:
                    if self.verbose:
                        print(f"⚠️ 处理WebSocket消息异常: {str(e)}")

            return processed_messages

        except Exception as e:
            if self.verbose:
                print(f"❌ 收集WebSocket消息失败: {str(e)}")
            return []

    def advanced_bypass_keepa(self, asin: str, max_retries: int = 3) -> Optional[Dict]:
        """高级Keepa绕过主方法"""
        if self.verbose:
            print(f"🎯 开始高级绕过 ASIN: {asin}")

        for attempt in range(max_retries):
            if self.verbose:
                print(f"🔄 尝试 {attempt + 1}/{max_retries}")

            # 生成新的指纹
            fingerprint = self.get_random_fingerprint()

            # 创建驱动器
            driver = self.create_undetected_driver(fingerprint)
            if not driver:
                continue

            try:
                # 预热会话
                if not self.warmup_session(driver):
                    if self.verbose:
                        print("❌ 会话预热失败，重试...")
                    continue

                # 设置WebSocket监听
                self.setup_websocket_listener(driver)

                # 访问目标产品页面
                product_url = f"https://keepa.com/#!product/1-{asin}"
                if self.verbose:
                    print(f"🛒 访问产品页面: {product_url}")

                driver.get(product_url)

                # 处理可能的Cloudflare挑战
                if not self.handle_cloudflare_challenge(driver):
                    if self.verbose:
                        print("❌ Cloudflare挑战处理失败")
                    continue

                # 等待页面加载和WebSocket数据
                if self.verbose:
                    print("⏳ 等待WebSocket数据...")

                time.sleep(15)  # 等待数据加载

                # 模拟用户交互
                self.simulate_human_behavior(driver)
                time.sleep(5)

                # 收集WebSocket消息
                messages = self.collect_websocket_messages(driver)

                if self.verbose:
                    print(f"📨 收集到 {len(messages)} 条WebSocket消息")

                # 解析数据
                result = self.parse_keepa_data(messages, asin)

                if result:
                    if self.verbose:
                        print(f"✅ 成功获取 {asin} 的数据!")

                    # 保存成功的指纹和会话
                    self.success_fingerprints.append(fingerprint)
                    session_data = self.extract_session_data(driver)
                    self.session_cache[asin] = session_data

                    return result
                else:
                    if self.verbose:
                        print(f"❌ 未能解析 {asin} 的数据")

            except Exception as e:
                if self.verbose:
                    print(f"❌ 尝试 {attempt + 1} 异常: {str(e)}")

            finally:
                # 清理驱动器
                try:
                    driver.quit()
                except:
                    pass

                # 随机等待后重试
                if attempt < max_retries - 1:
                    wait_time = random.uniform(10, 20)
                    if self.verbose:
                        print(f"⏳ 等待 {wait_time:.1f}s 后重试...")
                    time.sleep(wait_time)

        if self.verbose:
            print(f"❌ 所有尝试均失败: {asin}")
        return None

    def parse_keepa_data(self, messages: List[Dict], asin: str) -> Optional[Dict]:
        """解析Keepa数据"""
        if self.verbose:
            print(f"🔍 解析 {asin} 的数据...")

        for message in messages:
            try:
                # 查找包含产品数据的消息
                if isinstance(message, dict):
                    # 检查是否包含basicProducts
                    if 'json' in message:
                        json_str = message['json']
                        if 'basicProducts' in json_str:
                            try:
                                data = json.loads(json_str)
                                if 'basicProducts' in data:
                                    products = data['basicProducts']
                                    for product in products:
                                        if product.get('asin') == asin:
                                            return self.extract_price_data(product)
                            except:
                                continue

                    # 直接检查消息内容
                    if 'basicProducts' in message:
                        products = message['basicProducts']
                        for product in products:
                            if product.get('asin') == asin:
                                return self.extract_price_data(product)

            except Exception as e:
                if self.verbose:
                    print(f"⚠️ 解析消息异常: {str(e)}")
                continue

        return None

    def extract_price_data(self, product: Dict) -> Dict:
        """从产品数据中提取价格信息"""
        try:
            asin = product.get('asin', '')
            title = product.get('title', '')

            # 提取价格历史数据
            csv_data = product.get('csv', [])
            if not csv_data:
                return None

            # 解析价格数据
            prices = []
            for i in range(0, len(csv_data), 2):
                if i + 1 < len(csv_data):
                    timestamp = csv_data[i]
                    price = csv_data[i + 1]
                    if price > 0:  # 过滤无效价格
                        prices.append(price)

            if not prices:
                return None

            # 价格过滤：移除异常价格
            filtered_prices = [p for p in prices if 1 <= p <= 200000]  # 0.01-2000美元

            if not filtered_prices:
                return None

            highest_price = max(filtered_prices) / 100.0
            lowest_price = min(filtered_prices) / 100.0

            result = {
                'asin': asin,
                'title': title,
                'historical_highest_price': highest_price,
                'historical_lowest_price': lowest_price,
                'current_price': None  # 可以从其他字段获取
            }

            return result

        except Exception as e:
            if self.verbose:
                print(f"❌ 提取价格数据异常: {str(e)}")
            return None

def test_advanced_bypass():
    """测试高级绕过功能"""
    print("🚀 启动高级Keepa 401绕过测试...")
    print("🎯 使用最新的反检测技术:")
    print("   • undetected-chromedriver")
    print("   • 真实浏览器指纹轮换")
    print("   • 会话预热和行为模拟")
    print("   • 住宅代理支持")
    print("   • WebSocket消息拦截")
    print("=" * 80)

    # 创建绕过器
    bypasser = AdvancedKeepaBypass(verbose=True)

    # 测试ASIN列表
    test_asins = [
        "B07TZDH6G4",  # 之前成功的ASIN
        "B07SPTL99F",  # 之前失败的ASIN
        "B07TXJKT46",  # 之前失败的ASIN
        "B08N5WRWNW",  # 新测试ASIN
    ]

    results = {}
    success_count = 0

    for i, asin in enumerate(test_asins, 1):
        print(f"\n🎯 [{i}/{len(test_asins)}] 测试ASIN: {asin}")
        print("-" * 60)

        start_time = time.time()

        try:
            result = bypasser.advanced_bypass_keepa(asin)
            elapsed_time = time.time() - start_time

            if result:
                success_count += 1
                highest = result.get('historical_highest_price', '未知')
                lowest = result.get('historical_lowest_price', '未知')
                title = result.get('title', '未知')

                print(f"✅ {asin}: 高级绕过成功!")
                print(f"   📝 产品标题: {title[:50]}...")
                print(f"   📊 最高价格: ${highest}")
                print(f"   📊 最低价格: ${lowest}")
                print(f"   ⏱️  处理时间: {elapsed_time:.1f}s")

                results[asin] = "成功"
            else:
                print(f"❌ {asin}: 高级绕过失败")
                results[asin] = "失败"

        except Exception as e:
            elapsed_time = time.time() - start_time
            print(f"❌ {asin}: 异常 - {str(e)}")
            print(f"   ⏱️  处理时间: {elapsed_time:.1f}s")
            results[asin] = f"异常: {str(e)}"

        # 测试间隔
        if i < len(test_asins):
            print("⏳ 等待15秒后继续下一个测试...")
            time.sleep(15)

    # 输出总结
    print("\n" + "=" * 80)
    print("📋 高级绕过测试总结")
    print("=" * 80)

    for asin, status in results.items():
        if status == "成功":
            print(f"✅ {asin}: {status}")
        else:
            print(f"❌ {asin}: {status}")

    total_tests = len(results)
    success_rate = (success_count / total_tests * 100) if total_tests > 0 else 0

    print(f"\n📊 统计结果:")
    print(f"   总测试数: {total_tests}")
    print(f"   成功数量: {success_count}")
    print(f"   失败数量: {total_tests - success_count}")
    print(f"   成功率: {success_rate:.1f}%")

    if success_rate >= 75:
        print("\n🎉 高级绕过功能表现优秀!")
    elif success_rate >= 50:
        print("\n👍 高级绕过功能表现良好!")
    elif success_rate > 0:
        print("\n⚠️ 高级绕过功能部分有效，需要进一步优化")
    else:
        print("\n❌ 高级绕过功能需要重新设计")

    return results

def install_dependencies():
    """安装必要的依赖"""
    print("📦 检查并安装依赖...")

    required_packages = [
        "undetected-chromedriver",
        "selenium",
        "requests",
        "websocket-client"
    ]

    import subprocess
    import sys

    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📥 安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装完成")

if __name__ == "__main__":
    print("🔓 高级Keepa 401验证绕过器")
    print("🛠️ 基于2025年最新反检测技术")
    print("📚 集成undetected-chromedriver + 行为模拟 + 会话管理")
    print()

    try:
        # 检查依赖
        install_dependencies()
        print()

        # 运行测试
        test_results = test_advanced_bypass()

        print("\n🏁 测试完成!")

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
