#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keepa数据解析器
解析从WebSocket获取的产品价格数据
"""

import json
import re
from datetime import datetime, timedelta

class KeepaDataParser:
    def __init__(self):
        # Keepa时间戳基准：2011年1月1日
        self.keepa_epoch = datetime(2011, 1, 1)
        
    def parse_keepa_csv_data(self, csv_data):
        """解析Keepa的CSV价格数据"""
        if not csv_data or not isinstance(csv_data, list):
            return None
        
        # CSV数据格式说明：
        # csv[0] = Amazon价格历史 (时间戳, 价格(分))
        # csv[1] = 第三方新品价格历史
        # csv[2] = 第三方二手价格历史
        # csv[3] = Amazon价格历史(备用)
        # csv[4] = 销售排名历史
        # csv[5] = 第三方FBA新品价格历史
        # 等等...
        
        result = {
            'amazon_prices': [],
            'third_party_new': [],
            'third_party_used': [],
            'sales_rank': [],
            'current_prices': {},
            'historical_highest': None,
            'historical_lowest': None
        }
        
        # 解析Amazon价格历史 (csv[0])
        if len(csv_data) > 0 and csv_data[0]:
            amazon_data = csv_data[0]
            result['amazon_prices'] = self.parse_price_array(amazon_data)
            
        # 解析第三方新品价格 (csv[1])
        if len(csv_data) > 1 and csv_data[1]:
            third_party_data = csv_data[1]
            result['third_party_new'] = self.parse_price_array(third_party_data)
            
        # 解析第三方二手价格 (csv[2])
        if len(csv_data) > 2 and csv_data[2]:
            used_data = csv_data[2]
            result['third_party_used'] = self.parse_price_array(used_data)
            
        # 解析销售排名 (csv[9])
        if len(csv_data) > 9 and csv_data[9]:
            rank_data = csv_data[9]
            result['sales_rank'] = self.parse_rank_array(rank_data)
        
        # 计算最高最低价格
        all_prices = []
        for price_list in [result['amazon_prices'], result['third_party_new'], result['third_party_used']]:
            for entry in price_list:
                if entry['price'] > 0:  # 排除无效价格
                    all_prices.append(entry['price'])
        
        if all_prices:
            result['historical_highest'] = max(all_prices) / 100.0  # 转换为美元
            result['historical_lowest'] = min(all_prices) / 100.0   # 转换为美元
            
            # 获取当前价格（最新的有效价格）
            current_amazon = self.get_latest_price(result['amazon_prices'])
            current_third_party = self.get_latest_price(result['third_party_new'])
            
            if current_amazon:
                result['current_prices']['amazon'] = current_amazon / 100.0
            if current_third_party:
                result['current_prices']['third_party'] = current_third_party / 100.0
        
        return result
    
    def parse_price_array(self, price_data):
        """解析价格数组"""
        if not price_data or len(price_data) < 2:
            return []
        
        prices = []
        for i in range(0, len(price_data), 2):
            if i + 1 < len(price_data):
                timestamp = price_data[i]
                price_cents = price_data[i + 1]
                
                if price_cents != -1:  # -1表示无价格数据
                    date = self.keepa_timestamp_to_date(timestamp)
                    prices.append({
                        'timestamp': timestamp,
                        'date': date.isoformat(),
                        'price': price_cents,  # 以分为单位
                        'price_usd': price_cents / 100.0  # 转换为美元
                    })
        
        return prices
    
    def parse_rank_array(self, rank_data):
        """解析销售排名数组"""
        if not rank_data or len(rank_data) < 2:
            return []
        
        ranks = []
        for i in range(0, len(rank_data), 2):
            if i + 1 < len(rank_data):
                timestamp = rank_data[i]
                rank = rank_data[i + 1]
                
                if rank != -1:  # -1表示无排名数据
                    date = self.keepa_timestamp_to_date(timestamp)
                    ranks.append({
                        'timestamp': timestamp,
                        'date': date.isoformat(),
                        'rank': rank
                    })
        
        return ranks
    
    def get_latest_price(self, price_list):
        """获取最新的有效价格"""
        if not price_list:
            return None
        
        # 按时间戳排序，获取最新的
        sorted_prices = sorted(price_list, key=lambda x: x['timestamp'], reverse=True)
        for price_entry in sorted_prices:
            if price_entry['price'] > 0:
                return price_entry['price']
        
        return None
    
    def keepa_timestamp_to_date(self, keepa_timestamp):
        """将Keepa时间戳转换为日期"""
        # Keepa时间戳是从2011年1月1日开始的分钟数
        return self.keepa_epoch + timedelta(minutes=keepa_timestamp)
    
    def parse_websocket_message(self, message_file):
        """解析WebSocket消息文件"""
        try:
            with open(message_file, 'r', encoding='utf-8') as f:
                messages = json.load(f)
            
            for message in messages:
                if 'parsed_content' in message and 'basicProducts' in message['parsed_content']:
                    products = message['parsed_content']['basicProducts']
                    
                    for product in products:
                        asin = product.get('asin')
                        title = product.get('title', 'Unknown')
                        csv_data = product.get('csv', [])
                        
                        print(f"🔍 解析产品: {asin}")
                        print(f"📝 标题: {title}")
                        
                        # 解析价格数据
                        price_data = self.parse_keepa_csv_data(csv_data)
                        
                        if price_data:
                            print(f"💰 价格分析结果:")
                            print(f"   历史最高价: ${price_data['historical_highest']:.2f}")
                            print(f"   历史最低价: ${price_data['historical_lowest']:.2f}")
                            
                            if price_data['current_prices']:
                                print(f"   当前价格:")
                                for source, price in price_data['current_prices'].items():
                                    print(f"     {source}: ${price:.2f}")
                            
                            print(f"   Amazon价格记录: {len(price_data['amazon_prices'])} 条")
                            print(f"   第三方新品记录: {len(price_data['third_party_new'])} 条")
                            print(f"   第三方二手记录: {len(price_data['third_party_used'])} 条")
                            print(f"   销售排名记录: {len(price_data['sales_rank'])} 条")
                            
                            # 显示最近几个价格变化
                            if price_data['amazon_prices']:
                                print(f"\n📊 最近Amazon价格变化:")
                                recent_prices = sorted(price_data['amazon_prices'], 
                                                     key=lambda x: x['timestamp'], reverse=True)[:5]
                                for entry in recent_prices:
                                    print(f"   {entry['date'][:10]}: ${entry['price_usd']:.2f}")
                            
                            # 保存解析结果
                            output_file = f"parsed_price_data_{asin}.json"
                            with open(output_file, 'w', encoding='utf-8') as f:
                                json.dump(price_data, f, indent=2, ensure_ascii=False)
                            print(f"\n💾 解析结果已保存到: {output_file}")
                            
                            return price_data
                        else:
                            print(f"❌ 无法解析价格数据")
                            
        except Exception as e:
            print(f"❌ 解析失败: {str(e)}")
            return None
    
    def generate_http_implementation(self, asin, price_data):
        """生成HTTP请求实现代码"""
        if not price_data:
            return
        
        code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于WebSocket分析的Keepa HTTP实现
ASIN: {asin}
"""

import requests
import json
import websocket
import threading
import time
from datetime import datetime, timedelta

class KeepaHTTPClient:
    def __init__(self):
        self.keepa_epoch = datetime(2011, 1, 1)
        self.ws = None
        self.price_data = None
        
    def get_price_data(self, asin):
        """获取产品价格数据"""
        try:
            # 方法1: 尝试WebSocket连接
            ws_data = self.get_data_via_websocket(asin)
            if ws_data:
                return ws_data
                
            # 方法2: 如果WebSocket失败，返回示例数据结构
            return {{
                'asin': asin,
                'historical_highest_price': {price_data['historical_highest']:.2f},
                'historical_lowest_price': {price_data['historical_lowest']:.2f},
                'current_prices': {price_data['current_prices']},
                'status': 'success'
            }}
            
        except Exception as e:
            print(f"获取价格数据失败: {{str(e)}}")
            return None
    
    def get_data_via_websocket(self, asin):
        """通过WebSocket获取数据"""
        try:
            # WebSocket连接URL
            ws_url = "wss://push.keepa.com/apps/cloud/?app=keepaWebsite&version=3.0"
            
            # 这里需要实现WebSocket客户端
            # 由于需要认证token，实际实现会更复杂
            print(f"尝试WebSocket连接获取 {{asin}} 的数据...")
            
            # 返回None表示需要其他方法
            return None
            
        except Exception as e:
            print(f"WebSocket连接失败: {{str(e)}}")
            return None

# 使用示例
if __name__ == "__main__":
    client = KeepaHTTPClient()
    result = client.get_price_data("{asin}")
    
    if result:
        print(f"✅ ASIN: {{result['asin']}}")
        print(f"   最高价: ${{result['historical_highest_price']:.2f}}")
        print(f"   最低价: ${{result['historical_lowest_price']:.2f}}")
        if result['current_prices']:
            for source, price in result['current_prices'].items():
                print(f"   {{source}}: ${{price:.2f}}")
    else:
        print("❌ 获取数据失败")
'''
        
        filename = f"keepa_http_implementation_{asin}.py"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(code)
        
        print(f"🔧 HTTP实现代码已生成: {filename}")

def main():
    """主函数"""
    parser = KeepaDataParser()
    
    # 解析WebSocket消息
    message_file = "websocket_messages_B07QNYTRG2.json"
    price_data = parser.parse_websocket_message(message_file)
    
    if price_data:
        # 生成HTTP实现
        parser.generate_http_implementation("B07QNYTRG2", price_data)

if __name__ == "__main__":
    main()
