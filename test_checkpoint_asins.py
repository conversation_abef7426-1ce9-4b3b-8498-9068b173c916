#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试检查点之后的ASIN是否有效
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 历史价格8 import KeepaHistoricalPriceChecker

def load_asins_from_file():
    """从文件加载ASIN列表"""
    asins = []
    try:
        with open('ZYasin.txt', 'r', encoding='utf-8') as f:
            for line in f:
                asin = line.strip()
                if asin and len(asin) == 10:  # ASIN应该是10位
                    asins.append(asin)
    except Exception as e:
        print(f"读取文件错误: {e}")
    
    return asins

def load_checkpoint():
    """加载检查点"""
    try:
        with open('ZYasin_美国_checkpoint.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return None

def get_remaining_asins():
    """获取检查点之后剩余的ASIN"""
    all_asins = load_asins_from_file()
    checkpoint = load_checkpoint()
    
    if not checkpoint:
        print("没有找到检查点文件")
        return all_asins
    
    last_processed = checkpoint.get('last_processed_asin')
    if not last_processed:
        print("检查点中没有最后处理的ASIN")
        return all_asins
    
    print(f"检查点显示最后处理的ASIN: {last_processed}")
    
    # 找到最后处理的ASIN的位置
    try:
        last_index = all_asins.index(last_processed)
        remaining_asins = all_asins[last_index + 1:]
        print(f"总ASIN数: {len(all_asins)}")
        print(f"已处理: {last_index + 1}")
        print(f"剩余: {len(remaining_asins)}")
        return remaining_asins
    except ValueError:
        print(f"在ASIN列表中找不到检查点ASIN: {last_processed}")
        return all_asins

def test_remaining_asins():
    """测试剩余的ASIN"""
    print("🔍 测试检查点之后的ASIN...")
    
    remaining_asins = get_remaining_asins()
    
    if not remaining_asins:
        print("没有剩余的ASIN需要处理")
        return
    
    print(f"\n📋 剩余的前10个ASIN:")
    for i, asin in enumerate(remaining_asins[:10], 1):
        print(f"   {i}. {asin}")
    
    # 测试前5个剩余的ASIN
    analyzer = KeepaHistoricalPriceChecker(verbose=False)
    test_asins = remaining_asins[:5]
    
    success_count = 0
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n📱 测试剩余ASIN {i}: {asin}")
        print("-" * 40)
        
        try:
            # 直接调用WebSocket方法
            result = analyzer.reverse_engineer_keepa(asin)
            
            if result and analyzer.is_valid_websocket_result(result):
                print(f"✅ 成功获取数据:")
                print(f"   最高价: ${result.get('historical_highest_price', 0):.2f}")
                print(f"   最低价: ${result.get('historical_lowest_price', 0):.2f}")
                if result.get('current_price'):
                    print(f"   当前价格: ${result.get('current_price'):.2f}")
                success_count += 1
            else:
                print(f"❌ 无法获取数据")
                print(f"   这个ASIN可能在Keepa上没有价格跟踪数据")
                
        except Exception as e:
            print(f"❌ 异常: {str(e)}")
    
    print(f"\n📋 剩余ASIN测试结果: {success_count}/{len(test_asins)} 成功")
    print(f"成功率: {success_count/len(test_asins)*100:.1f}%")
    
    if success_count == 0:
        print("\n⚠️  所有剩余ASIN都失败了")
        print("💡 这解释了为什么主程序显示所有ASIN都是'未知'")
        print("   - 检查点之前的ASIN可能是有效的")
        print("   - 检查点之后的ASIN可能都没有价格数据")
    else:
        print(f"\n✅ 部分剩余ASIN有效 ({success_count}/{len(test_asins)})")
        print("💡 主程序应该能处理这些有效的ASIN")

def main():
    """主函数"""
    print("🚀 分析检查点之后的ASIN...")
    print("=" * 60)
    
    test_remaining_asins()

if __name__ == "__main__":
    main()
