#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Keepa图像API绕过器
不依赖复杂的图像处理库
"""

import requests
import time
import random

class SimpleKeepaImageBypass:
    """简化的基于图像API的Keepa绕过器"""
    
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.session = requests.Session()
        self.setup_session()
    
    def setup_session(self):
        """设置请求会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://keepa.com/',
            'Sec-Fetch-Dest': 'image',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Site': 'same-origin',
        }
        self.session.headers.update(headers)
    
    def test_keepa_endpoints(self, asin, domain=1):
        """测试各种Keepa端点"""
        if self.verbose:
            print(f"🔍 测试 {asin} 的各种Keepa端点...")
        
        endpoints = [
            # 图像端点
            f"https://graph.keepa.com/pricehistory.png?domain={domain}&asin={asin}",
            f"https://graph.keepa.com/pricehistory.png?domain={domain}&asin={asin}&range=365",
            f"https://graph.keepa.com/pricehistory.png?domain={domain}&asin={asin}&range=180",
            f"https://graph.keepa.com/pricehistory.png?domain={domain}&asin={asin}&range=90",
            f"https://graph.keepa.com/pricehistory.png?domain={domain}&asin={asin}&range=30",
            
            # 可能的API端点
            f"https://api.keepa.com/product?domain={domain}&asin={asin}",
            f"https://keepa.com/api/product?domain={domain}&asin={asin}",
            f"https://keepa.com/product?domain={domain}&asin={asin}",
            
            # 数据端点
            f"https://keepa.com/pricehistory?domain={domain}&asin={asin}",
            f"https://dyn.keepa.com/pricehistory?domain={domain}&asin={asin}",
            
            # 其他可能的端点
            f"https://keepa.com/api/query?domain={domain}&selection=%7B%22asin%22%3A%22{asin}%22%7D",
        ]
        
        successful_endpoints = []
        
        for i, url in enumerate(endpoints, 1):
            try:
                if self.verbose:
                    print(f"📡 [{i}/{len(endpoints)}] 测试: {url}")
                
                response = self.session.get(url, timeout=10)
                
                status = response.status_code
                content_type = response.headers.get('content-type', '未知')
                content_length = len(response.content)
                
                if self.verbose:
                    print(f"   状态码: {status}")
                    print(f"   内容类型: {content_type}")
                    print(f"   内容大小: {content_length} bytes")
                
                if status == 200:
                    if self.verbose:
                        print(f"   ✅ 成功访问!")
                    
                    # 分析响应内容
                    analysis = self.analyze_response(response, asin)
                    
                    successful_endpoints.append({
                        'url': url,
                        'status': status,
                        'content_type': content_type,
                        'content_length': content_length,
                        'analysis': analysis
                    })
                    
                    # 如果找到有用的数据，保存响应
                    if analysis.get('has_data'):
                        filename = f"keepa_response_{asin}_{i}.dat"
                        with open(filename, 'wb') as f:
                            f.write(response.content)
                        if self.verbose:
                            print(f"   💾 响应已保存: {filename}")
                
                elif status == 401:
                    if self.verbose:
                        print(f"   ❌ 401 未授权")
                elif status == 403:
                    if self.verbose:
                        print(f"   ❌ 403 禁止访问")
                elif status == 404:
                    if self.verbose:
                        print(f"   ❌ 404 未找到")
                else:
                    if self.verbose:
                        print(f"   ⚠️ 状态码: {status}")
                
            except requests.exceptions.Timeout:
                if self.verbose:
                    print(f"   ⏰ 请求超时")
            except requests.exceptions.RequestException as e:
                if self.verbose:
                    print(f"   ❌ 请求失败: {str(e)}")
            except Exception as e:
                if self.verbose:
                    print(f"   ❌ 异常: {str(e)}")
            
            # 请求间隔
            time.sleep(random.uniform(1, 2))
        
        return successful_endpoints
    
    def analyze_response(self, response, asin):
        """分析响应内容"""
        try:
            content = response.content
            content_type = response.headers.get('content-type', '').lower()
            
            analysis = {
                'has_data': False,
                'data_type': 'unknown',
                'contains_asin': False,
                'contains_price': False,
                'estimated_prices': []
            }
            
            # 检查是否是图像
            if 'image' in content_type:
                analysis['data_type'] = 'image'
                if len(content) > 1000:  # 有实际内容的图像
                    analysis['has_data'] = True
            
            # 检查是否是JSON
            elif 'json' in content_type:
                analysis['data_type'] = 'json'
                try:
                    text = content.decode('utf-8')
                    if asin in text:
                        analysis['contains_asin'] = True
                        analysis['has_data'] = True
                    
                    # 查找价格信息
                    import re
                    price_patterns = [
                        r'"price":\s*(\d+\.?\d*)',
                        r'"highest":\s*(\d+\.?\d*)',
                        r'"lowest":\s*(\d+\.?\d*)',
                        r'\$(\d+\.?\d*)',
                    ]
                    
                    for pattern in price_patterns:
                        matches = re.findall(pattern, text)
                        for match in matches:
                            try:
                                price = float(match)
                                if 1 <= price <= 2000:
                                    analysis['estimated_prices'].append(price)
                                    analysis['contains_price'] = True
                            except:
                                continue
                    
                except:
                    pass
            
            # 检查是否是HTML
            elif 'html' in content_type:
                analysis['data_type'] = 'html'
                try:
                    text = content.decode('utf-8')
                    if asin in text:
                        analysis['contains_asin'] = True
                        analysis['has_data'] = True
                except:
                    pass
            
            # 检查是否是纯文本
            elif 'text' in content_type:
                analysis['data_type'] = 'text'
                try:
                    text = content.decode('utf-8')
                    if asin in text:
                        analysis['contains_asin'] = True
                        analysis['has_data'] = True
                except:
                    pass
            
            # 其他二进制数据
            else:
                if len(content) > 100:
                    analysis['has_data'] = True
                    analysis['data_type'] = 'binary'
            
            return analysis
            
        except Exception as e:
            return {'has_data': False, 'error': str(e)}
    
    def bypass_keepa_comprehensive(self, asin):
        """综合绕过Keepa"""
        if self.verbose:
            print(f"🚀 启动综合Keepa绕过 - {asin}")
            print("🔍 测试所有可能的端点...")
        
        try:
            # 测试所有端点
            successful_endpoints = self.test_keepa_endpoints(asin)
            
            if not successful_endpoints:
                if self.verbose:
                    print("❌ 没有成功的端点")
                return None
            
            # 分析成功的端点
            if self.verbose:
                print(f"\n📊 成功访问了 {len(successful_endpoints)} 个端点:")
            
            best_result = None
            
            for i, endpoint in enumerate(successful_endpoints, 1):
                url = endpoint['url']
                analysis = endpoint['analysis']
                
                if self.verbose:
                    print(f"\n{i}. {url}")
                    print(f"   数据类型: {analysis.get('data_type', '未知')}")
                    print(f"   包含ASIN: {analysis.get('contains_asin', False)}")
                    print(f"   包含价格: {analysis.get('contains_price', False)}")
                    
                    if analysis.get('estimated_prices'):
                        prices = analysis['estimated_prices']
                        print(f"   估算价格: ${min(prices):.2f} - ${max(prices):.2f}")
                
                # 选择最佳结果
                if analysis.get('contains_price') and analysis.get('estimated_prices'):
                    prices = analysis['estimated_prices']
                    best_result = {
                        'asin': asin,
                        'estimated_highest_price': max(prices),
                        'estimated_lowest_price': min(prices),
                        'price_count': len(prices),
                        'source_url': url,
                        'method': 'endpoint_analysis',
                        'success': True
                    }
                elif analysis.get('has_data') and not best_result:
                    best_result = {
                        'asin': asin,
                        'data_available': True,
                        'source_url': url,
                        'data_type': analysis.get('data_type'),
                        'method': 'endpoint_access',
                        'success': True
                    }
            
            return best_result
            
        except Exception as e:
            if self.verbose:
                print(f"❌ 综合绕过失败: {str(e)}")
            return None

def test_simple_bypass():
    """测试简化绕过"""
    print("🚀 简化Keepa绕过测试")
    print("🔍 全面测试Keepa端点")
    print("=" * 60)
    
    bypasser = SimpleKeepaImageBypass(verbose=True)
    
    test_asins = [
        "B07TZDH6G4",  # 主要测试ASIN
        "B07SPTL99F",  # 备用测试ASIN
    ]
    
    results = []
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n{'='*20} [{i}/{len(test_asins)}] 测试ASIN: {asin} {'='*20}")
        
        start_time = time.time()
        result = bypasser.bypass_keepa_comprehensive(asin)
        elapsed_time = time.time() - start_time
        
        if result and result.get('success'):
            print(f"\n🎉 测试成功!")
            print(f"📊 结果: {result}")
            results.append(result)
        else:
            print(f"\n❌ 测试失败")
        
        print(f"⏱️ 处理时间: {elapsed_time:.1f}s")
        
        # 测试间隔
        if i < len(test_asins):
            print("\n⏳ 等待10秒后继续下一个测试...")
            time.sleep(10)
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 最终测试总结")
    print("=" * 80)
    
    success_count = len(results)
    total_count = len(test_asins)
    success_rate = (success_count / total_count * 100) if total_count > 0 else 0
    
    print(f"📊 总体成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
    
    if results:
        print("\n✅ 成功的结果:")
        for result in results:
            asin = result.get('asin', '未知')
            method = result.get('method', '未知')
            
            if 'estimated_highest_price' in result:
                high = result['estimated_highest_price']
                low = result['estimated_lowest_price']
                count = result.get('price_count', 0)
                print(f"   {asin}: ${low:.2f} - ${high:.2f} ({count} 价格点) [{method}]")
            else:
                data_type = result.get('data_type', '未知')
                print(f"   {asin}: 数据可用 ({data_type}) [{method}]")
    
    if success_rate > 0:
        print("\n🎉 发现了可用的绕过方法!")
        print("💡 建议：基于成功的端点开发更精确的数据提取逻辑")
    else:
        print("\n❌ 所有方法均失败")
        print("💡 建议：考虑使用代理、更换IP或寻找其他数据源")

if __name__ == "__main__":
    try:
        test_simple_bypass()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
