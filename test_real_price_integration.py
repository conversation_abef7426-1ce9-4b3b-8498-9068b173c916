#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实价格集成
验证新的真实价格获取功能是否正常工作
"""

import sys
import time
from 历史价格8 import KeepaHistoricalPriceChecker

def test_real_price_integration():
    """测试真实价格集成功能"""
    print("🚀 真实价格集成测试")
    print("🎯 验证浏览器自动化获取真实Keepa价格")
    print("💡 这将获取真实的波浪线价格数据，而不是估算")
    print("=" * 60)
    
    # 创建价格检查器实例
    checker = KeepaHistoricalPriceChecker(verbose=True)
    
    # 测试ASIN - 我们知道这个有数据
    test_asin = "B07TZDH6G4"
    
    print(f"\n🔍 测试ASIN: {test_asin}")
    print("-" * 40)
    
    start_time = time.time()
    
    # 使用新的真实价格获取方法
    result = checker.get_keepa_image_api_data_optimized(test_asin)
    
    elapsed_time = time.time() - start_time
    
    if result and result.get('success'):
        print(f"\n✅ 成功! (耗时: {elapsed_time:.1f}s)")
        print(f"📊 最高价: ${result['historical_highest_price']:.2f}")
        print(f"📊 最低价: ${result['historical_lowest_price']:.2f}")
        print(f"📊 当前价: ${result['current_price']:.2f}")
        print(f"📊 来源: {result['source']}")
        print(f"📊 时间范围: {result['time_range']}")
        print(f"📊 置信度: {result['confidence']}")
        
        if 'price_points' in result:
            print(f"📊 价格点数: {result['price_points']}")
        
        # 验证价格合理性
        if result['source'] == 'keepa_browser_real_stats':
            print(f"\n🎉 成功获取真实价格数据!")
            print(f"   ✅ 这是从Keepa统计页面提取的真实价格")
            print(f"   ✅ 不是基于图像大小的估算")
            
            # 检查价格是否在合理范围内
            high_price = result['historical_highest_price']
            low_price = result['historical_lowest_price']
            
            if 10 <= low_price <= 100 and 20 <= high_price <= 200:
                print(f"   ✅ 价格范围合理: ${low_price:.2f} - ${high_price:.2f}")
            else:
                print(f"   ⚠️ 价格范围需要验证: ${low_price:.2f} - ${high_price:.2f}")
        else:
            print(f"\n⚠️ 使用了备用方法: {result['source']}")
            print(f"   💡 真实价格获取可能失败，使用了估算方法")
    else:
        print(f"\n❌ 失败 (耗时: {elapsed_time:.1f}s)")
        print(f"   💡 可能需要检查网络连接或Keepa访问")
    
    print(f"\n💡 说明:")
    print(f"   🎯 这个测试验证了真实价格获取功能")
    print(f"   🎯 成功时会显示'keepa_browser_real_stats'来源")
    print(f"   🎯 失败时会自动回退到图像API估算方法")
    print(f"   🎯 真实价格来自Keepa统计页面，不是估算")

def test_main_program_integration():
    """测试主程序集成"""
    print(f"\n" + "=" * 60)
    print("🚀 主程序集成测试")
    print("🎯 验证主程序是否正确使用新的真实价格功能")
    print("-" * 60)
    
    checker = KeepaHistoricalPriceChecker(verbose=True)
    
    test_asin = "B07TZDH6G4"
    
    print(f"\n🔍 通过主程序接口测试: {test_asin}")
    
    start_time = time.time()
    
    # 使用主程序的reverse_engineer_keepa方法
    result = checker.reverse_engineer_keepa(test_asin)
    
    elapsed_time = time.time() - start_time
    
    if result and result.get('success'):
        print(f"\n✅ 主程序集成成功! (耗时: {elapsed_time:.1f}s)")
        print(f"📊 最高价: ${result['historical_highest_price']:.2f}")
        print(f"📊 最低价: ${result['historical_lowest_price']:.2f}")
        print(f"📊 当前价: ${result['current_price']:.2f}")
        print(f"📊 来源: {result['source']}")
        
        if result['source'] == 'keepa_browser_real_stats':
            print(f"\n🎉 主程序成功使用真实价格数据!")
        else:
            print(f"\n⚠️ 主程序使用了备用方法")
    else:
        print(f"\n❌ 主程序集成失败 (耗时: {elapsed_time:.1f}s)")
    
    print(f"\n📊 集成测试完成")
    print(f"   💡 如果看到'keepa_browser_real_stats'来源，说明成功获取真实价格")
    print(f"   💡 如果看到其他来源，说明使用了备用估算方法")

if __name__ == "__main__":
    try:
        test_real_price_integration()
        test_main_program_integration()
    except KeyboardInterrupt:
        print("\n⏹️ 测试中断")
    except Exception as e:
        print(f"\n❌ 异常: {str(e)}")
        import traceback
        traceback.print_exc()
