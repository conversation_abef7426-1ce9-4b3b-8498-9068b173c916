#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序输出格式
验证主程序是否正确显示黄金购买框价格和分类信息
"""

import sys
import time
from 历史价格8 import KeepaHistoricalPriceChecker

def test_main_program_output():
    """测试主程序输出格式"""
    print("🚀 主程序输出格式测试")
    print("🎯 验证是否正确显示黄金购买框价格和分类信息")
    print("🏆 期望看到: '🏆黄金购买框最高价' 而不是 '历史最高价'")
    print("=" * 60)
    
    # 创建价格检查器实例
    checker = KeepaHistoricalPriceChecker(verbose=True)
    
    # 测试ASIN列表
    test_asins = [
        "B07TZDH6G4",  # 我们知道有数据的ASIN
        "B07SPTL99F",  # 另一个测试ASIN
    ]
    
    print(f"\n🔍 测试 {len(test_asins)} 个ASIN的输出格式")
    print("-" * 40)
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n[{i}/{len(test_asins)}] 测试ASIN: {asin}")
        print("-" * 30)
        
        start_time = time.time()
        
        # 调用主程序的处理方法
        result = checker.get_keepa_info_with_browser(asin, max_retries=1)
        
        elapsed_time = time.time() - start_time
        
        print(f"处理耗时: {elapsed_time:.1f}s")
        
        if result and result.get('success'):
            print(f"✅ 处理成功")
            
            # 显示详细信息
            source = result.get('source', 'unknown')
            print(f"📊 数据来源: {source}")
            
            if source == 'keepa_browser_real_stats':
                print(f"🎉 成功使用黄金购买框数据!")
                
                if 'category' in result:
                    print(f"🏷️ 分类: {result['category']}")
                
                if 'last_price_change' in result and result['last_price_change']:
                    change_info = result['last_price_change']
                    print(f"📅 价格变动: {change_info.get('text', '未知')}")
            else:
                print(f"⚠️ 使用了备用方法: {source}")
        else:
            print(f"❌ 处理失败")
        
        # 避免请求过快
        if i < len(test_asins):
            print("⏳ 等待 3 秒...")
            time.sleep(3)
    
    print(f"\n" + "=" * 60)
    print("📊 输出格式说明")
    print("-" * 60)
    print("🏆 成功时应显示:")
    print("   ✅ ASIN: B07TZDH6G4 | 🏆黄金购买框最高价: $59.99 | 当前价: $50.91 | 状态: 缺货 | 价格变动: 3个月前 | 分类: 15天外_缺货")
    print("")
    print("📊 备用方法时显示:")
    print("   ✅ ASIN: B07TZDH6G4 | 历史最高价: $59.99 | 历史最低价: $9.76 | 状态: 有货")
    print("")
    print("💡 关键区别:")
    print("   🏆 黄金购买框: 显示 '🏆黄金购买框最高价' + 分类信息")
    print("   📊 传统方法: 显示 '历史最高价' + '历史最低价'")

def test_batch_processing_output():
    """测试批量处理输出"""
    print(f"\n" + "=" * 60)
    print("🚀 批量处理输出测试")
    print("🎯 模拟主程序批量处理的输出格式")
    print("-" * 60)
    
    checker = KeepaHistoricalPriceChecker(verbose=False)  # 关闭详细输出
    
    test_asins = ["B07TZDH6G4", "B07SPTL99F"]
    
    print(f"\n📋 批量处理 {len(test_asins)} 个ASIN:")
    print("-" * 40)
    
    for asin in test_asins:
        # 直接调用处理方法，模拟主程序的批量处理
        result = checker.get_keepa_info_with_browser(asin, max_retries=1)
        # 输出已经在process_single_asin方法中处理了
        time.sleep(1)  # 短暂延迟
    
    print(f"\n💡 现在的输出应该显示黄金购买框价格和分类信息!")

if __name__ == "__main__":
    try:
        test_main_program_output()
        test_batch_processing_output()
    except KeyboardInterrupt:
        print("\n⏹️ 测试中断")
    except Exception as e:
        print(f"\n❌ 异常: {str(e)}")
        import traceback
        traceback.print_exc()
