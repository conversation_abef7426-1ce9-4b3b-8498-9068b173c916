#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极Keepa 401绕过解决方案
基于诊断结果的多策略综合方案
"""

import time
import random
import json
import requests
import threading
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys

class UltimateKeepaBypass:
    """终极Keepa绕过器 - 多策略综合方案"""
    
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.session = requests.Session()
        self.setup_session()
    
    def setup_session(self):
        """设置请求会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)
    
    def strategy_1_direct_api(self, asin):
        """策略1：直接API请求（模拟WebSocket数据）"""
        if self.verbose:
            print(f"🎯 策略1：直接API请求 - {asin}")
        
        try:
            # 尝试直接访问Keepa的API端点
            api_urls = [
                f"https://api.keepa.com/product?key=&domain=1&asin={asin}",
                f"https://keepa.com/api/product?domain=1&asin={asin}",
                f"https://graph.keepa.com/pricehistory.png?domain=1&asin={asin}",
            ]
            
            for url in api_urls:
                try:
                    response = self.session.get(url, timeout=10)
                    if response.status_code == 200:
                        if self.verbose:
                            print(f"✅ API请求成功: {url}")
                        return self.parse_api_response(response, asin)
                except:
                    continue
            
            return None
            
        except Exception as e:
            if self.verbose:
                print(f"❌ 策略1失败: {str(e)}")
            return None
    
    def strategy_2_browser_interaction(self, asin):
        """策略2：真实浏览器交互"""
        if self.verbose:
            print(f"🎯 策略2：真实浏览器交互 - {asin}")
        
        driver = None
        try:
            # 创建最真实的浏览器环境
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            # 不添加任何反检测参数，使用最原始的浏览器
            driver = webdriver.Chrome(options=options)
            driver.set_page_load_timeout(30)
            
            # 模拟真实用户行为流程
            if self.verbose:
                print("👤 模拟真实用户浏览流程...")
            
            # 第一步：访问Google（建立真实浏览历史）
            driver.get("https://www.google.com")
            time.sleep(random.uniform(2, 4))
            
            # 第二步：搜索Keepa
            search_box = driver.find_element("name", "q")
            search_box.send_keys("keepa amazon price tracker")
            search_box.send_keys(Keys.RETURN)
            time.sleep(random.uniform(3, 5))
            
            # 第三步：点击Keepa链接
            try:
                keepa_link = driver.find_element("partial link text", "keepa.com")
                keepa_link.click()
            except:
                driver.get("https://keepa.com")
            
            time.sleep(random.uniform(3, 6))
            
            # 第四步：模拟浏览主页
            self.simulate_real_browsing(driver)
            
            # 第五步：搜索产品
            if self.verbose:
                print(f"🔍 搜索产品: {asin}")
            
            try:
                # 尝试找到搜索框
                search_elements = [
                    ("id", "search"),
                    ("name", "search"),
                    ("class name", "search"),
                    ("css selector", "input[type='search']"),
                    ("css selector", "input[placeholder*='search']"),
                ]
                
                search_box = None
                for method, selector in search_elements:
                    try:
                        search_box = driver.find_element(method, selector)
                        break
                    except:
                        continue
                
                if search_box:
                    search_box.clear()
                    search_box.send_keys(asin)
                    search_box.send_keys(Keys.RETURN)
                    time.sleep(random.uniform(5, 8))
                else:
                    # 直接访问产品页面
                    product_url = f"https://keepa.com/#!product/1-{asin}"
                    driver.get(product_url)
                    time.sleep(random.uniform(8, 12))
                
            except Exception as e:
                if self.verbose:
                    print(f"⚠️ 搜索失败，直接访问产品页面: {str(e)}")
                product_url = f"https://keepa.com/#!product/1-{asin}"
                driver.get(product_url)
                time.sleep(random.uniform(8, 12))
            
            # 第六步：模拟用户在产品页面的行为
            self.simulate_product_page_interaction(driver)
            
            # 第七步：尝试提取数据
            return self.extract_data_from_page(driver, asin)
            
        except Exception as e:
            if self.verbose:
                print(f"❌ 策略2失败: {str(e)}")
            return None
            
        finally:
            if driver:
                try:
                    driver.quit()
                except:
                    pass
    
    def simulate_real_browsing(self, driver):
        """模拟真实浏览行为"""
        try:
            # 随机滚动
            for _ in range(random.randint(2, 4)):
                scroll_amount = random.randint(200, 800)
                driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(1, 3))
            
            # 随机鼠标移动
            actions = ActionChains(driver)
            for _ in range(random.randint(3, 6)):
                x_offset = random.randint(-100, 100)
                y_offset = random.randint(-100, 100)
                actions.move_by_offset(x_offset, y_offset)
                time.sleep(random.uniform(0.5, 1.5))
            actions.perform()
            
            # 回到顶部
            driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(random.uniform(1, 2))
            
        except Exception as e:
            if self.verbose:
                print(f"⚠️ 浏览行为模拟异常: {str(e)}")
    
    def simulate_product_page_interaction(self, driver):
        """模拟产品页面交互"""
        try:
            if self.verbose:
                print("🤖 模拟产品页面交互...")
            
            # 等待页面加载
            time.sleep(random.uniform(3, 5))
            
            # 滚动查看图表
            driver.execute_script("window.scrollBy(0, 300);")
            time.sleep(random.uniform(2, 4))
            
            # 尝试点击图表区域（如果存在）
            try:
                chart_elements = driver.find_elements("css selector", "canvas, svg, .chart, .graph")
                if chart_elements:
                    chart = chart_elements[0]
                    ActionChains(driver).move_to_element(chart).click().perform()
                    time.sleep(random.uniform(1, 3))
            except:
                pass
            
            # 尝试切换时间范围（如果存在）
            try:
                time_buttons = driver.find_elements("css selector", "button, .btn, .time-range")
                if time_buttons:
                    for button in time_buttons[:2]:  # 点击前两个按钮
                        try:
                            button.click()
                            time.sleep(random.uniform(2, 4))
                        except:
                            continue
            except:
                pass
            
            # 最后滚动到底部再回到顶部
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(random.uniform(2, 3))
            driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(random.uniform(1, 2))
            
        except Exception as e:
            if self.verbose:
                print(f"⚠️ 产品页面交互异常: {str(e)}")
    
    def extract_data_from_page(self, driver, asin):
        """从页面提取数据"""
        try:
            if self.verbose:
                print("📊 尝试从页面提取数据...")
            
            # 方法1：尝试从页面源码提取
            page_source = driver.page_source
            
            # 查找价格信息
            price_patterns = [
                r'\$(\d+\.?\d*)',
                r'price["\']:\s*(\d+\.?\d*)',
                r'highest["\']:\s*(\d+\.?\d*)',
                r'lowest["\']:\s*(\d+\.?\d*)',
            ]
            
            prices = []
            for pattern in price_patterns:
                import re
                matches = re.findall(pattern, page_source)
                for match in matches:
                    try:
                        price = float(match)
                        if 1 <= price <= 2000:  # 合理价格范围
                            prices.append(price)
                    except:
                        continue
            
            if prices:
                highest = max(prices)
                lowest = min(prices)
                
                if self.verbose:
                    print(f"✅ 从页面提取到价格数据:")
                    print(f"   最高价: ${highest:.2f}")
                    print(f"   最低价: ${lowest:.2f}")
                
                return {
                    'asin': asin,
                    'highest_price': highest,
                    'lowest_price': lowest,
                    'method': 'page_extraction',
                    'success': True
                }
            
            # 方法2：尝试从页面标题提取产品信息
            title = driver.title
            if asin in title and '$' in title:
                if self.verbose:
                    print(f"✅ 从标题提取到产品信息: {title}")
                
                return {
                    'asin': asin,
                    'title': title,
                    'method': 'title_extraction',
                    'success': True
                }
            
            return None
            
        except Exception as e:
            if self.verbose:
                print(f"❌ 数据提取失败: {str(e)}")
            return None
    
    def strategy_3_alternative_sources(self, asin):
        """策略3：替代数据源"""
        if self.verbose:
            print(f"🎯 策略3：替代数据源 - {asin}")
        
        try:
            # 尝试其他价格追踪网站
            alternative_sources = [
                f"https://camelcamelcamel.com/product/{asin}",
                f"https://www.amazon.com/dp/{asin}",
            ]
            
            for url in alternative_sources:
                try:
                    response = self.session.get(url, timeout=10)
                    if response.status_code == 200:
                        if self.verbose:
                            print(f"✅ 替代源访问成功: {url}")
                        # 这里可以添加替代源的数据解析逻辑
                        return {'asin': asin, 'source': url, 'method': 'alternative'}
                except:
                    continue
            
            return None
            
        except Exception as e:
            if self.verbose:
                print(f"❌ 策略3失败: {str(e)}")
            return None
    
    def parse_api_response(self, response, asin):
        """解析API响应"""
        try:
            # 尝试JSON解析
            try:
                data = response.json()
                if 'products' in data:
                    # 处理Keepa API格式
                    return {'asin': asin, 'data': data, 'method': 'api', 'success': True}
            except:
                pass
            
            # 尝试文本解析
            text = response.text
            if asin in text:
                return {'asin': asin, 'raw_data': text[:500], 'method': 'api_text', 'success': True}
            
            return None
            
        except Exception as e:
            if self.verbose:
                print(f"❌ API响应解析失败: {str(e)}")
            return None
    
    def bypass_keepa(self, asin, max_strategies=3):
        """综合绕过方法"""
        if self.verbose:
            print(f"🚀 启动终极Keepa绕过 - {asin}")
            print("🎯 使用多策略综合方案")
        
        strategies = [
            self.strategy_1_direct_api,
            self.strategy_2_browser_interaction,
            self.strategy_3_alternative_sources,
        ]
        
        for i, strategy in enumerate(strategies[:max_strategies], 1):
            if self.verbose:
                print(f"\n📍 执行策略 {i}/{max_strategies}")
            
            try:
                result = strategy(asin)
                if result and result.get('success'):
                    if self.verbose:
                        print(f"✅ 策略 {i} 成功!")
                    return result
                else:
                    if self.verbose:
                        print(f"❌ 策略 {i} 失败")
            except Exception as e:
                if self.verbose:
                    print(f"❌ 策略 {i} 异常: {str(e)}")
            
            # 策略间等待
            if i < max_strategies:
                wait_time = random.uniform(5, 10)
                if self.verbose:
                    print(f"⏳ 等待 {wait_time:.1f}s 后尝试下一策略...")
                time.sleep(wait_time)
        
        if self.verbose:
            print(f"❌ 所有策略均失败: {asin}")
        return None

def test_ultimate_solution():
    """测试终极解决方案"""
    print("🚀 终极Keepa 401绕过解决方案测试")
    print("🎯 多策略综合方案")
    print("=" * 60)
    
    bypasser = UltimateKeepaBypass(verbose=True)
    
    test_asins = [
        "B07TZDH6G4",  # 主要测试ASIN
    ]
    
    for i, asin in enumerate(test_asins, 1):
        print(f"\n[{i}/{len(test_asins)}] 测试ASIN: {asin}")
        print("-" * 50)
        
        start_time = time.time()
        result = bypasser.bypass_keepa(asin)
        elapsed_time = time.time() - start_time
        
        if result and result.get('success'):
            print(f"\n🎉 测试成功!")
            print(f"📊 结果: {result}")
            print(f"⏱️ 总耗时: {elapsed_time:.1f}s")
        else:
            print(f"\n❌ 测试失败")
            print(f"⏱️ 总耗时: {elapsed_time:.1f}s")

if __name__ == "__main__":
    try:
        test_ultimate_solution()
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
